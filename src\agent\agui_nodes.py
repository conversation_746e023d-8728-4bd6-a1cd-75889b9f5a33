"""
AG-UI Compatible LangGraph Nodes
Enhanced versions of existing nodes that emit AG-UI events
"""

from typing import Dict, Any, List, Optional
import json
import traceback

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_community.chat_models import ChatZhipuAI

from src.agent.state import ItineraryState
from src.ag_ui.stream_manager import AGUIStreamManager
from src.ag_ui.langgraph_integration import LangGraphAGUIIntegration
from src.ag_ui.tool_wrapper import (
    wrap_train_search_tool, wrap_flight_search_tool, 
    wrap_weather_tool, wrap_gaode_tools
)

# Global stream manager - will be set when creating the graph
_global_stream_manager: Optional[AGUIStreamManager] = None
_global_integration: Optional[LangGraphAGUIIntegration] = None


def set_agui_context(stream_manager: AGUIStreamManager):
    """Set the global AG-UI context for nodes"""
    global _global_stream_manager, _global_integration
    _global_stream_manager = stream_manager
    _global_integration = LangGraphAGUIIntegration(stream_manager)


def get_agui_integration() -> Optional[LangGraphAGUIIntegration]:
    """Get the current AG-UI integration"""
    return _global_integration


def agui_planner_node(state: ItineraryState) -> ItineraryState:
    """
    AG-UI compatible planner node
    Enhanced version of the original planner_node with AG-UI event emission
    """
    if not _global_integration:
        # Fallback to original implementation
        from src.agent.nodes import planner_node
        return planner_node(state)
    
    integration = _global_integration
    
    # Emit step started
    integration.stream_manager.start_step("planner")
    
    try:
        # Import original implementation
        from src.agent.nodes import planner_node
        
        # Add thinking process tracking
        thoughts = []
        
        # Execute original logic with thought tracking
        thoughts.append("开始分析用户的旅行需求...")
        
        # Call original function
        result = planner_node(state)
        
        # Add thoughts to result
        if "thinking_process" not in result:
            result["thinking_process"] = []
        result["thinking_process"].extend(thoughts)
        
        # Emit thinking process
        if thoughts:
            integration._emit_thinking_process("规划师", thoughts)
        
        # Emit state delta
        integration._emit_state_delta(state, result)
        
        # Emit step finished
        integration.stream_manager.finish_step("planner")
        
        return result
        
    except Exception as e:
        integration.stream_manager.error_run(f"规划师节点执行失败: {str(e)}")
        raise


def agui_major_transport_node(state: ItineraryState) -> ItineraryState:
    """
    AG-UI compatible major transport node
    Enhanced version with tool call events
    """
    if not _global_integration:
        from src.agent.nodes import major_transport_node
        return major_transport_node(state)
    
    integration = _global_integration
    
    # Emit step started
    integration.stream_manager.start_step("major_transport")
    
    try:
        thoughts = []
        thoughts.append("开始规划城市间大交通方案...")
        
        # Wrap tools with AG-UI events
        wrapped_train_search = wrap_train_search_tool(integration.stream_manager)
        wrapped_flight_search = wrap_flight_search_tool(integration.stream_manager)
        
        # Get departure and arrival cities
        departure_city = state.get("departure_city")
        arrival_city = state.get("arrival_city")
        
        if not departure_city or not arrival_city:
            thoughts.append("缺少出发城市或目的地城市信息，需要先完成地址解析")
            # Emit thinking and return
            integration._emit_thinking_process("大交通专家", thoughts)
            state["thinking_process"] = thoughts
            state["next_step"] = "planner"
            integration.stream_manager.finish_step("major_transport")
            return state
        
        thoughts.append(f"正在查询从{departure_city}到{arrival_city}的交通方案...")
        
        # Emit current thoughts
        integration._emit_thinking_process("大交通专家", thoughts)
        
        # Call wrapped train search tool
        train_results = []
        for event in wrapped_train_search(departure_city, arrival_city):
            # Event is already emitted by the wrapper
            pass
        
        # Get the actual result (last item from generator)
        try:
            from src.tools.train_search_tool import search_train
            train_results = search_train(departure_city, arrival_city)
        except Exception as e:
            thoughts.append(f"火车查询失败: {str(e)}")
        
        # Process results
        if train_results:
            thoughts.append(f"找到{len(train_results)}个火车/高铁方案")
            state["major_transport_options"] = train_results
            
            # Select best option (simplified logic)
            if train_results:
                best_option = train_results[0]  # Take first option
                state["selected_major_transport"] = best_option
                thoughts.append(f"推荐方案: {best_option.get('train_number', 'N/A')} - {best_option.get('departure_time', 'N/A')}")
        else:
            thoughts.append("未找到合适的大交通方案")
        
        # Update state
        state["thinking_process"] = thoughts
        state["current_step"] = "major_transport"
        state["next_step"] = "local_transport" if state.get("selected_major_transport") else "planner"
        
        # Emit final thoughts
        integration._emit_thinking_process("大交通专家", thoughts[-2:])  # Last few thoughts
        
        # Emit state delta
        integration._emit_state_delta({}, state)
        
        # Emit step finished
        integration.stream_manager.finish_step("major_transport")
        
        return state
        
    except Exception as e:
        integration.stream_manager.error_run(f"大交通专家节点执行失败: {str(e)}")
        raise


def agui_local_transport_node(state: ItineraryState) -> ItineraryState:
    """
    AG-UI compatible local transport node
    Enhanced version with tool call events
    """
    if not _global_integration:
        from src.agent.nodes import local_transport_node
        return local_transport_node(state)
    
    integration = _global_integration
    
    # Emit step started
    integration.stream_manager.start_step("local_transport")
    
    try:
        thoughts = []
        thoughts.append("开始规划小交通方案...")
        
        # Wrap Gaode tools with AG-UI events
        gaode_tools = wrap_gaode_tools(integration.stream_manager)
        
        # Get coordinates and station info
        departure_coords = state.get("departure_coordinates")
        arrival_coords = state.get("arrival_coordinates")
        selected_transport = state.get("selected_major_transport")
        
        if not departure_coords or not arrival_coords:
            thoughts.append("缺少坐标信息，无法规划小交通")
            integration._emit_thinking_process("小交通专家", thoughts)
            state["thinking_process"] = thoughts
            state["next_step"] = "planner"
            integration.stream_manager.finish_step("local_transport")
            return state
        
        thoughts.append("正在查询车站信息和驾车路线...")
        integration._emit_thinking_process("小交通专家", thoughts)
        
        # Get station information using wrapped tools
        departure_city = state.get("departure_city")
        arrival_city = state.get("arrival_city")
        
        if departure_city and arrival_city:
            # Call wrapped tools
            for event in gaode_tools["get_railway_station"](departure_city):
                pass  # Events already emitted
            
            for event in gaode_tools["get_railway_station"](arrival_city):
                pass  # Events already emitted
        
        # Simplified local transport planning
        local_transport_options = {
            "departure": {
                "route_type": "driving",
                "estimated_time": "30分钟",
                "distance": "15公里"
            },
            "arrival": {
                "route_type": "driving", 
                "estimated_time": "25分钟",
                "distance": "12公里"
            }
        }
        
        state["local_transport_options"] = local_transport_options
        thoughts.append("小交通方案规划完成")
        
        # Update state
        state["thinking_process"] = thoughts
        state["current_step"] = "local_transport"
        state["next_step"] = "synthesis"
        
        # Emit final thoughts
        integration._emit_thinking_process("小交通专家", thoughts[-1:])
        
        # Emit state delta
        integration._emit_state_delta({}, state)
        
        # Emit step finished
        integration.stream_manager.finish_step("local_transport")
        
        return state
        
    except Exception as e:
        integration.stream_manager.error_run(f"小交通专家节点执行失败: {str(e)}")
        raise


def agui_synthesis_node(state: ItineraryState) -> ItineraryState:
    """
    AG-UI compatible synthesis node
    Enhanced version that generates final plan
    """
    if not _global_integration:
        from src.agent.nodes import synthesis_node
        return synthesis_node(state)
    
    integration = _global_integration
    
    # Emit step started
    integration.stream_manager.start_step("synthesis")
    
    try:
        thoughts = []
        thoughts.append("开始综合所有信息生成最终行程规划...")
        
        # Emit initial thoughts
        integration._emit_thinking_process("综合报告专家", thoughts)
        
        # Generate final plan
        final_plan = {
            "departure_info": {
                "city": state.get("departure_city"),
                "coordinates": state.get("departure_coordinates")
            },
            "arrival_info": {
                "city": state.get("arrival_city"),
                "coordinates": state.get("arrival_coordinates")
            },
            "major_transport": state.get("selected_major_transport"),
            "local_transport": state.get("local_transport_options"),
            "timeline": {
                "total_duration": "约4小时",
                "departure_time": "建议8:00出发",
                "arrival_time": "预计12:00到达"
            }
        }
        
        state["final_plan"] = final_plan
        thoughts.append("最终行程规划生成完成")
        
        # Update state
        state["thinking_process"] = thoughts
        state["current_step"] = "synthesis"
        state["next_step"] = "end"
        
        # Emit final thoughts
        integration._emit_thinking_process("综合报告专家", thoughts[-1:])
        
        # Emit state delta
        integration._emit_state_delta({}, state)
        
        # Emit step finished
        integration.stream_manager.finish_step("synthesis")
        
        return state
        
    except Exception as e:
        integration.stream_manager.error_run(f"综合报告专家节点执行失败: {str(e)}")
        raise


# Node mapping for AG-UI compatible graph
AGUI_NODE_MAPPING = {
    "planner": agui_planner_node,
    "major_transport": agui_major_transport_node,
    "local_transport": agui_local_transport_node,
    "synthesis": agui_synthesis_node,
}
