# main_cli_langgraph.py
# 基于LangGraph工作流的命令行界面入口

import os
from dotenv import load_dotenv
import sys
import json
from typing import Dict, Any, List
from datetime import datetime

# 确保这行在最前面，以加载环境变量
load_dotenv()

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.messages import HumanMessage, AIMessage
from src.agent.graph import itinerary_app

def display_banner():
    """显示程序启动横幅"""
    print("\n" + "=" * 70)
    print("🧠 智能旅行规划助手 (LangGraph多智能体版本)")
    print("=" * 70)
    print("支持功能:")
    print("1. 📝 自然语言行程查询和规划")
    print("2. 🚄 高铁/火车交通查询")
    print("3. 🚗 本地驾车路线规划")
    print("4. 🌦️ 天气信息集成")
    print("5. 🗺️ 完整行程时间线生成")
    print("")
    print("示例查询:")
    print("- '帮我规划从北京到上海的行程，明天出发'")
    print("- '周五从成都到重庆的旅行计划，我想去解放碑'")
    print("- '下周一从广州塔到深圳湾公园的最佳路线'")
    print("")
    print("输入 'exit' 或 'quit' 退出系统")
    print("=" * 70 + "\n")

def format_thinking_process(thinking: List[str]) -> str:
    """格式化思考过程，使其更易读"""
    formatted = "\n🧠 思考过程:\n"
    for i, thought in enumerate(thinking, 1):
        formatted += f"{i}. {thought}\n"
    return formatted

def format_plan(plan: Dict[str, Any]) -> str:
    """格式化行程计划，使其在命令行中可读性更好"""
    if not plan:
        return "未能生成有效的行程计划。"
    
    formatted = "\n📋 行程规划结果:\n"
    formatted += f"🔹 {plan.get('title', '行程规划')}\n"
    formatted += f"🔹 {plan.get('overview', '')}\n\n"
    
    # 时间轴信息
    timeline = plan.get("timeline", {})
    if timeline:
        formatted += "⏱️ 时间安排:\n"
        
        # 显示出发日期
        if timeline.get("departure_date_str"):
            formatted += f"📅 {timeline.get('departure_date_str')}\n"
        
        # 完整时间线
        if timeline.get("departure_from_origin"):
            formatted += f"  {timeline.get('departure_from_origin')} - 从出发地点出发\n"
        if timeline.get("arrival_at_station"):
            formatted += f"  {timeline.get('arrival_at_station')} - 到达{plan.get('transportation', {}).get('major', {}).get('departure', {}).get('station', '出发站')}\n"
        if timeline.get("train_departure"):
            formatted += f"  {timeline.get('train_departure')} - {plan.get('transportation', {}).get('major', {}).get('code', '列车')}出发\n"
        
        # 如果是跨天行程，显示到达日期
        if timeline.get("is_multi_day") and timeline.get("arrival_date_str"):
            formatted += f"\n📅 {timeline.get('arrival_date_str')}\n"
        
        if timeline.get("train_arrival"):
            formatted += f"  {timeline.get('train_arrival')} - 列车到达{plan.get('transportation', {}).get('major', {}).get('arrival', {}).get('station', '到达站')}\n"
        if timeline.get("arrival_at_destination"):
            formatted += f"  {timeline.get('arrival_at_destination')} - 到达最终目的地\n"
        
        formatted += "\n"
    
    # 交通信息
    transportation = plan.get("transportation", {})
    major = transportation.get("major", {})
    local = transportation.get("local", {})
    
    if major:
        formatted += "🚄 主要交通:\n"
        formatted += f"  车次: {major.get('code', '未知')}\n"
        formatted += f"  出发: {major.get('departure', {}).get('station', '未知')} {major.get('departure', {}).get('time', '未知')}\n"
        formatted += f"  到达: {major.get('arrival', {}).get('station', '未知')} {major.get('arrival', {}).get('time', '未知')}\n"
        formatted += f"  历时: {major.get('duration', '未知')}\n"
        formatted += f"  座位: {major.get('seat', {}).get('type', '未知')} {major.get('seat', {}).get('price', '未知')}元\n\n"
    
    if local:
        formatted += "🚗 本地交通:\n"
        dep_driving = local.get("departure", {})
        if dep_driving:
            formatted += f"  从出发地到火车站: {dep_driving.get('distance', '未知')}, {dep_driving.get('duration', '未知')}\n"
        
        arr_driving = local.get("arrival", {})
        if arr_driving:
            formatted += f"  从火车站到目的地: {arr_driving.get('distance', '未知')}, {arr_driving.get('duration', '未知')}\n\n"
    
    # 提示信息
    tips = plan.get("tips", [])
    if tips:
        formatted += "💡 出行提示:\n"
        for tip in tips:
            formatted += f"  • {tip}\n"
    
    return formatted

def run_agent():
    """运行基于LangGraph的智能体"""
    display_banner()
    
    # 存储整个对话历史
    conversation_history = []
    # 存储运行时状态
    runtime_state = {}
    
    while True:
        # 获取用户输入
        user_input = input("您: ")
        if user_input.lower() in ['exit', 'quit', '退出']:
            print("再见！")
            break
        
        # 记录用户输入到对话历史
        conversation_history.append(HumanMessage(content=user_input))
        
        # 初始化状态
        state = {
            "messages": conversation_history.copy(),
            "original_query": user_input,
            "execution_history": [],
            "thinking_process": []
        }
        
        # 如果有之前的运行时状态，合并到新状态中
        for key, value in runtime_state.items():
            if key not in state:
                state[key] = value
        
        print("助手: 正在为您规划行程，请稍候...\n")
        
        # 运行LangGraph工作流
        try:
            # 是否需要暂停工作流进行用户交互
            paused_for_user_input = False
            
            # 第一次运行工作流
            result = itinerary_app.invoke(state)
            
            # 检查是否需要用户澄清
            while result.get("is_clarification_needed", False):
                paused_for_user_input = True
                question = result.get("clarification_question", "需要您提供更多信息...")
                print(f"\n❓ {question}")
                clarification = input("您的回复: ")
                
                # 更新状态并继续运行
                result["user_clarification_response"] = clarification
                # 添加到对话历史
                conversation_history.append(HumanMessage(content=clarification))
                result["messages"] = conversation_history.copy()
                
                # 再次运行工作流
                result = itinerary_app.invoke(result)
            
            # 提取最终结果
            final_plan = result.get("final_plan", {})
            thinking_process = result.get("thinking_process", [])
            
            # 更新运行时状态，保留关键信息用于下一次交互
            runtime_state = {
                "departure_city": result.get("departure_city"),
                "arrival_city": result.get("arrival_city"),
                "departure_place": result.get("departure_place"),
                "arrival_place": result.get("arrival_place"),
                "travel_date": result.get("travel_date"),
                "major_transport_options": result.get("major_transport_options"),
                "selected_major_transport": result.get("selected_major_transport"),
                "departure_station": result.get("departure_station"),
                "arrival_station": result.get("arrival_station")
            }
            
            # 显示思考过程
            if thinking_process:
                print(format_thinking_process(thinking_process))
            
            # 显示行程规划结果
            if final_plan:
                print(format_plan(final_plan))
            else:
                print("很抱歉，我无法完成行程规划。请提供更明确的起始地点和日期信息。")
            
            # 如果之前已暂停交互，就不添加额外的AI消息
            if not paused_for_user_input:
                # 生成总结性回复
                summary = f"我已为您规划了从{final_plan.get('departure_place', '出发地')}到{final_plan.get('arrival_place', '目的地')}的行程。"
                if final_plan.get("transportation", {}).get("major", {}).get("code"):
                    train_code = final_plan.get("transportation", {}).get("major", {}).get("code")
                    summary += f"您将乘坐{train_code}次列车。"
                if final_plan.get("timeline", {}).get("departure_from_origin"):
                    departure_time = final_plan.get("timeline", {}).get("departure_from_origin")
                    summary += f"建议您{departure_time}从出发地出发。"
                
                # 添加AI回复到对话历史
                conversation_history.append(AIMessage(content=summary))
            
        except Exception as e:
            print(f"处理过程中出现错误: {str(e)}")
            print("请尝试提供更完整的信息，包括具体的出发地、目的地和日期。")
        
        print("\n" + "-" * 70)

if __name__ == "__main__":
    run_agent() 