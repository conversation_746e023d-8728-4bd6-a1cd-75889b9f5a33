"""
AG-UI Protocol Stream Manager
Manages event streams and provides utilities for LangGraph integration
"""

from typing import Generator, Dict, Any, Optional, Callable, AsyncGenerator
import asyncio
import threading
import queue
import time
from contextlib import asynccontextmanager

from .events import (
    AGUIEvent, EventType, 
    RunStartedEvent, RunFinishedEvent, RunErrorEvent,
    StepStartedEvent, StepFinishedEvent,
    TextMessageStartEvent, TextMessageContentEvent, TextMessageEndEvent,
    ToolCallStartEvent, ToolCallArgsEvent, ToolCallEndEvent, ToolCallResultEvent,
    StateSnapshotEvent, StateDeltaEvent, MessagesSnapshotEvent,
    generate_id, generate_message_id, generate_tool_call_id, chunk_string
)
from .encoder import EventStream


class AGUIStreamManager:
    """Manages AG-UI event streams for LangGraph integration"""
    
    def __init__(self, thread_id: str = None, run_id: str = None):
        """
        Initialize the stream manager
        
        Args:
            thread_id: Thread ID for the conversation
            run_id: Run ID for this execution
        """
        self.thread_id = thread_id or generate_id()
        self.run_id = run_id or generate_id()
        self.stream = EventStream()
        self._current_step = None
        self._current_message_id = None
        self._current_tool_call_id = None
        self._state_history = []
        
    def start_run(self) -> str:
        """Start a new run and emit RUN_STARTED event"""
        event = RunStartedEvent(
            thread_id=self.thread_id,
            run_id=self.run_id
        )
        return self.stream.emit(event)
    
    def finish_run(self, result: Any = None) -> str:
        """Finish the run and emit RUN_FINISHED event"""
        event = RunFinishedEvent(
            thread_id=self.thread_id,
            run_id=self.run_id,
            result=result
        )
        return self.stream.emit(event)
    
    def error_run(self, message: str, code: str = None) -> str:
        """Emit RUN_ERROR event"""
        event = RunErrorEvent(
            message=message,
            code=code
        )
        return self.stream.emit(event)
    
    def start_step(self, step_name: str) -> str:
        """Start a new step and emit STEP_STARTED event"""
        self._current_step = step_name
        event = StepStartedEvent(step_name=step_name)
        return self.stream.emit(event)
    
    def finish_step(self, step_name: str = None) -> str:
        """Finish the current step and emit STEP_FINISHED event"""
        step_name = step_name or self._current_step
        event = StepFinishedEvent(step_name=step_name)
        return self.stream.emit(event)
    
    def start_message(self, role: str = "assistant") -> str:
        """Start a new text message"""
        self._current_message_id = generate_message_id()
        event = TextMessageStartEvent(
            message_id=self._current_message_id,
            role=role
        )
        return self.stream.emit(event)
    
    def add_message_content(self, content: str, message_id: str = None) -> str:
        """Add content to the current message"""
        message_id = message_id or self._current_message_id
        if not message_id:
            raise ValueError("No active message. Call start_message() first.")
        
        event = TextMessageContentEvent(
            message_id=message_id,
            delta=content
        )
        return self.stream.emit(event)
    
    def end_message(self, message_id: str = None) -> str:
        """End the current message"""
        message_id = message_id or self._current_message_id
        if not message_id:
            raise ValueError("No active message to end.")
        
        event = TextMessageEndEvent(message_id=message_id)
        result = self.stream.emit(event)
        self._current_message_id = None
        return result
    
    def stream_text(self, text: str, chunk_size: int = 50, delay: float = 0.01) -> Generator[str, None, None]:
        """Stream text content in chunks"""
        message_id = self.start_message()
        
        chunks = chunk_string(text, chunk_size)
        for chunk in chunks:
            yield self.add_message_content(chunk)
            if delay > 0:
                time.sleep(delay)
        
        yield self.end_message()
    
    def start_tool_call(self, tool_name: str, parent_message_id: str = None) -> str:
        """Start a tool call"""
        self._current_tool_call_id = generate_tool_call_id()
        event = ToolCallStartEvent(
            tool_call_id=self._current_tool_call_id,
            tool_call_name=tool_name,
            parent_message_id=parent_message_id
        )
        return self.stream.emit(event)
    
    def add_tool_args(self, args_chunk: str, tool_call_id: str = None) -> str:
        """Add arguments to the current tool call"""
        tool_call_id = tool_call_id or self._current_tool_call_id
        if not tool_call_id:
            raise ValueError("No active tool call. Call start_tool_call() first.")
        
        event = ToolCallArgsEvent(
            tool_call_id=tool_call_id,
            delta=args_chunk
        )
        return self.stream.emit(event)
    
    def end_tool_call(self, tool_call_id: str = None) -> str:
        """End the current tool call"""
        tool_call_id = tool_call_id or self._current_tool_call_id
        if not tool_call_id:
            raise ValueError("No active tool call to end.")
        
        event = ToolCallEndEvent(tool_call_id=tool_call_id)
        result = self.stream.emit(event)
        self._current_tool_call_id = None
        return result
    
    def add_tool_result(self, result: str, tool_call_id: str, message_id: str = None) -> str:
        """Add tool call result"""
        event = ToolCallResultEvent(
            message_id=message_id or generate_message_id(),
            tool_call_id=tool_call_id,
            content=result
        )
        return self.stream.emit(event)
    
    def update_state(self, new_state: Dict[str, Any], emit_delta: bool = True) -> Optional[str]:
        """Update state and optionally emit delta"""
        if not emit_delta:
            self._state_history.append(new_state.copy())
            return None
        
        old_state = self._state_history[-1] if self._state_history else {}
        
        # Create delta patches
        from .events import create_json_patch
        patches = create_json_patch(old_state, new_state)
        
        if patches:
            event = StateDeltaEvent(delta=patches)
            result = self.stream.emit(event)
            self._state_history.append(new_state.copy())
            return result
        
        return None
    
    def snapshot_state(self, state: Dict[str, Any]) -> str:
        """Emit state snapshot"""
        event = StateSnapshotEvent(snapshot=state)
        result = self.stream.emit(event)
        self._state_history.append(state.copy())
        return result
    
    def snapshot_messages(self, messages: list) -> str:
        """Emit messages snapshot"""
        event = MessagesSnapshotEvent(messages=messages)
        return self.stream.emit(event)
    
    def get_encoded_events(self) -> list:
        """Get all encoded events"""
        return [self.stream.encoder.encode(event) for event in self.stream.get_events()]
    
    def clear(self):
        """Clear all events and reset state"""
        self.stream.clear()
        self._state_history.clear()
        self._current_step = None
        self._current_message_id = None
        self._current_tool_call_id = None


class AsyncAGUIStreamManager(AGUIStreamManager):
    """Async version of AGUIStreamManager for async contexts"""
    
    async def stream_text_async(self, text: str, chunk_size: int = 50, delay: float = 0.01) -> AsyncGenerator[str, None]:
        """Async version of stream_text"""
        message_id = self.start_message()
        
        chunks = chunk_string(text, chunk_size)
        for chunk in chunks:
            yield self.add_message_content(chunk)
            if delay > 0:
                await asyncio.sleep(delay)
        
        yield self.end_message()


def create_stream_manager(thread_id: str = None, run_id: str = None) -> AGUIStreamManager:
    """
    Create a new stream manager
    
    Args:
        thread_id: Thread ID for the conversation
        run_id: Run ID for this execution
        
    Returns:
        New AGUIStreamManager instance
    """
    return AGUIStreamManager(thread_id, run_id)


@asynccontextmanager
async def async_stream_context(thread_id: str = None, run_id: str = None):
    """
    Async context manager for stream management
    
    Args:
        thread_id: Thread ID for the conversation
        run_id: Run ID for this execution
        
    Yields:
        AsyncAGUIStreamManager instance
    """
    manager = AsyncAGUIStreamManager(thread_id, run_id)
    try:
        manager.start_run()
        yield manager
    except Exception as e:
        manager.error_run(str(e))
        raise
    finally:
        manager.finish_run()
