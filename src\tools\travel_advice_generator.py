"""
AI旅行建议生成工具
根据行程信息生成个性化的旅行建议
"""

import json
from typing import Dict, Any, Optional
from src.tools.llm_tool import LLMTool

class TravelAdviceGenerator:
    """AI旅行建议生成器"""
    
    def __init__(self):
        self.llm_tool = LLMTool()
    
    def generate_travel_advice(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据行程规划数据生成AI旅行建议
        
        Args:
            plan_data: 包含出发地、目的地、日期、交通工具等信息的字典
            
        Returns:
            包含三个部分建议的字典
        """
        try:
            # 提取关键信息
            departure_place = plan_data.get('departure_place', '未知出发地')
            arrival_place = plan_data.get('arrival_place', '未知目的地')
            date_str = plan_data.get('date_str', '未知日期')
            
            # 提取交通工具信息
            transportation = plan_data.get('transportation', {})
            major_transport = transportation.get('major', {})
            transport_type = major_transport.get('type', 'train')
            transport_code = major_transport.get('code', '未知')
            
            # 构建提示词
            prompt = self._build_prompt(
                departure_place=departure_place,
                arrival_place=arrival_place,
                date_str=date_str,
                transport_type=transport_type,
                transport_code=transport_code,
                plan_data=plan_data
            )
            
            print(f"[旅行建议生成器] 🤖 正在生成AI旅行建议...")
            
            # 调用LLM生成建议
            response = self.llm_tool.call_llm(prompt)
            
            if response and response.get('success'):
                advice_text = response.get('content', '')
                # 解析AI回复为结构化数据
                parsed_advice = self._parse_advice_response(advice_text)
                print(f"[旅行建议生成器] ✅ AI旅行建议生成成功")
                return parsed_advice
            else:
                print(f"[旅行建议生成器] ❌ AI生成失败，使用默认建议")
                return self._get_fallback_advice(plan_data)
                
        except Exception as e:
            print(f"[旅行建议生成器] ❌ 生成建议时出错: {e}")
            return self._get_fallback_advice(plan_data)
    
    def _build_prompt(self, departure_place: str, arrival_place: str, date_str: str, 
                     transport_type: str, transport_code: str, plan_data: Dict[str, Any]) -> str:
        """构建AI提示词"""
        
        # 确定交通工具类型
        if transport_type == 'flight':
            transport_desc = f"飞机 {transport_code}"
        else:
            transport_desc = f"火车 {transport_code}"
        
        prompt = f"""角色: 你是一位经验丰富、注重细节的旅行规划专家。

任务: 根据我提供的以下信息，为我生成一份详尽的、可执行的旅行建议。

已知信息:
出发地: {departure_place}
目的地: {arrival_place}
出发日期: {date_str}
大小交通工具: {transport_desc}

输出要求:
你的回答必须包含以下三个部分，并且内容清晰、条理分明：

第一部分：【出发时间与风险提示】
最佳出发时间: 基于所选的交通工具和可能的交通状况（例如，工作日早晚高峰、节假日拥堵等），明确建议我应该在出发当天几点从家中（或指定出发点）出发。

时间规划节点: 请列出从出发到顺利搭乘交通工具的关键时间节点，例如"建议到达机场/火车站时间"、"建议开始安检时间"等。

风险预警: 明确指出如果晚于某个具体时间点出发，我将面临错过飞机/火车/大巴的"高风险"时刻。请解释原因（例如：值机柜台关闭时间、安检排队高峰、道路拥堵加剧等）。

第二部分：【"万无一失"物品清单】
请根据我的目的地、旅行时长和季节，为我提供一份详细的物品清单。清单需要分类，并且包含"必备品"和"建议携带"两类。

必备品 (Essentials):
- 证件类: （例如：身份证、护照、签证、驾照等）
- 财物类: （例如：信用卡、少量现金、手机支付）
- 电子产品及配件: （例如：手机、充电器、充电宝、耳机）
- 健康与卫生: （例如：个人常用药、创可贴、消毒湿巾、口罩）

建议携带 (Recommended):
- 衣物类: （请根据目的地天气和旅行天数，具体建议衣物的种类和数量）
- 洗漱用品: （例如：牙刷、牙膏、洗面奶、旅行装洗护用品）
- 其他实用物品: （例如：雨伞、太阳镜、防晒霜、转换插头、U型枕、小零食等）

第三部分：【目的地小贴士】
交通衔接: 当我到达目的地（机场/火车站）后，请建议最便捷的几种交通方式前往市区或酒店，并简要说明其优缺点（例如：地铁方便快捷、出租车省心但贵、机场大巴性价比高等）。

当地须知: 提供1-2条关于目的地的重要提醒，例如当地的天气特点、支付习惯（是否需要准备现金）、或者近期有无特殊活动或注意事项。

风格要求: 语言亲切、专业、易于理解，就像一位真正的旅行专家在为我提供一对一的咨询。

请严格按照以上三个部分的格式输出，每个部分都要详细具体。"""

        return prompt
    
    def _parse_advice_response(self, advice_text: str) -> Dict[str, Any]:
        """解析AI回复为结构化数据"""
        try:
            # 简单的文本解析，将AI回复分为三个部分
            sections = {
                'timing_and_risks': '',
                'packing_list': '',
                'destination_tips': ''
            }

            # 查找各个部分的标题
            timing_start = advice_text.find('【出发时间与风险提示】')
            packing_start = advice_text.find('【"万无一失"物品清单】')
            tips_start = advice_text.find('【目的地小贴士】')

            if timing_start != -1:
                timing_end = packing_start if packing_start != -1 else len(advice_text)
                raw_content = advice_text[timing_start:timing_end].strip()
                sections['timing_and_risks'] = self._format_section_content(raw_content)

            if packing_start != -1:
                packing_end = tips_start if tips_start != -1 else len(advice_text)
                raw_content = advice_text[packing_start:packing_end].strip()
                sections['packing_list'] = self._format_section_content(raw_content)

            if tips_start != -1:
                raw_content = advice_text[tips_start:].strip()
                sections['destination_tips'] = self._format_section_content(raw_content)

            # 如果解析失败，将整个回复作为一个部分
            if not any(sections.values()):
                sections['timing_and_risks'] = self._format_section_content(advice_text)

            return {
                'success': True,
                'sections': sections,
                'full_text': advice_text
            }

        except Exception as e:
            print(f"[旅行建议生成器] ❌ 解析AI回复失败: {e}")
            return {
                'success': False,
                'sections': {
                    'timing_and_risks': self._format_section_content(advice_text),
                    'packing_list': '',
                    'destination_tips': ''
                },
                'full_text': advice_text
            }

    def _format_section_content(self, content: str) -> str:
        """格式化章节内容，提升可读性"""
        if not content:
            return content

        # 移除多余的空行
        lines = [line.strip() for line in content.split('\n') if line.strip()]

        formatted_lines = []
        for line in lines:
            # 处理标题行
            if line.startswith('【') and line.endswith('】'):
                formatted_lines.append(f"\n{line}\n")
            # 处理子标题
            elif line.endswith(':') or line.endswith('：'):
                formatted_lines.append(f"\n{line}")
            # 处理列表项
            elif line.startswith('-') or line.startswith('•') or line.startswith('*'):
                formatted_lines.append(f"  {line}")
            # 处理普通段落
            else:
                formatted_lines.append(line)

        return '\n'.join(formatted_lines).strip()
    
    def _get_fallback_advice(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取默认的旅行建议（当AI生成失败时使用）"""
        transportation = plan_data.get('transportation', {})
        major_transport = transportation.get('major', {})
        transport_type = major_transport.get('type', 'train')
        
        if transport_type == 'flight':
            timing_advice = """【出发时间与风险提示】
最佳出发时间: 建议提前2-3小时从家中出发
时间规划节点: 
- 提前2小时到达机场
- 提前1.5小时开始值机和安检
- 提前30分钟到达登机口
风险预警: 如果晚于起飞前1小时到达机场，将面临误机的高风险"""
            
            packing_advice = """【"万无一失"物品清单】
必备品:
- 证件类: 身份证、机票
- 财物类: 信用卡、少量现金
- 电子产品: 手机、充电器、充电宝
- 健康用品: 口罩、消毒湿巾

建议携带:
- 衣物类: 根据目的地天气准备
- 洗漱用品: 旅行装洗护用品
- 其他: 耳机、眼罩、U型枕"""
        else:
            timing_advice = """【出发时间与风险提示】
最佳出发时间: 建议提前1-2小时从家中出发
时间规划节点:
- 提前30分钟到达火车站
- 提前15分钟开始安检进站
- 提前5分钟到达站台
风险预警: 如果晚于开车前30分钟到达车站，将面临误车的风险"""
            
            packing_advice = """【"万无一失"物品清单】
必备品:
- 证件类: 身份证、火车票
- 财物类: 信用卡、少量现金
- 电子产品: 手机、充电器、充电宝
- 健康用品: 口罩、消毒湿巾

建议携带:
- 衣物类: 根据目的地天气和行程天数准备
- 洗漱用品: 旅行装洗护用品
- 其他: 零食、水杯、娱乐用品"""
        
        destination_tips = """【目的地小贴士】
交通衔接: 到达后可选择地铁、出租车或网约车前往市区
当地须知: 建议提前了解当地天气和支付方式"""
        
        return {
            'success': True,
            'sections': {
                'timing_and_risks': timing_advice,
                'packing_list': packing_advice,
                'destination_tips': destination_tips
            },
            'full_text': timing_advice + '\n\n' + packing_advice + '\n\n' + destination_tips
        }
