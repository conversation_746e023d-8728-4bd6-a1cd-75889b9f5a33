"""
AG-UI Tool Wrapper
Wraps existing tools to emit AG-UI TOOL_CALL_* events
"""

import json
import functools
from typing import Any, Dict, Callable, Optional, Generator
import traceback

from .events import (
    ToolCallStartEvent, ToolCallArgsEvent, ToolCallEndEvent, ToolCallResultEvent,
    generate_tool_call_id, generate_message_id, chunk_string
)
from .stream_manager import AGUIStreamManager


class AGUIToolWrapper:
    """Wrapper for tools to emit AG-UI events"""
    
    def __init__(self, stream_manager: AGUIStreamManager):
        """
        Initialize the tool wrapper
        
        Args:
            stream_manager: Stream manager for emitting events
        """
        self.stream_manager = stream_manager
    
    def wrap_tool(self, tool_func: Callable, tool_name: str = None) -> Callable:
        """
        Wrap a tool function to emit AG-UI events
        
        Args:
            tool_func: The tool function to wrap
            tool_name: Name of the tool (defaults to function name)
            
        Returns:
            Wrapped function that emits AG-UI events
        """
        tool_name = tool_name or tool_func.__name__
        
        @functools.wraps(tool_func)
        def wrapped_tool(*args, **kwargs) -> Generator[str, None, Any]:
            """Wrapped tool function that emits events"""
            tool_call_id = generate_tool_call_id()
            
            try:
                # Start tool call
                yield self.stream_manager.stream.emit(
                    ToolCallStartEvent(
                        tool_call_id=tool_call_id,
                        tool_call_name=tool_name,
                        parent_message_id=self.stream_manager._current_message_id
                    )
                )
                
                # Stream arguments
                args_dict = {
                    "args": args,
                    "kwargs": kwargs
                }
                args_json = json.dumps(args_dict, ensure_ascii=False, default=str)
                
                # Stream args in chunks
                for chunk in chunk_string(args_json, 100):
                    yield self.stream_manager.stream.emit(
                        ToolCallArgsEvent(
                            tool_call_id=tool_call_id,
                            delta=chunk
                        )
                    )
                
                # End tool call args
                yield self.stream_manager.stream.emit(
                    ToolCallEndEvent(tool_call_id=tool_call_id)
                )
                
                # Execute the actual tool
                result = tool_func(*args, **kwargs)
                
                # Emit tool result
                result_json = json.dumps(result, ensure_ascii=False, default=str)
                yield self.stream_manager.stream.emit(
                    ToolCallResultEvent(
                        message_id=generate_message_id(),
                        tool_call_id=tool_call_id,
                        content=result_json
                    )
                )
                
                return result
                
            except Exception as e:
                # Emit error result
                error_result = {
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
                yield self.stream_manager.stream.emit(
                    ToolCallResultEvent(
                        message_id=generate_message_id(),
                        tool_call_id=tool_call_id,
                        content=json.dumps(error_result, ensure_ascii=False)
                    )
                )
                raise
        
        return wrapped_tool
    
    def wrap_async_tool(self, tool_func: Callable, tool_name: str = None) -> Callable:
        """
        Wrap an async tool function to emit AG-UI events
        
        Args:
            tool_func: The async tool function to wrap
            tool_name: Name of the tool (defaults to function name)
            
        Returns:
            Wrapped async function that emits AG-UI events
        """
        tool_name = tool_name or tool_func.__name__
        
        @functools.wraps(tool_func)
        async def wrapped_async_tool(*args, **kwargs):
            """Wrapped async tool function that emits events"""
            tool_call_id = generate_tool_call_id()
            
            try:
                # Start tool call
                self.stream_manager.stream.emit(
                    ToolCallStartEvent(
                        tool_call_id=tool_call_id,
                        tool_call_name=tool_name,
                        parent_message_id=self.stream_manager._current_message_id
                    )
                )
                
                # Stream arguments
                args_dict = {
                    "args": args,
                    "kwargs": kwargs
                }
                args_json = json.dumps(args_dict, ensure_ascii=False, default=str)
                
                # Stream args in chunks
                for chunk in chunk_string(args_json, 100):
                    self.stream_manager.stream.emit(
                        ToolCallArgsEvent(
                            tool_call_id=tool_call_id,
                            delta=chunk
                        )
                    )
                
                # End tool call args
                self.stream_manager.stream.emit(
                    ToolCallEndEvent(tool_call_id=tool_call_id)
                )
                
                # Execute the actual tool
                result = await tool_func(*args, **kwargs)
                
                # Emit tool result
                result_json = json.dumps(result, ensure_ascii=False, default=str)
                self.stream_manager.stream.emit(
                    ToolCallResultEvent(
                        message_id=generate_message_id(),
                        tool_call_id=tool_call_id,
                        content=result_json
                    )
                )
                
                return result
                
            except Exception as e:
                # Emit error result
                error_result = {
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
                self.stream_manager.stream.emit(
                    ToolCallResultEvent(
                        message_id=generate_message_id(),
                        tool_call_id=tool_call_id,
                        content=json.dumps(error_result, ensure_ascii=False)
                    )
                )
                raise
        
        return wrapped_async_tool


def create_tool_wrapper(stream_manager: AGUIStreamManager) -> AGUIToolWrapper:
    """
    Create a new tool wrapper
    
    Args:
        stream_manager: Stream manager for emitting events
        
    Returns:
        New AGUIToolWrapper instance
    """
    return AGUIToolWrapper(stream_manager)


def agui_tool(stream_manager: AGUIStreamManager, tool_name: str = None):
    """
    Decorator to wrap a tool function with AG-UI events
    
    Args:
        stream_manager: Stream manager for emitting events
        tool_name: Name of the tool (defaults to function name)
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        wrapper = AGUIToolWrapper(stream_manager)
        return wrapper.wrap_tool(func, tool_name)
    
    return decorator


def agui_async_tool(stream_manager: AGUIStreamManager, tool_name: str = None):
    """
    Decorator to wrap an async tool function with AG-UI events
    
    Args:
        stream_manager: Stream manager for emitting events
        tool_name: Name of the tool (defaults to function name)
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        wrapper = AGUIToolWrapper(stream_manager)
        return wrapper.wrap_async_tool(func, tool_name)
    
    return decorator


# Convenience functions for wrapping existing tools
def wrap_train_search_tool(stream_manager: AGUIStreamManager):
    """Wrap the train search tool with AG-UI events"""
    from src.tools.train_search_tool import search_train

    def agui_search_train(departure_city: str, arrival_city: str):
        """AG-UI wrapped train search"""
        tool_call_id = generate_tool_call_id()

        # Emit tool call start
        stream_manager.stream.emit(
            ToolCallStartEvent(
                tool_call_id=tool_call_id,
                tool_call_name="search_train",
                parent_message_id=stream_manager._current_message_id
            )
        )

        # Emit tool arguments
        args_json = json.dumps({
            "departure_city": departure_city,
            "arrival_city": arrival_city
        }, ensure_ascii=False)

        for chunk in chunk_string(args_json, 100):
            stream_manager.stream.emit(
                ToolCallArgsEvent(
                    tool_call_id=tool_call_id,
                    delta=chunk
                )
            )

        # Emit tool call end
        stream_manager.stream.emit(
            ToolCallEndEvent(tool_call_id=tool_call_id)
        )

        try:
            # Execute the actual tool
            result = search_train(departure_city, arrival_city)

            # Emit tool result
            result_json = json.dumps(result, ensure_ascii=False, default=str)
            stream_manager.stream.emit(
                ToolCallResultEvent(
                    message_id=generate_message_id(),
                    tool_call_id=tool_call_id,
                    content=result_json
                )
            )

            return result

        except Exception as e:
            # Emit error result
            error_result = {"error": str(e)}
            stream_manager.stream.emit(
                ToolCallResultEvent(
                    message_id=generate_message_id(),
                    tool_call_id=tool_call_id,
                    content=json.dumps(error_result, ensure_ascii=False)
                )
            )
            raise

    return agui_search_train


def wrap_flight_search_tool(stream_manager: AGUIStreamManager):
    """Wrap the flight search tool with AG-UI events"""
    from src.tools.flight_search_tool import search_flight

    def agui_search_flight(departure_city: str, arrival_city: str):
        """AG-UI wrapped flight search"""
        tool_call_id = generate_tool_call_id()

        # Emit tool call events
        stream_manager.stream.emit(
            ToolCallStartEvent(
                tool_call_id=tool_call_id,
                tool_call_name="search_flight"
            )
        )

        args_json = json.dumps({
            "departure_city": departure_city,
            "arrival_city": arrival_city
        }, ensure_ascii=False)

        for chunk in chunk_string(args_json, 100):
            stream_manager.stream.emit(
                ToolCallArgsEvent(tool_call_id=tool_call_id, delta=chunk)
            )

        stream_manager.stream.emit(
            ToolCallEndEvent(tool_call_id=tool_call_id)
        )

        try:
            result = search_flight(departure_city, arrival_city)
            stream_manager.stream.emit(
                ToolCallResultEvent(
                    message_id=generate_message_id(),
                    tool_call_id=tool_call_id,
                    content=json.dumps(result, ensure_ascii=False, default=str)
                )
            )
            return result
        except Exception as e:
            error_result = {"error": str(e)}
            stream_manager.stream.emit(
                ToolCallResultEvent(
                    message_id=generate_message_id(),
                    tool_call_id=tool_call_id,
                    content=json.dumps(error_result, ensure_ascii=False)
                )
            )
            raise

    return agui_search_flight


def wrap_weather_tool(stream_manager: AGUIStreamManager):
    """Wrap the weather tool with AG-UI events"""
    from src.tools.search_tool import get_weather

    def agui_get_weather(city: str):
        """AG-UI wrapped weather search"""
        tool_call_id = generate_tool_call_id()

        stream_manager.stream.emit(
            ToolCallStartEvent(
                tool_call_id=tool_call_id,
                tool_call_name="get_weather"
            )
        )

        args_json = json.dumps({"city": city}, ensure_ascii=False)
        for chunk in chunk_string(args_json, 100):
            stream_manager.stream.emit(
                ToolCallArgsEvent(tool_call_id=tool_call_id, delta=chunk)
            )

        stream_manager.stream.emit(
            ToolCallEndEvent(tool_call_id=tool_call_id)
        )

        try:
            result = get_weather(city)
            stream_manager.stream.emit(
                ToolCallResultEvent(
                    message_id=generate_message_id(),
                    tool_call_id=tool_call_id,
                    content=json.dumps(result, ensure_ascii=False, default=str)
                )
            )
            return result
        except Exception as e:
            error_result = {"error": str(e)}
            stream_manager.stream.emit(
                ToolCallResultEvent(
                    message_id=generate_message_id(),
                    tool_call_id=tool_call_id,
                    content=json.dumps(error_result, ensure_ascii=False)
                )
            )
            raise

    return agui_get_weather


def wrap_gaode_tools(stream_manager: AGUIStreamManager):
    """Wrap all Gaode API tools with AG-UI events"""
    from src.tools.gaode_api_tool import (
        get_city_from_coordinates, get_railway_station,
        get_taxi_route, get_transit_route
    )

    def create_gaode_wrapper(tool_func, tool_name):
        """Create a wrapper for a Gaode tool"""
        def wrapped_tool(*args, **kwargs):
            tool_call_id = generate_tool_call_id()

            stream_manager.stream.emit(
                ToolCallStartEvent(
                    tool_call_id=tool_call_id,
                    tool_call_name=tool_name
                )
            )

            args_dict = {"args": args, "kwargs": kwargs}
            args_json = json.dumps(args_dict, ensure_ascii=False, default=str)

            for chunk in chunk_string(args_json, 100):
                stream_manager.stream.emit(
                    ToolCallArgsEvent(tool_call_id=tool_call_id, delta=chunk)
                )

            stream_manager.stream.emit(
                ToolCallEndEvent(tool_call_id=tool_call_id)
            )

            try:
                result = tool_func(*args, **kwargs)
                stream_manager.stream.emit(
                    ToolCallResultEvent(
                        message_id=generate_message_id(),
                        tool_call_id=tool_call_id,
                        content=json.dumps(result, ensure_ascii=False, default=str)
                    )
                )
                return result
            except Exception as e:
                error_result = {"error": str(e)}
                stream_manager.stream.emit(
                    ToolCallResultEvent(
                        message_id=generate_message_id(),
                        tool_call_id=tool_call_id,
                        content=json.dumps(error_result, ensure_ascii=False)
                    )
                )
                raise

        return wrapped_tool

    return {
        "get_city_from_coordinates": create_gaode_wrapper(get_city_from_coordinates, "get_city_from_coordinates"),
        "get_railway_station": create_gaode_wrapper(get_railway_station, "get_railway_station"),
        "get_taxi_route": create_gaode_wrapper(get_taxi_route, "get_taxi_route"),
        "get_transit_route": create_gaode_wrapper(get_transit_route, "get_transit_route"),
    }
