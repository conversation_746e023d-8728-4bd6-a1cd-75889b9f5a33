/**
 * AG-UI Client Library
 * JavaScript client for handling AG-UI protocol events
 */

class AGUIClient {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '';
        this.eventHandlers = new Map();
        this.currentRun = null;
        this.state = {};
        this.messages = [];
        this.toolCalls = new Map();
        
        // Default event handlers
        this.setupDefaultHandlers();
    }
    
    /**
     * Setup default event handlers
     */
    setupDefaultHandlers() {
        this.on('RUN_STARTED', (event) => {
            this.currentRun = {
                threadId: event.threadId,
                runId: event.runId,
                startTime: Date.now()
            };
            console.log('Run started:', this.currentRun);
        });
        
        this.on('RUN_FINISHED', (event) => {
            if (this.currentRun) {
                this.currentRun.endTime = Date.now();
                this.currentRun.duration = this.currentRun.endTime - this.currentRun.startTime;
            }
            console.log('Run finished:', this.currentRun);
        });
        
        this.on('RUN_ERROR', (event) => {
            console.error('Run error:', event.message);
        });
        
        this.on('STATE_DELTA', (event) => {
            this.applyStateDelta(event.delta);
        });
        
        this.on('MESSAGES_SNAPSHOT', (event) => {
            this.messages = event.messages;
        });
    }
    
    /**
     * Register an event handler
     * @param {string} eventType - AG-UI event type
     * @param {function} handler - Event handler function
     */
    on(eventType, handler) {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        this.eventHandlers.get(eventType).push(handler);
    }
    
    /**
     * Remove an event handler
     * @param {string} eventType - AG-UI event type
     * @param {function} handler - Event handler function to remove
     */
    off(eventType, handler) {
        if (this.eventHandlers.has(eventType)) {
            const handlers = this.eventHandlers.get(eventType);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    /**
     * Emit an event to all registered handlers
     * @param {string} eventType - AG-UI event type
     * @param {object} event - Event data
     */
    emit(eventType, event) {
        if (this.eventHandlers.has(eventType)) {
            this.eventHandlers.get(eventType).forEach(handler => {
                try {
                    handler(event);
                } catch (error) {
                    console.error(`Error in event handler for ${eventType}:`, error);
                }
            });
        }
    }
    
    /**
     * Start a conversation with the agent
     * @param {object} input - Conversation input
     * @returns {Promise<ReadableStream>} Event stream
     */
    async runAgent(input) {
        const response = await fetch(`${this.baseUrl}/agent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify(input)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return this.processEventStream(response);
    }
    
    /**
     * Simple chat interface
     * @param {string} message - User message
     * @returns {Promise<ReadableStream>} Event stream
     */
    async chat(message) {
        const response = await fetch(`${this.baseUrl}/agent/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify({ message })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return this.processEventStream(response);
    }
    
    /**
     * Process Server-Sent Events stream
     * @param {Response} response - Fetch response
     * @returns {Promise<void>}
     */
    async processEventStream(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.slice(6));
                            this.handleEvent(eventData);
                        } catch (error) {
                            console.warn('Failed to parse event:', error, line);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    }
    
    /**
     * Handle incoming AG-UI event
     * @param {object} event - AG-UI event
     */
    handleEvent(event) {
        // Emit to registered handlers
        this.emit(event.type, event);
        
        // Handle specific event types
        switch (event.type) {
            case 'TEXT_MESSAGE_START':
                this.handleTextMessageStart(event);
                break;
            case 'TEXT_MESSAGE_CONTENT':
                this.handleTextMessageContent(event);
                break;
            case 'TEXT_MESSAGE_END':
                this.handleTextMessageEnd(event);
                break;
            case 'TOOL_CALL_START':
                this.handleToolCallStart(event);
                break;
            case 'TOOL_CALL_ARGS':
                this.handleToolCallArgs(event);
                break;
            case 'TOOL_CALL_END':
                this.handleToolCallEnd(event);
                break;
            case 'TOOL_CALL_RESULT':
                this.handleToolCallResult(event);
                break;
        }
    }
    
    /**
     * Handle text message events
     */
    handleTextMessageStart(event) {
        if (!this.currentMessages) {
            this.currentMessages = new Map();
        }
        this.currentMessages.set(event.messageId, {
            id: event.messageId,
            role: event.role,
            content: '',
            isComplete: false
        });
    }
    
    handleTextMessageContent(event) {
        if (this.currentMessages && this.currentMessages.has(event.messageId)) {
            const message = this.currentMessages.get(event.messageId);
            message.content += event.delta;
        }
    }
    
    handleTextMessageEnd(event) {
        if (this.currentMessages && this.currentMessages.has(event.messageId)) {
            const message = this.currentMessages.get(event.messageId);
            message.isComplete = true;
            
            // Add to messages array
            this.messages.push(message);
        }
    }
    
    /**
     * Handle tool call events
     */
    handleToolCallStart(event) {
        this.toolCalls.set(event.toolCallId, {
            id: event.toolCallId,
            name: event.toolCallName,
            parentMessageId: event.parentMessageId,
            args: '',
            result: null,
            isComplete: false
        });
    }
    
    handleToolCallArgs(event) {
        if (this.toolCalls.has(event.toolCallId)) {
            const toolCall = this.toolCalls.get(event.toolCallId);
            toolCall.args += event.delta;
        }
    }
    
    handleToolCallEnd(event) {
        if (this.toolCalls.has(event.toolCallId)) {
            const toolCall = this.toolCalls.get(event.toolCallId);
            toolCall.argsComplete = true;
        }
    }
    
    handleToolCallResult(event) {
        if (this.toolCalls.has(event.toolCallId)) {
            const toolCall = this.toolCalls.get(event.toolCallId);
            toolCall.result = event.content;
            toolCall.isComplete = true;
        }
    }
    
    /**
     * Apply JSON Patch operations to state
     * @param {Array} patches - JSON Patch operations
     */
    applyStateDelta(patches) {
        for (const patch of patches) {
            switch (patch.op) {
                case 'add':
                case 'replace':
                    this.setStateValue(patch.path, patch.value);
                    break;
                case 'remove':
                    this.removeStateValue(patch.path);
                    break;
            }
        }
    }
    
    /**
     * Set a value in state using JSON Pointer path
     * @param {string} path - JSON Pointer path
     * @param {any} value - Value to set
     */
    setStateValue(path, value) {
        const parts = path.split('/').slice(1); // Remove leading empty string
        let current = this.state;
        
        for (let i = 0; i < parts.length - 1; i++) {
            const part = parts[i];
            if (!(part in current)) {
                current[part] = {};
            }
            current = current[part];
        }
        
        if (parts.length > 0) {
            current[parts[parts.length - 1]] = value;
        }
    }
    
    /**
     * Remove a value from state using JSON Pointer path
     * @param {string} path - JSON Pointer path
     */
    removeStateValue(path) {
        const parts = path.split('/').slice(1);
        let current = this.state;
        
        for (let i = 0; i < parts.length - 1; i++) {
            const part = parts[i];
            if (!(part in current)) {
                return; // Path doesn't exist
            }
            current = current[part];
        }
        
        if (parts.length > 0) {
            delete current[parts[parts.length - 1]];
        }
    }
    
    /**
     * Get current state
     * @returns {object} Current state
     */
    getState() {
        return { ...this.state };
    }
    
    /**
     * Get current messages
     * @returns {Array} Current messages
     */
    getMessages() {
        return [...this.messages];
    }
    
    /**
     * Get current tool calls
     * @returns {Array} Current tool calls
     */
    getToolCalls() {
        return Array.from(this.toolCalls.values());
    }
    
    /**
     * Reset client state
     */
    reset() {
        this.currentRun = null;
        this.state = {};
        this.messages = [];
        this.toolCalls.clear();
        if (this.currentMessages) {
            this.currentMessages.clear();
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AGUIClient;
} else if (typeof window !== 'undefined') {
    window.AGUIClient = AGUIClient;
}
