<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规划出错了 - AI Trip Plan</title>
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;700&family=Noto+Sans+SC:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e1e8f0 100%);
            opacity: 0; /* 初始不可见 */
            animation: page-fade-in 0.6s ease-out forwards;
            animation-delay: 0.2s;
        }
        @keyframes page-fade-in {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .font-serif { font-family: 'Noto Serif SC', serif; }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">
    <div class="text-center p-8 max-w-2xl mx-auto">
        <div class="w-24 h-24 mx-auto bg-red-100 rounded-full flex items-center justify-center mb-6">
            <i class="fas fa-exclamation-triangle text-5xl text-red-500"></i>
        </div>
        <h1 class="text-4xl font-bold font-serif text-gray-800">哦豁，出错了...</h1>
        <p class="mt-4 text-lg text-gray-600">
            在为您规划行程时遇到了一个问题。
        </p>
        <div class="mt-6 text-left bg-white p-4 rounded-lg shadow-inner">
            <p class="font-semibold text-gray-700">错误详情：</p>
            <p class="text-sm text-red-600 mt-2 font-mono">{{ error_message }}</p>
        </div>

        {% if thinking_process %}
        <div class="mt-6 text-left">
            <h3 class="text-lg font-semibold text-gray-700">AI最后的思考过程：</h3>
            <div class="bg-gray-100 p-4 rounded-lg mt-2 text-sm text-gray-600 max-h-48 overflow-y-auto">
                <ul class="space-y-2">
                    {% for item in thinking_process %}
                    <li><strong>{{ item.step }}:</strong> {{ item.content }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}

        <div class="mt-8">
            <a href="/" class="text-indigo-600 hover:text-indigo-800 font-semibold transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>返回首页重试
            </a>
        </div>
    </div>
</body>
</html> 