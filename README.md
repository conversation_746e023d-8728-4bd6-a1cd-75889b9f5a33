# 智能旅行规划助手

## 🌟 项目概述

智能旅行规划助手是一款先进的人工智能工具，旨在简化您的旅行规划。无论您需要查询天气、寻找最佳火车路线，还是获取一般性的旅行建议，这款助手都能为您提供帮助。它利用 LangGraph 和先进的语言模型的强大功能，提供智能的、具有上下文感知能力的回复。

该应用程序提供两种主要界面：
- 用户友好的**网页界面**，用于交互式规划。
- **命令行界面 (CLI)**，用于快速查询和编写脚本。

## 🛠️ 技术栈

该项目采用现代化、强大的技术栈构建：

- **后端:** Python 3.11+
- **人工智能框架:** LangChain 与 LangGraph，用于创建强大的、有状态的代理。
- **语言模型:** ZhipuAI (或任何其他兼容的模型)。
- **Web 框架:** FastAPI，用于构建高性能的 API。
- **Web 服务器:** Uvicorn，用于为 FastAPI 应用程序提供服务。
- **依赖项:** `python-dotenv`, `requests`。

## 📂 新项目结构

该项目已重构为清晰、可扩展且可维护的分层架构。这种关注点分离的设计使得扩展和测试单个组件变得容易。

```
.
├── .env                  # 环境变量 (API 密钥等)
├── main.py               # 运行 CLI 或 Web 应用程序的主入口点
├── requirements.txt      # Python 依赖项
├── src/                  # 主要源代码目录
│   ├── agent/            # 核心 LangGraph 代理逻辑
│   │   ├── graph.py      # 代理图的定义
│   │   ├── nodes.py      # 构成代理图的节点
│   │   ├── router.py     # 节点之间的路由逻辑
│   │   └── state.py      # 代理状态的定义
│   ├── clients/          # 用于与外部 API 交互的客户端
│   │   └── train_client.py # 同程旅行 API 的客户端
│   ├── services/         # 处理数据的业务逻辑
│   │   └── train_service.py # 分析火车数据的逻辑
│   ├── tools/            # 代理可以使用的 LangChain 工具
│   │   ├── search_tool.py # 天气搜索工具
│   │   └── train_search_tool.py # 火车搜索工具
│   ├── utils/            # 实用功能
│   │   └── map_utils.py  # (示例实用程序)
│   └── web/              # FastAPI Web 应用程序
│       ├── app.py        # FastAPI 应用程序定义
│       └── templates/    # Web 界面的 HTML 模板
│           ├── index.html
│           └── ...
└── README.md             # 本文件
```

### 核心目录职责：

- **`src/agent`**: 包含 LangGraph 代理的所有组件，包括图定义、节点、路由器和状态。
- **`src/clients`**: 包含用于与外部 API 交互的低级客户端。每个客户端负责处理特定 API 的请求/响应周期。
- **`src/services`**: 包含应用程序的业务逻辑。服务使用客户端获取数据，然后对其进行处理以提供有意义的结果。
- **`src/tools`**: 定义 LangGraph 代理可以使用的工具。这些工具是围绕服务的轻量级包装器。
- **`src/web`**: FastAPI Web 应用程序，包括 API 端点和 HTML 模板。

## 🚀 本地环境设置和启动

请按照以下步骤在您的本地计算机上运行智能旅行规划助手。

### 1. 克隆存储库

```bash
git clone <repository-url>
cd <repository-directory>
```

### 2. 创建虚拟环境

强烈建议使用虚拟环境来管理项目的依赖关系。

```bash
python -m venv venv
source venv/bin/activate  # 在 Windows 上，使用 `venv\Scripts\activate`
```

### 3. 安装依赖项

使用 pip 安装所有必需的 Python 包：

```bash
pip install -r requirements.txt
```

### 4. 配置环境变量

该应用程序需要语言模型和任何其他外部服务的 API 密钥。将 `.env.example` 文件复制到 `.env` 并填写您的 API 密钥。

```bash
cp .env.example .env
```

现在，用您喜欢的文本编辑器编辑 `.env` 文件：

```
# .env
ZHIPUAI_API_KEY="your-zhipuai-api-key"
```

### 5. 运行应用程序

`main.py` 脚本是应用程序的唯一入口点。

#### 运行命令行界面 (CLI)：

```bash
python main.py cli
```

您将看到助手的问候，然后可以开始输入您的查询。

#### 运行 Web 界面：

```bash
python main.py web
```

Web 服务器将启动，您可以在浏览器中访问 `http://localhost:8000` 上的应用程序。

## 🌐 API 使用示例 (Web 界面)

您可以使用 `curl` 等工具或任何 API 客户端与 Web 应用程序的 API 进行交互。

### 发送聊天消息

- **端点:** `POST /chat`
- **请求正文:** 一个包含 `message` 键的 JSON 对象。
- **示例:**

```bash
curl -X POST "http://localhost:8000/chat" \
-H "Content-Type: application/json" \
-d '{
    "message": "今天北京的天气怎么样？"
}'
```

### 预期响应

API 将返回一个包含代理回复的 JSON 对象。

```json
{
    "reply": "北京今天天气晴朗，气温 25°C，有微风。"
}
```
