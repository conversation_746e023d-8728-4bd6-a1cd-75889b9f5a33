# tools/gaode_api_tool.py
from typing import Dict, Any, List, Optional, Tuple
import requests
import json
import time
import traceback
from datetime import datetime, timedelta
import re
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 高德地图Web服务API密钥，从环境变量获取
AMAP_KEY = os.getenv("AMAP_API_KEY", "")

# 检查API密钥是否存在
if not AMAP_KEY:
    print("[警告] 未找到高德地图API密钥(AMAP_API_KEY)环境变量，将尝试直接从项目根目录的.env文件读取")
    try:
        # 尝试直接读取项目根目录下的.env文件
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        if key == 'AMAP_API_KEY':
                            AMAP_KEY = value
                            print(f"[信息] 从.env文件成功读取到高德地图API密钥")
                            break
    except Exception as e:
        print(f"[警告] 读取.env文件时出错: {str(e)}")

# 如果仍然没有密钥，使用备用测试密钥
if not AMAP_KEY:
    print("[警告] 未能获取到高德地图API密钥，将使用备用测试密钥")
    AMAP_KEY = "e8d39110f00d7a502ae199a6d699dd3a"  # 备用测试密钥

print(f"[信息] 使用的高德地图API密钥: {AMAP_KEY[:4]}...{AMAP_KEY[-4:]}")

def is_valid_coordinates(lng: Optional[str], lat: Optional[str]) -> bool:
    """
    验证经纬度坐标是否有效
    
    参数:
        lng: 经度字符串
        lat: 纬度字符串
        
    返回:
        坐标是否有效的布尔值
    """
    if not lng or not lat:
        return False
    
    try:
        lng_float = float(lng)
        lat_float = float(lat)
        
        # 经度范围: -180 到 180
        # 纬度范围: -90 到 90
        valid_lng = -180 <= lng_float <= 180
        valid_lat = -90 <= lat_float <= 90
        
        return valid_lng and valid_lat
    except ValueError:
        return False

def get_city_from_coordinates(lng: str, lat: str) -> Dict[str, Any]:
    """
    使用高德地图API根据经纬度获取城市名称
    
    参数:
        lng: 经度
        lat: 纬度
        
    返回:
        包含城市信息的字典，格式为 {"status": "success/error", "city": "城市名", "address": "详细地址", "message": "错误信息"}
    """
    print(f"[高德API] 正在获取坐标({lng},{lat})的城市信息...")
    
    # 检查坐标有效性
    if not is_valid_coordinates(lng, lat):
        error_msg = f"无效的坐标: 经度={lng}, 纬度={lat}"
        print(f"[高德API] ❌ {error_msg}")
        return {"status": "error", "message": error_msg}
    
    # 构建API请求URL
    url = "https://restapi.amap.com/v3/geocode/regeo"
    params = {
        "key": AMAP_KEY,
        "location": f"{lng},{lat}",
        "extensions": "base",
        "output": "json"
    }
    
    try:
        # 发送请求
        response = requests.get(url, params=params)
        data = response.json()
        
        # 检查API响应状态
        if data.get("status") == "1":
            regeo_code = data.get("regeocode", {})
            address_component = regeo_code.get("addressComponent", {})
            formatted_address = regeo_code.get("formatted_address", "")
            
            # 获取城市名称
            city = address_component.get("city", "")
            if not city:
                # 处理直辖市，如北京、上海等，这些城市的city字段可能为空，使用province
                city = address_component.get("province", "")
                
            # 如果city以"市"结尾，移除"市"
            if city and city.endswith("市"):
                city = city[:-1]
                
            print(f"[高德API] ✅ 获取到城市: {city}, 地址: {formatted_address}")
            return {
                "status": "success", 
                "city": city, 
                "address": formatted_address,
                "district": address_component.get("district", ""),
                "province": address_component.get("province", "")
            }
        else:
            error_msg = data.get("info", "未知错误")
            print(f"[高德API] ❌ 获取城市失败: {error_msg}")
            return {"status": "error", "message": error_msg}
            
    except Exception as e:
        print(f"[高德API] ❌ 请求城市信息时出错: {str(e)}")
        print(traceback.format_exc())
        return {"status": "error", "message": str(e)}

def get_railway_station(city: str) -> Dict[str, Any]:
    """
    使用高德地图POI搜索API查找指定城市的高铁站/火车站
    
    参数:
        city: 城市名称
        
    返回:
        包含车站信息的字典，格式为 {"status": "success/error", "name": "站名", "location": "经度,纬度", "message": "错误信息"}
    """
    print(f"[高德API] 正在搜索{city}的高铁站/火车站...")
    
    # 构建API请求URL
    url = "https://restapi.amap.com/v3/place/text"
    
    # 优先搜索高铁站
    high_speed_params = {
        "key": AMAP_KEY,
        "keywords": f"{city}高铁站",
        "city": city,
        "offset": 10,
        "page": 1,
        "extensions": "all"
    }
    
    try:
        # 先尝试搜索高铁站
        response = requests.get(url, params=high_speed_params)
        data = response.json()
        
        if data.get("status") == "1" and data.get("pois") and len(data["pois"]) > 0:
            # 找到高铁站
            station = data["pois"][0]
            station_name = station.get("name", "")
            station_location = station.get("location", "")
            
            print(f"[高德API] ✅ 找到高铁站: {station_name}, 坐标: {station_location}")
            return {
                "status": "success",
                "name": station_name,
                "location": station_location,
                "address": station.get("address", ""),
                "type": "高铁站"
            }
        
        # 如果没找到高铁站，尝试搜索火车站
        train_params = {
            "key": AMAP_KEY,
            "keywords": f"{city}火车站",
            "city": city,
            "offset": 10,
            "page": 1,
            "extensions": "all"
        }
        
        response = requests.get(url, params=train_params)
        data = response.json()
        
        if data.get("status") == "1" and data.get("pois") and len(data["pois"]) > 0:
            # 找到火车站
            station = data["pois"][0]
            station_name = station.get("name", "")
            station_location = station.get("location", "")
            
            print(f"[高德API] ✅ 找到火车站: {station_name}, 坐标: {station_location}")
            return {
                "status": "success",
                "name": station_name,
                "location": station_location,
                "address": station.get("address", ""),
                "type": "火车站"
            }
        
        # 如果高铁站和火车站都没找到，返回错误
        print(f"[高德API] ⚠️ 未找到{city}的高铁站或火车站，将使用城市中心坐标")
        return {
            "status": "error", 
            "message": f"未找到{city}的高铁站或火车站"
        }
            
    except Exception as e:
        print(f"[高德API] ❌ 搜索车站时出错: {str(e)}")
        print(traceback.format_exc())
        return {"status": "error", "message": str(e)}

def get_taxi_route(origin_lng: str, origin_lat: str, dest_lng: str, dest_lat: str) -> Dict[str, Any]:
    """
    使用高德地图驾车路线规划API获取打车路线和费用

    参数:
        origin_lng: 起点经度
        origin_lat: 起点纬度
        dest_lng: 终点经度
        dest_lat: 终点纬度

    返回:
        包含打车路线信息和费用的字典
    """
    print(f"[高德API] 正在规划从({origin_lng},{origin_lat})到({dest_lng},{dest_lat})的打车路线...")
    
    # 检查坐标有效性
    if not is_valid_coordinates(origin_lng, origin_lat):
        error_msg = f"无效的起点坐标: 经度={origin_lng}, 纬度={origin_lat}"
        print(f"[高德API] ❌ {error_msg}")
        return {"status": "error", "message": error_msg}
        
    if not is_valid_coordinates(dest_lng, dest_lat):
        error_msg = f"无效的终点坐标: 经度={dest_lng}, 纬度={dest_lat}"
        print(f"[高德API] ❌ {error_msg}")
        return {"status": "error", "message": error_msg}
    
    # 构建API请求URL
    url = "https://restapi.amap.com/v3/direction/driving"
    params = {
        "key": AMAP_KEY,
        "origin": f"{origin_lng},{origin_lat}",
        "destination": f"{dest_lng},{dest_lat}",
        "extensions": "all",  # 返回详细路线
        "strategy": 10,  # 时间最短
        "output": "json"
    }
    
    try:
        # 发送请求
        response = requests.get(url, params=params)
        data = response.json()
        
        # 检查API响应状态
        if data.get("status") == "1" and data.get("route"):
            route = data["route"]
            paths = route.get("paths", [])

            # 获取打车费用（extensions=all时才返回）
            taxi_cost = route.get("taxi_cost", 0)
            print(f"[高德API] 调试信息 - route keys: {list(route.keys())}")
            print(f"[高德API] 调试信息 - taxi_cost: {taxi_cost}")
            
            if paths:
                path = paths[0]  # 获取第一条路线（最优路线）
                
                # 提取路线信息
                distance = int(path.get("distance", 0))  # 路线距离(米)
                duration = int(path.get("duration", 0))  # 行驶时间(秒)
                
                # 如果duration不合理，基于距离估算
                if duration <= 0:
                    # 假设平均车速30km/h，计算预估时间
                    duration = int(distance / 30000 * 3600)  # 转换为秒
                
                traffic_lights = path.get("traffic_lights", 0)  # 红绿灯数量
                tolls = path.get("tolls", 0)  # 过路费
                toll_distance = path.get("toll_distance", 0)  # 收费路段长度
                
                # 获取路径详情
                steps = path.get("steps", [])
                formatted_steps = []
                
                for step in steps:
                    formatted_steps.append({
                        "instruction": step.get("instruction", ""),
                        "distance": step.get("distance", "0"),
                        "duration": step.get("duration", "0"),
                        "road_name": step.get("road", ""),
                        "tolls": step.get("tolls", "0")
                    })
                
                # 格式化持续时间为可读格式
                hours = duration // 3600
                minutes = (duration % 3600) // 60
                seconds = duration % 60
                
                formatted_duration = ""
                if hours > 0:
                    formatted_duration += f"{hours}小时"
                if minutes > 0 or hours > 0:
                    formatted_duration += f"{minutes}分钟"
                if seconds > 0 or (hours == 0 and minutes == 0):
                    formatted_duration += f"{seconds}秒"
                
                # 不再返回格式化的距离，而是直接返回整数
                print(f"[高德API] ✅ 获取到打车路线: 距离={distance/1000:.1f}公里, 时间={formatted_duration}, 费用=¥{taxi_cost}")
                return {
                    "status": "success",
                    "distance": distance,  # 直接返回整数
                    "distance_value": distance,
                    "duration": formatted_duration,
                    "duration_value": duration,
                    "raw_duration_seconds": duration,
                    "traffic_lights": traffic_lights,
                    "tolls": tolls,
                    "toll_distance": toll_distance,
                    "taxi_cost": taxi_cost,  # 添加打车费用
                    "steps": formatted_steps,
                    "polyline": path.get("polyline", "")  # 路线坐标点串
                }
            else:
                error_msg = "未找到有效路线"
                print(f"[高德API] ❌ {error_msg}")
                return {"status": "error", "message": error_msg}
        else:
            error_msg = data.get("info", "未知错误")
            print(f"[高德API] ❌ 获取驾车路线失败: {error_msg}")
            return {"status": "error", "message": error_msg}
            
    except Exception as e:
        print(f"[高德API] ❌ 请求驾车路线时出错: {str(e)}")
        print(traceback.format_exc())
        return {"status": "error", "message": str(e)}

def geocode_address(address: str, city: str = "") -> Dict[str, Any]:
    """
    使用高德地图地理编码API将地址转换为经纬度坐标
    
    参数:
        address: 地址文本
        city: 所在城市(可选)
        
    返回:
        包含坐标信息的字典
    """
    print(f"[高德API] 正在对地址进行地理编码: {address}")
    
    # 构建API请求URL
    url = "https://restapi.amap.com/v3/geocode/geo"
    params = {
        "key": AMAP_KEY,
        "address": address,
        "output": "json"
    }
    
    if city:
        params["city"] = city
    
    try:
        # 发送请求
        response = requests.get(url, params=params)
        data = response.json()
        
        # 检查API响应状态
        if data.get("status") == "1" and data.get("geocodes") and len(data["geocodes"]) > 0:
            geocode = data["geocodes"][0]
            location = geocode.get("location", "")  # 格式为 "经度,纬度"
            formatted_address = geocode.get("formatted_address", "")
            
            if location:
                lng, lat = location.split(",")
                print(f"[高德API] ✅ 地理编码成功: 地址={formatted_address}, 坐标=({lng},{lat})")
                return {
                    "status": "success",
                    "lng": lng,
                    "lat": lat,
                    "location": location,
                    "formatted_address": formatted_address,
                    "province": geocode.get("province", ""),
                    "city": geocode.get("city", ""),
                    "district": geocode.get("district", "")
                }
            else:
                error_msg = "地理编码结果中没有坐标信息"
                print(f"[高德API] ❌ {error_msg}")
                return {"status": "error", "message": error_msg}
        else:
            error_msg = data.get("info", "未知错误")
            print(f"[高德API] ❌ 地理编码失败: {error_msg}")
            return {"status": "error", "message": error_msg}
            
    except Exception as e:
        print(f"[高德API] ❌ 地理编码请求出错: {str(e)}")
        print(traceback.format_exc())
        return {"status": "error", "message": str(e)}

def parse_duration_to_timedelta(duration_str: str) -> timedelta:
    """
    将格式化的持续时间字符串(如"2小时30分钟")解析为timedelta对象
    
    参数:
        duration_str: 格式化的持续时间字符串
        
    返回:
        对应的timedelta对象
    """
    hours = 0
    minutes = 0
    seconds = 0
    
    # 提取小时
    hour_match = re.search(r'(\d+)小时', duration_str)
    if hour_match:
        hours = int(hour_match.group(1))
    
    # 提取分钟
    minute_match = re.search(r'(\d+)分钟', duration_str)
    if minute_match:
        minutes = int(minute_match.group(1))
    
    # 提取秒
    second_match = re.search(r'(\d+)秒', duration_str)
    if second_match:
        seconds = int(second_match.group(1))
    
    return timedelta(hours=hours, minutes=minutes, seconds=seconds)

def get_transit_route(origin_lng: str, origin_lat: str, dest_lng: str, dest_lat: str, city: str) -> Dict[str, Any]:
    """
    使用高德地图公交路径规划API获取公交路线

    参数:
        origin_lng: 起点经度
        origin_lat: 起点纬度
        dest_lng: 终点经度
        dest_lat: 终点纬度
        city: 城市名称

    返回:
        包含公交路线信息的字典
    """
    print(f"[高德API] 正在规划从({origin_lng},{origin_lat})到({dest_lng},{dest_lat})在{city}的公交路线...")

    # 检查坐标有效性
    if not is_valid_coordinates(origin_lng, origin_lat):
        error_msg = f"无效的起点坐标: 经度={origin_lng}, 纬度={origin_lat}"
        print(f"[高德API] ❌ {error_msg}")
        return {"status": "error", "message": error_msg}

    if not is_valid_coordinates(dest_lng, dest_lat):
        error_msg = f"无效的终点坐标: 经度={dest_lng}, 纬度={dest_lat}"
        print(f"[高德API] ❌ {error_msg}")
        return {"status": "error", "message": error_msg}

    # 构建API请求URL
    url = "https://restapi.amap.com/v3/direction/transit/integrated"
    params = {
        "key": AMAP_KEY,
        "origin": f"{origin_lng},{origin_lat}",
        "destination": f"{dest_lng},{dest_lat}",
        "city": city,
        "extensions": "all",  # 返回详细信息
        "strategy": 0,  # 最快捷模式
        "output": "json"
    }

    try:
        # 发送请求
        response = requests.get(url, params=params)
        data = response.json()

        # 检查API响应状态
        if data.get("status") == "1" and data.get("route"):
            route = data["route"]
            transits = route.get("transits", [])

            if transits:
                # 选择第一个方案（最优方案）
                transit = transits[0]

                # 提取基本信息
                cost_raw = transit.get("cost", 0)
                # 处理cost可能是列表的情况
                if isinstance(cost_raw, list):
                    cost = float(cost_raw[0]) if cost_raw else 0
                else:
                    cost = float(cost_raw)

                duration = int(transit.get("duration", 0))  # 时间(秒)
                walking_distance = int(transit.get("walking_distance", 0))  # 步行距离(米)

                # 格式化持续时间
                hours = duration // 3600
                minutes = (duration % 3600) // 60
                seconds = duration % 60

                formatted_duration = ""
                if hours > 0:
                    formatted_duration += f"{hours}小时"
                if minutes > 0 or hours > 0:
                    formatted_duration += f"{minutes}分钟"
                if seconds > 0 or (hours == 0 and minutes == 0):
                    formatted_duration += f"{seconds}秒"

                # 提取换乘信息
                segments = transit.get("segments", [])
                transit_steps = []

                for segment in segments:
                    if "bus" in segment:
                        # 公交/地铁段
                        bus_info = segment["bus"]
                        buslines = bus_info.get("buslines", [])
                        if buslines:
                            busline = buslines[0]
                            line_name = busline.get("name", "")
                            dep_stop = busline.get("departure_stop", {}).get("name", "")
                            arr_stop = busline.get("arrival_stop", {}).get("name", "")
                            distance = busline.get("distance", 0)
                            via_num = int(busline.get("via_num", 0))  # 确保是整数

                            # 生成用户友好的指引文本
                            instruction = f"乘坐 {line_name}"
                            if dep_stop:
                                instruction += f"，从 {dep_stop} 上车"
                            if arr_stop:
                                instruction += f"，到 {arr_stop} 下车"
                            if via_num > 0:
                                instruction += f"（经过{via_num}站）"

                            transit_steps.append({
                                "type": "transit",
                                "name": line_name,
                                "departure_stop": dep_stop,
                                "arrival_stop": arr_stop,
                                "distance": distance,
                                "duration": busline.get("duration", 0),
                                "via_num": via_num,
                                "instruction": instruction  # 添加指引文本
                            })
                    elif "walking" in segment:
                        # 步行段
                        walking_info = segment["walking"]
                        distance = walking_info.get("distance", 0)
                        duration = walking_info.get("duration", 0)

                        # 生成更详细的步行指引
                        instruction = f"步行 {distance}米"
                        if duration > 0:
                            minutes = duration // 60
                            if minutes > 0:
                                instruction += f"（约{minutes}分钟）"

                        transit_steps.append({
                            "type": "walking",
                            "distance": distance,
                            "duration": duration,
                            "instruction": instruction
                        })

                # 处理费用为0的情况
                cost_display = cost
                cost_text = f"¥{cost}" if cost > 0 else "具体费用请联系运营商"

                print(f"[高德API] ✅ 获取到公交路线: 时间={formatted_duration}, 费用={cost_text}, 步行距离={walking_distance}米")
                return {
                    "status": "success",
                    "duration": formatted_duration,
                    "duration_value": duration,
                    "raw_duration_seconds": duration,
                    "cost": cost_display,
                    "cost_text": cost_text,  # 添加格式化的费用文本
                    "walking_distance": walking_distance,
                    "steps": transit_steps,
                    "transfer_count": len([s for s in transit_steps if s["type"] == "transit"]) - 1  # 换乘次数
                }
            else:
                error_msg = "未找到公交路线"
                print(f"[高德API] ❌ {error_msg}")
                return {"status": "error", "message": error_msg}
        else:
            error_msg = data.get("info", "未知错误")
            print(f"[高德API] ❌ 获取公交路线失败: {error_msg}")
            return {"status": "error", "message": error_msg}

    except Exception as e:
        print(f"[高德API] ❌ 请求公交路线时出错: {str(e)}")
        print(traceback.format_exc())
        return {"status": "error", "message": str(e)}