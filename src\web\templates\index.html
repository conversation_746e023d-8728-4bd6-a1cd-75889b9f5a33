<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行程智能规划助手 - AI Trip Plan</title>
    
    <!-- 外部资源 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://unpkg.com/choices.js/public/assets/styles/choices.min.css" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&family=Noto+Sans+SC:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- 自定义样式与动画 -->
    <style>
        body { 
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f4f8;
            background-image: radial-gradient(circle at top left, #e0e7ff 30%, transparent 60%),
                              radial-gradient(circle at bottom right, #d1fae5 40%, transparent 70%);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .font-serif { font-family: 'Noto Serif SC', serif; }
        
        /* 页面切换动画 */
        .content-fade-out {
            animation: fade-out 0.4s ease-out forwards;
        }
        @keyframes fade-out {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-20px); }
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.65);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        }

        /* Choices.js 自定义样式 */
        .choices__inner {
            background-color: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(209, 213, 219, 0.7);
            border-radius: 0.75rem; /* rounded-xl */
            padding: 0.6rem 1rem;
            min-height: 52px;
        }
        .choices__list--dropdown {
            background-color: #ffffff;
            border-radius: 0.75rem;
        }
        .choices__list--dropdown .choices__item--selectable.is-highlighted {
            background-color: #4f46e5;
        }
        .choices[data-type*="select-one"]::after {
            right: 1.25rem;
            border-color: #6b7280 transparent transparent;
        }
        .choices__input {
             background-color: transparent !important;
        }

        /* 自定义输入框样式 */
        .custom-input {
            background-color: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(209, 213, 219, 0.7);
            border-radius: 0.75rem; /* rounded-xl */
            padding: 0.75rem 1rem;
            min-height: 52px;
            width: 100%;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }
        .custom-input:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.4);
        }

        /* 全新加载动画: 多彩行程罗盘 */
        .loader-container {
            position: relative;
            width: 120px;
            height: 120px;
        }
        .loader-ring {
            position: absolute;
            border-radius: 50%;
            border-style: solid;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .ring1 {
            inset: 0;
            border-width: 8px;
            border-color: #6366f1 transparent transparent transparent; /* Indigo 500 */
            animation-duration: 1.2s;
        }
        .ring2 {
            inset: 15px;
            border-width: 6px;
            border-color: transparent #14b8a6 transparent transparent; /* Teal 500 */
            animation-direction: reverse;
            animation-duration: 1.5s;
        }
        .ring3 {
            inset: 30px;
            border-width: 4px;
            border-color: transparent transparent #a855f7 transparent; /* Purple 500 */
            animation-duration: 1.8s;
        }
        
        /* 高德地图自动补全下拉菜单样式 */
        .amap-sug-result {
            border-radius: 0.75rem !important;
            margin-top: 4px !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
        }

        /* 地址选择成功的样式 */
        .address-selected {
            border-color: #10b981 !important;
            background-color: #f0fdf4 !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }

        /* 地址选择成功后的图标 */
        .address-selected::after {
            content: '✓';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            font-weight: bold;
            font-size: 16px;
        }

        /* 移除出发地输入框的选择后图标，改用JS控制的按钮图标 */
        #departure-input.address-selected::after {
            content: none;
        }

        /* 定位按钮样式 */
        #location-btn {
            transition: all 0.3s ease;
        }

        #location-btn i {
            font-size: 1.125rem; /* text-lg */
        }

        #location-btn:hover {
            transform: scale(1.1);
        }

        #location-btn:active {
            transform: scale(1);
        }

        /* 个性化设置按钮样式 */
        #open-personalization {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        #open-personalization:hover {
            transform: scale(1.1);
        }

        /* 个性化弹窗动画 */
        #personalization-modal {
            animation: fadeIn 0.3s ease-out;
        }

        #personalization-modal .bg-white {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .amap-sug-result .auto-item {
            padding: 8px 12px !important;
            font-family: 'Noto Sans SC', sans-serif !important;
        }
        .amap-sug-result .auto-item.active {
            background-color: #4f46e5 !important;
            color: white !important;
        }
    </style>
</head>
<body class="antialiased text-gray-800">

    <!-- 个性化设置按钮 -->
    <div class="fixed top-4 right-4 z-40">
        <button id="open-personalization" class="bg-white shadow-lg rounded-full p-3 hover:shadow-xl transition-all duration-300 border border-gray-200 group" title="个性化设置">
            <i class="fas fa-user-cog text-gray-600 group-hover:text-indigo-600 transition-colors"></i>
        </button>
    </div>

    <!-- 主内容区 -->
    <main class="min-h-screen flex items-center justify-center p-4 transition-all duration-500" id="main-content">
        <div class="w-full max-w-2xl text-center">
            
            <!-- Hero 模块 -->
            <header class="mb-10">
                <h1 class="text-5xl md:text-6xl font-bold font-serif bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-transparent bg-clip-text mb-4">
                    AI 智慧之旅
                </h1>
                <p class="text-xl text-gray-600">输入您的需求，即刻开启专属行程规划</p>
            </header>
            
            <!-- 规划表单 -->
            <form id="plan-form" action="/plan_langgraph" method="post" class="glass-card p-8 rounded-2xl shadow-xl">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6">
                    <div>
                        <label for="departure-input" class="block text-left text-sm font-medium text-gray-700 mb-2">出发地点</label>
                        <div class="relative flex items-center">
                            <input id="departure-input" type="text" placeholder="输入或点击右侧图标定位" class="custom-input w-full pr-10" autocomplete="off" />
                            <input type="hidden" id="departure" name="departure" required>
                            <input type="hidden" id="departure_lng" name="departure_lng">
                            <input type="hidden" id="departure_lat" name="departure_lat">
                            <button type="button" id="location-btn" title="使用当前位置" class="absolute inset-y-0 right-0 flex items-center justify-center w-10 text-gray-400 hover:text-indigo-600 transition-colors duration-200">
                                <i class="fas fa-location-crosshairs"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">💡 请从下拉列表中选择具体地点</p>
                    </div>
                    <div>
                        <label for="arrival-input" class="block text-left text-sm font-medium text-gray-700 mb-2">目的地点</label>
                        <div class="relative">
                            <input id="arrival-input" type="text" placeholder="输入目的地点，系统将自动匹配" class="custom-input" autocomplete="off" />
                            <input type="hidden" id="arrival" name="arrival" required>
                            <input type="hidden" id="arrival_lng" name="arrival_lng">
                            <input type="hidden" id="arrival_lat" name="arrival_lat">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">💡 请从下拉列表中选择具体地点</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="date" class="block text-left text-sm font-medium text-gray-700 mb-2">出发日期</label>
                        <input type="date" id="date" name="date" value="{{ default_date }}" required class="custom-input transition">
                    </div>
                    <div>
                        <label for="criteria" class="block text-left text-sm font-medium text-gray-700 mb-2">规划策略</label>
                        <select id="criteria" name="criteria" required class="custom-input transition">
                            <option value="综合最优">综合最优 ✨</option>
                            <option value="最便宜">价格最便宜 💰</option>
                            <option value="最快">时间最快 🚀</option>
                            <option value="最贵">体验最豪华 💎</option>
                        </select>
                    </div>
                </div>
                
                <!-- 新增的LangGraph多智能体模式选择 -->
                <div class="mb-8">
                    <label for="api_type" class="block text-left text-sm font-medium text-gray-700 mb-2">处理模式</label>
                    <div class="grid grid-cols-1 gap-3">
                        <div class="flex items-center">
                            <input id="langgraph_api" name="api_type" type="radio" value="langgraph" checked
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                   onchange="document.getElementById('plan-form').action = '/plan_langgraph'">
                            <label for="langgraph_api" class="ml-2 block text-sm text-gray-700">
                                <span class="flex items-center">
                                    多智能体模式 <span class="ml-1 px-1.5 py-0.5 text-xs rounded bg-indigo-100 text-indigo-800">New</span>
                                </span>
                            </label>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">多智能体模式支持更高级的行程规划，包括精确的小交通规划和时间轴计算</p>
                </div>
                
                <button type="submit" id="submit-button" class="w-full bg-gradient-to-r from-blue-600 to-violet-600 text-white font-bold py-3 px-5 rounded-xl hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-lg">
                    <i class="fas fa-magic mr-2"></i> 开始规划
                </button>
            </form>
        </div>
    </main>
    
    <!-- 个性化匹配弹窗 -->
    <div id="personalization-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <!-- 弹窗头部 -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-4 md:p-6 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl md:text-2xl font-bold mb-2">🎯 个性化匹配</h2>
                        <p class="text-indigo-100 text-sm md:text-base">告诉我们您的偏好，为您量身定制最佳行程方案</p>
                    </div>
                    <button id="close-modal" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-xl md:text-2xl"></i>
                    </button>
                </div>
            </div>

            <!-- 弹窗内容 - 横向布局 -->
            <div class="p-4 md:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
                    <!-- 问题1：无法接受的情况 -->
                    <div class="space-y-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-800 flex items-center">
                            <span class="bg-red-100 text-red-600 w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs md:text-sm font-bold mr-2 md:mr-3">1</span>
                            下哪种情况，是您绝对无法接受的？
                        </h3>
                        <div class="space-y-2 md:space-y-3 ml-8 md:ml-11">
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="unacceptable" value="red-eye" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">为了赶时间，乘坐体验不佳的红眼航班</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="unacceptable" value="miss-early" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">为了图安逸，错过更早到达的机会</span>
                            </label>
                        </div>
                    </div>

                    <!-- 问题2：规划策略 -->
                    <div class="space-y-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-800 flex items-center">
                            <span class="bg-blue-100 text-blue-600 w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs md:text-sm font-bold mr-2 md:mr-3">2</span>
                            对于交通方式的选择，您首选的规划策略是？
                        </h3>
                        <div class="space-y-2 md:space-y-3 ml-8 md:ml-11">
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="strategy" value="comprehensive" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">综合最优 ✨</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="strategy" value="price" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">价格最便宜 💰</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="strategy" value="time" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">时间最快 🚀</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="strategy" value="comfort" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">体验最舒适 💎</span>
                            </label>
                        </div>
                    </div>

                    <!-- 问题3：酒店选择 -->
                    <div class="space-y-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-800 flex items-center">
                            <span class="bg-green-100 text-green-600 w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs md:text-sm font-bold mr-2 md:mr-3">3</span>
                            选择酒店时，您的第一顺位考虑是？
                        </h3>
                        <div class="space-y-2 md:space-y-3 ml-8 md:ml-11">
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="hotel" value="location" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">位置，位置，还是位置</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="hotel" value="brand" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">可靠的品牌和品质</span>
                            </label>
                        </div>
                    </div>

                    <!-- 问题4：费用范围 -->
                    <div class="space-y-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-800 flex items-center">
                            <span class="bg-purple-100 text-purple-600 w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs md:text-sm font-bold mr-2 md:mr-3">4</span>
                            出行常规的费用范围：
                        </h3>
                        <div class="space-y-2 md:space-y-3 ml-8 md:ml-11">
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="budget" value="1000" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">1000以内</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="budget" value="5000" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">5000以内</span>
                            </label>
                            <label class="flex items-center p-2 md:p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="radio" name="budget" value="comfort" class="mr-2 md:mr-3 text-indigo-600">
                                <span class="text-sm md:text-base text-gray-700">舒适最重要</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="bg-gray-50 px-6 py-4 rounded-b-2xl flex justify-between items-center">
                <button id="skip-personalization" class="text-gray-500 hover:text-gray-700 transition-colors">
                    跳过设置
                </button>
                <button id="save-personalization" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors font-medium">
                    保存偏好
                </button>
            </div>
        </div>
    </div>

    <!-- 加载浮层 (全新设计) -->
    <div id="loading-overlay" class="fixed inset-0 bg-gray-50 bg-opacity-90 z-50 flex-col hidden items-center justify-center transition-opacity duration-500" style="backdrop-filter: blur(5px);">
        <div class="loader-container mb-6">
            <div class="loader-ring ring1"></div>
            <div class="loader-ring ring2"></div>
            <div class="loader-ring ring3"></div>
            <i class="fas fa-route text-indigo-500 text-3xl absolute top-1/2 left-1/2" style="transform: translate(-50%, -50%);"></i>
        </div>
        <h2 class="text-3xl font-semibold font-serif text-gray-800">AI 规划中...</h2>
        <p class="mt-2 text-gray-600">正在为您量身定制最佳行程，请稍候</p>
    </div>

    <!-- 高德地图API安全密钥配置 -->
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: 'b3b88a9d6688e1f64aa6b34c57391869', // 安全密钥
        };
    </script>
    
    <!-- 引入高德地图API和插件 -->
    <script src="https://cdn.jsdelivr.net/npm/@amap/amap-jsapi-loader@1.0.1/dist/index.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 个性化匹配弹窗逻辑
            const personalizationModal = document.getElementById('personalization-modal');
            const closeModalBtn = document.getElementById('close-modal');
            const skipPersonalizationBtn = document.getElementById('skip-personalization');
            const savePersonalizationBtn = document.getElementById('save-personalization');
            const openPersonalizationBtn = document.getElementById('open-personalization');
            const strategySelect = document.getElementById('criteria'); // 修正：应该是criteria而不是strategy

            // 检查是否已经设置过个性化偏好
            const hasPersonalization = localStorage.getItem('user_personalization');
            if (!hasPersonalization) {
                // 延迟1秒显示弹窗，让页面先加载完成
                setTimeout(() => {
                    personalizationModal.classList.remove('hidden');
                }, 1000);
            } else {
                // 恢复已保存的个性化偏好
                restorePersonalizationPreferences();
            }

            // 恢复个性化偏好到页面
            function restorePersonalizationPreferences() {
                try {
                    const savedPreferences = JSON.parse(localStorage.getItem('user_personalization'));
                    if (savedPreferences && !savedPreferences.skipped) {
                        // 恢复规划策略到主页下拉框
                        if (savedPreferences.strategy) {
                            const strategyMapping = {
                                'comprehensive': '综合最优',
                                'price': '最便宜',
                                'time': '最快',
                                'comfort': '最贵'
                            };
                            const mappedValue = strategyMapping[savedPreferences.strategy];
                            if (mappedValue && strategySelect) {
                                strategySelect.value = mappedValue;
                                console.log('已恢复规划策略:', mappedValue);
                            }
                        }
                    }
                } catch (e) {
                    console.error('恢复个性化偏好失败:', e);
                }
            }

            // 关闭弹窗
            function closePersonalizationModal() {
                personalizationModal.classList.add('hidden');
            }

            closeModalBtn.addEventListener('click', closePersonalizationModal);
            openPersonalizationBtn.addEventListener('click', function() {
                personalizationModal.classList.remove('hidden');
            });
            skipPersonalizationBtn.addEventListener('click', function() {
                localStorage.setItem('user_personalization', JSON.stringify({ skipped: true }));
                closePersonalizationModal();
            });

            // 保存个性化偏好
            savePersonalizationBtn.addEventListener('click', function() {
                const preferences = {
                    unacceptable: document.querySelector('input[name="unacceptable"]:checked')?.value,
                    strategy: document.querySelector('input[name="strategy"]:checked')?.value,
                    hotel: document.querySelector('input[name="hotel"]:checked')?.value,
                    budget: document.querySelector('input[name="budget"]:checked')?.value,
                    timestamp: new Date().toISOString()
                };

                // 保存到localStorage
                localStorage.setItem('user_personalization', JSON.stringify(preferences));

                // 如果选择了规划策略，同步到主页下拉框
                if (preferences.strategy) {
                    const strategyMapping = {
                        'comprehensive': '综合最优',
                        'price': '最便宜',
                        'time': '最快',
                        'comfort': '最贵'
                    };
                    const mappedValue = strategyMapping[preferences.strategy];
                    if (mappedValue && strategySelect) {
                        strategySelect.value = mappedValue;
                        console.log('已同步规划策略:', mappedValue);
                    }
                }

                // 显示保存成功提示
                savePersonalizationBtn.innerHTML = '<i class="fas fa-check mr-2"></i>已保存';
                savePersonalizationBtn.classList.add('bg-green-600');
                savePersonalizationBtn.classList.remove('bg-indigo-600');

                // 2秒后关闭弹窗
                setTimeout(() => {
                    closePersonalizationModal();
                }, 2000);
            });

            // 页面切换动画逻辑
            const form = document.getElementById('plan-form');
            const loadingOverlay = document.getElementById('loading-overlay');
            const mainContent = document.getElementById('main-content');
            const submitButton = document.getElementById('submit-button');
            const departureInput = document.getElementById('departure-input');
            const arrivalInput = document.getElementById('arrival-input');
            const departureLngInput = document.getElementById('departure_lng');
            const departureLatInput = document.getElementById('departure_lat');
            const arrivalLngInput = document.getElementById('arrival_lng');
            const arrivalLatInput = document.getElementById('arrival_lat');
            const departureNameInput = document.getElementById('departure');
            const arrivalNameInput = document.getElementById('arrival');
            const locationBtn = document.getElementById('location-btn');
            
            // 高德地图API配置
            const API_KEY = '0a3bb21454092a806107a7c097f2e001'; // Web端Key
            
            // 加载高德地图API
            AMapLoader.load({
                key: API_KEY,
                version: "2.0",
                plugins: ['AMap.AutoComplete', 'AMap.Geocoder', 'AMap.Geolocation'],
            }).then((AMap) => {
                console.log('高德地图API加载成功!');
                
                // 初始化地理编码服务
                const geocoder = new AMap.Geocoder({
                    city: "全国", // 城市，默认全国
                });
                
                // 初始化定位服务
                const geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true, // 是否使用高精度定位
                    timeout: 10000, // 超时时间
                    zoomToAccuracy: false, // 是否自动调整地图视野到定位点
                });
                
                // 初始化自动完成插件 (出发地)
                const autoCompleteDeparture = new AMap.AutoComplete({
                    input: "departure-input",
                    outPutDirAuto: true
                });

                // 初始化自动完成插件 (目的地)
                const autoCompleteArrival = new AMap.AutoComplete({
                    input: "arrival-input",
                    outPutDirAuto: true
                });

                // 用户选择状态跟踪
                let departureSelected = false;
                let arrivalSelected = false;

                // 重置选择状态的辅助函数
                function setupInputValidation(autoComplete, inputElement, nameInput, lngInput, latInput, isDepature) {
                    inputElement.addEventListener('input', function() {
                        // 用户开始输入新内容时，重置选择状态
                        if (isDepature) {
                            departureSelected = false;
                        } else {
                            arrivalSelected = false;
                        }

                        // 清除之前的坐标
                        lngInput.value = '';
                        latInput.value = '';
                        nameInput.value = '';
                        inputElement.style.borderColor = ''; // 重置边框颜色
                        inputElement.style.backgroundColor = ''; // 重置背景颜色

                        // 移除选择成功的样式
                        inputElement.classList.remove('address-selected');
                        
                        // 如果是出发地输入框，重置图标
                        if (isDepature) {
                            const icon = locationBtn.querySelector('i');
                            icon.className = 'fas fa-location-crosshairs text-gray-400';
                        }
                    });
                }

                // 为出发地和目的地输入框启用输入验证
                setupInputValidation(autoCompleteDeparture, departureInput, departureNameInput, departureLngInput, departureLatInput, true);
                setupInputValidation(autoCompleteArrival, arrivalInput, arrivalNameInput, arrivalLngInput, arrivalLatInput, false);
                
                // 监听出发地自动完成选择事件
                autoCompleteDeparture.on('select', function(e) {
                    const poi = e.poi;
                    console.log('选择的出发地:', poi);
                    
                    const handleSuccess = () => {
                        departureSelected = true;
                        departureInput.classList.add('address-selected');
                        const icon = locationBtn.querySelector('i');
                        icon.className = 'fas fa-check-circle text-green-500';
                        
                        // 3秒后可以重置回定位图标，给用户修改机会
                        setTimeout(() => {
                            if (departureSelected) { // 检查状态是否仍然是已选择
                                icon.className = 'fas fa-location-crosshairs text-gray-400';
                            }
                        }, 3000);
                    };

                    if (poi && poi.location) {
                        departureNameInput.value = poi.name;
                        departureLngInput.value = poi.location.lng;
                        departureLatInput.value = poi.location.lat;
                        handleSuccess();
                        console.log('出发地坐标已设置:', poi.location.lng, poi.location.lat);
                    } else if (poi && poi.name) {
                        // 如果返回的POI没有location，则使用地理编码服务获取坐标
                        geocoder.getLocation(poi.name, function(status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                const location = result.geocodes[0].location;
                                departureNameInput.value = poi.name;
                                departureLngInput.value = location.lng;
                                departureLatInput.value = location.lat;
                                handleSuccess();
                                console.log('出发地坐标(通过地理编码获取):', location.lng, location.lat);
                            } else {
                                console.error('地理编码失败');
                                alert('无法获取该地点的坐标，请选择更具体的地址');
                            }
                        });
                    }
                });
                
                // 监听目的地自动完成选择事件
                autoCompleteArrival.on('select', function(e) {
                    const poi = e.poi;
                    console.log('选择的目的地:', poi);
                    if (poi && poi.location) {
                        arrivalNameInput.value = poi.name;
                        arrivalLngInput.value = poi.location.lng;
                        arrivalLatInput.value = poi.location.lat;
                        arrivalSelected = true; // 标记为已选择
                        arrivalInput.classList.add('address-selected');
                        console.log('目的地坐标已设置:', poi.location.lng, poi.location.lat);
                    } else if (poi && poi.name) {
                        // 如果返回的POI没有location，则使用地理编码服务获取坐标
                        geocoder.getLocation(poi.name, function(status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                const location = result.geocodes[0].location;
                                arrivalNameInput.value = poi.name;
                                arrivalLngInput.value = location.lng;
                                arrivalLatInput.value = location.lat;
                                arrivalSelected = true; // 标记为已选择
                                arrivalInput.classList.add('address-selected');
                                console.log('目的地坐标(通过地理编码获取):', location.lng, location.lat);
                            } else {
                                console.error('地理编码失败');
                                alert('无法获取该地点的坐标，请选择更具体的地址');
                            }
                        });
                    }
                });
                
                // 浏览器定位按钮点击事件
                locationBtn.addEventListener('click', function() {
                    const icon = locationBtn.querySelector('i');

                    // 更新按钮状态为加载中
                    icon.className = 'fas fa-spinner fa-spin';
                    locationBtn.disabled = true;

                    geolocation.getCurrentPosition(function(status, result) {
                        locationBtn.disabled = false;
                        
                        if (status === 'complete') {
                            const position = result.position;
                            console.log('当前位置:', position);
                            
                            // 使用逆地理编码服务将坐标转换为地址
                            geocoder.getAddress(position, function(status, result) {
                                if (status === 'complete' && result.info === 'OK') {
                                    const address = result.regeocode;
                                    const formatted = address.formattedAddress;
                                    
                                    console.log('当前地址:', formatted);
                                    
                                    // 填充出发地输入框
                                    departureInput.value = formatted;
                                    departureNameInput.value = formatted;
                                    departureLngInput.value = position.lng;
                                    departureLatInput.value = position.lat;

                                    // 标记为已选择并添加样式
                                    departureSelected = true;
                                    departureInput.classList.add('address-selected');

                                    // 更新定位按钮为成功状态
                                    icon.className = 'fas fa-check-circle text-green-500';

                                    // 3秒后恢复原状态
                                    setTimeout(() => {
                                        icon.className = 'fas fa-location-crosshairs text-gray-400';
                                    }, 3000);
                                } else {
                                    console.error('逆地理编码失败');
                                    alert('无法获取您当前的地址信息，请手动输入');
                                    // 更新按钮为失败状态
                                    icon.className = 'fas fa-times-circle text-red-500';
                                    // 3秒后恢复原状态
                                    setTimeout(() => {
                                        icon.className = 'fas fa-location-crosshairs text-gray-400';
                                    }, 3000);
                                }
                            });
                        } else {
                            console.error('定位失败');
                            alert('无法获取您的位置，请手动输入出发地点');
                            // 更新按钮为失败状态
                            icon.className = 'fas fa-times-circle text-red-500';
                            // 3秒后恢复原状态
                            setTimeout(() => {
                                icon.className = 'fas fa-location-crosshairs text-gray-400';
                            }, 3000);
                        }
                    });
                });
                
                // 表单提交前验证
            form.addEventListener('submit', function(event) {
                    if (!departureInput.value.trim() || !arrivalInput.value.trim()) {
                        event.preventDefault();
                        alert('请输入出发地点和目的地点');
                        return;
                    }

                    // 检查用户是否从下拉列表中选择了地址
                    if (!departureSelected) {
                        event.preventDefault();
                        alert('请从下拉列表中选择出发地点');
                        // 聚焦到出发地输入框并触发搜索
                        departureInput.focus();
                        if (departureInput.value.trim().length >= 2) {
                            autoCompleteDeparture.search(departureInput.value.trim());
                        }
                        return;
                    }

                    if (!arrivalSelected) {
                        event.preventDefault();
                        alert('请从下拉列表中选择目的地点');
                        // 聚焦到目的地输入框并触发搜索
                        arrivalInput.focus();
                        if (arrivalInput.value.trim().length >= 2) {
                            autoCompleteArrival.search(arrivalInput.value.trim());
                        }
                        return;
                    }

                    // 打印坐标值，用于调试
                    console.log('出发地坐标:', departureLngInput.value, departureLatInput.value);
                    console.log('目的地坐标:', arrivalLngInput.value, arrivalLatInput.value);

                    // 最后检查坐标是否存在
                    if (!departureLngInput.value || !departureLatInput.value) {
                        event.preventDefault();
                        alert('出发地坐标信息缺失，请重新选择');
                        return;
                    }

                    if (!arrivalLngInput.value || !arrivalLatInput.value) {
                        event.preventDefault();
                        alert('目的地坐标信息缺失，请重新选择');
                        return;
                    }
                    
                // 禁用按钮防止重复提交
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';

                // 显示加载浮层
                loadingOverlay.classList.remove('hidden');
                loadingOverlay.classList.add('flex');
                
                // 淡出主内容
                mainContent.classList.add('content-fade-out');
                
                // 根据选择的API类型设置表单action
                const apiTypeRadios = document.querySelectorAll('input[name="api_type"]');
                let actionUrl = '/plan'; // 默认action
                apiTypeRadios.forEach(radio => {
                    if (radio.checked) {
                        actionUrl = radio.value === 'langgraph' ? '/plan_langgraph' : '/plan';
                    }
                });
                form.action = actionUrl;
                
                });
            }).catch(e => {
                console.error("高德地图API加载失败:", e);
                alert("高德地图API加载失败，可能影响地点选择功能");
            });
        });
    </script>
</body>
</html> 