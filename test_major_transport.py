#!/usr/bin/env python3
"""
测试major_transport_node是否能正常执行
"""

import sys
import os
sys.path.append(os.path.abspath('.'))

from src.agent.nodes import major_transport_node
from src.agent.state import ItineraryState

def test_major_transport_node():
    """测试major_transport_node函数"""
    print("=" * 60)
    print("🧪 测试major_transport_node函数")
    print("=" * 60)
    
    # 创建测试状态
    test_state = {
        "departure_place": "广东省珠海市香洲区前山街道前河北路珠海环宇城",
        "arrival_place": "上海浦东国际机场2号航站楼",
        "departure_coordinates": {"lng": 113.524498, "lat": 22.233447},
        "arrival_coordinates": {"lng": 121.809243, "lat": 31.151128},
        "travel_date": "2025-07-26",
        "criteria": "综合最优"
    }
    
    print(f"📋 测试状态:")
    for key, value in test_state.items():
        print(f"   {key}: {value}")
    print()
    
    try:
        print("🚀 调用major_transport_node...")
        result = major_transport_node(test_state)
        print("✅ 函数调用成功!")
        print(f"📤 返回结果类型: {type(result)}")
        print(f"📤 返回结果: {result}")
        
    except Exception as e:
        print(f"❌ 函数调用失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_major_transport_node()
