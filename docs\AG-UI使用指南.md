# AG-UI 智能行程规划助手使用指南

## 概述

本项目已成功集成 AG-UI 协议，提供符合标准的事件驱动智能体交互体验。AG-UI（Agent User Interaction Protocol）是一个标准化的协议，用于前端应用与AI服务之间的实时通信。

## 新增功能

### 1. AG-UI 协议支持
- ✅ 16种标准化事件类型完整实现
- ✅ 实时流式事件传输
- ✅ 工具调用事件化
- ✅ 状态增量更新
- ✅ 多智能体协作事件流

### 2. 实时流式界面
- 🌊 Server-Sent Events (SSE) 流式传输
- 🤖 智能体状态实时显示
- 🔧 工具调用过程可视化
- 💭 思考过程流式输出
- 📊 事件流实时监控

## 使用方法

### 启动服务器

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务器
python main.py web
```

### 访问AG-UI界面

1. **传统界面**: http://localhost:8000/
2. **AG-UI界面**: http://localhost:8000/agui

### AG-UI界面功能

#### 实时事件流显示
- 显示所有AG-UI标准事件
- 生命周期事件（RUN_STARTED, STEP_STARTED等）
- 文本消息事件（思考过程流式输出）
- 工具调用事件（API调用过程）
- 状态管理事件（增量更新）

#### 智能体状态监控
- 规划师状态
- 大交通专家状态
- 小交通专家状态
- 综合报告专家状态

#### 工具调用可视化
- 火车查询工具
- 高德地图API工具
- 天气查询工具
- 参数和结果实时显示

## API 端点

### AG-UI 标准端点

#### POST /agent
标准AG-UI代理端点，接受完整的RunAgentInput格式：

```json
{
  "threadId": "thread_xxx",
  "runId": "run_xxx", 
  "messages": [
    {
      "id": "msg_xxx",
      "role": "user",
      "content": "我想从北京到上海旅行"
    }
  ],
  "context": [],
  "tools": [],
  "state": {}
}
```

#### POST /agent/chat
简化的聊天端点：

```json
{
  "message": "我想从北京到上海旅行"
}
```

#### POST /plan_agui
行程规划专用AG-UI端点：

```json
{
  "query": "我想从北京到上海旅行",
  "departure_coords": {"lng": 116.4074, "lat": 39.9042},
  "arrival_coords": {"lng": 121.4737, "lat": 31.2304}
}
```

## 事件类型说明

### 生命周期事件
- `RUN_STARTED`: 规划开始
- `RUN_FINISHED`: 规划完成
- `RUN_ERROR`: 规划错误
- `STEP_STARTED`: 智能体开始工作
- `STEP_FINISHED`: 智能体完成工作

### 文本消息事件
- `TEXT_MESSAGE_START`: 开始输出文本
- `TEXT_MESSAGE_CONTENT`: 文本内容块
- `TEXT_MESSAGE_END`: 文本输出结束

### 工具调用事件
- `TOOL_CALL_START`: 开始调用工具
- `TOOL_CALL_ARGS`: 工具参数
- `TOOL_CALL_END`: 工具调用结束
- `TOOL_CALL_RESULT`: 工具调用结果

### 状态管理事件
- `STATE_SNAPSHOT`: 完整状态快照
- `STATE_DELTA`: 状态增量更新
- `MESSAGES_SNAPSHOT`: 消息历史快照

## 开发指南

### 添加新的AG-UI兼容节点

1. 在 `src/agent/agui_nodes.py` 中创建新节点：

```python
def agui_new_node(state: ItineraryState) -> ItineraryState:
    if not _global_integration:
        # 回退到原始实现
        from src.agent.nodes import original_new_node
        return original_new_node(state)
    
    integration = _global_integration
    
    # 发送步骤开始事件
    integration.stream_manager.start_step("new_node")
    
    try:
        # 节点逻辑
        thoughts = ["开始新节点处理..."]
        
        # 发送思考过程
        integration._emit_thinking_process("新节点", thoughts)
        
        # 更新状态
        state["new_field"] = "new_value"
        
        # 发送状态增量
        integration._emit_state_delta({}, state)
        
        # 发送步骤完成事件
        integration.stream_manager.finish_step("new_node")
        
        return state
        
    except Exception as e:
        integration.stream_manager.error_run(f"新节点执行失败: {str(e)}")
        raise
```

2. 在 `src/agent/agui_graph.py` 中添加节点到图谱：

```python
workflow.add_node("new_node", agui_new_node)
```

### 添加新的AG-UI工具

1. 在 `src/ag_ui/tool_events.py` 中创建工具处理器：

```python
def create_agui_new_tool(stream_manager: AGUIStreamManager):
    from src.tools.new_tool import new_tool_function
    
    handler = ToolEventHandler(stream_manager, "new_tool")
    
    def agui_new_tool(*args, **kwargs):
        with handler.tool_call_context(*args, **kwargs):
            result = new_tool_function(*args, **kwargs)
            handler._emit_success_result(result)
            return result
    
    return agui_new_tool
```

2. 在工具注册表中添加：

```python
tools["new_tool"] = create_agui_new_tool(stream_manager)
```

## 测试

### 运行AG-UI集成测试

```bash
python test_agui_integration.py
```

### 测试覆盖范围
- ✅ 事件系统测试
- ✅ 流管理器测试
- ✅ 工具包装器测试
- ✅ FastAPI集成测试
- ✅ 端到端流程测试

## 兼容性

### 向后兼容
- 原有的非AG-UI端点继续工作
- 原有的LangGraph节点保持不变
- 可以选择性启用AG-UI功能

### 前端兼容
- 支持传统轮询模式
- 支持AG-UI实时事件流
- 自动降级机制

## 故障排除

### 常见问题

1. **事件流中断**
   - 检查网络连接
   - 确认服务器支持SSE
   - 查看浏览器控制台错误

2. **工具调用失败**
   - 检查API密钥配置
   - 确认工具包装器正确初始化
   - 查看服务器日志

3. **状态同步问题**
   - 确认JSON Patch格式正确
   - 检查状态字段序列化
   - 验证增量更新逻辑

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

查看事件流：

```javascript
// 在浏览器控制台中
console.log('AG-UI事件:', event);
```

## 性能优化

### 事件流优化
- 事件批处理
- 内容分块传输
- 连接池管理

### 状态管理优化
- 增量更新
- 状态压缩
- 缓存策略

## 扩展性

### 自定义事件
可以使用CUSTOM事件类型扩展协议：

```python
custom_event = CustomEvent(
    name="route_visualization",
    value={"route_data": route_info}
)
```

### 多智能体扩展
添加新的专家智能体：

1. 创建AG-UI兼容节点
2. 添加到图谱
3. 配置路由逻辑
4. 更新前端显示

## 总结

AG-UI协议的集成为智能行程规划助手带来了：

- 🚀 **实时性**: 流式事件传输，即时反馈
- 🔧 **标准化**: 符合AG-UI协议规范
- 🤖 **可视化**: 智能体工作过程透明
- 🔄 **兼容性**: 保持现有功能完整
- 📈 **扩展性**: 易于添加新功能

通过AG-UI协议，用户可以实时观察AI智能体的思考过程、工具调用和决策流程，获得更加透明和交互式的AI体验。
