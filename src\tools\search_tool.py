# tools/search_tool.py
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
import traceback

# 定义输入模式
class WeatherInput(BaseModel):
    city: str = Field(..., description="城市名称，例如\"北京\"、\"上海\"等")

# 使用继承BaseTool的方式定义工具
class WeatherTool(BaseTool):
    name: str = "get_weather"
    description: str = "获取指定城市的当前天气"
    args_schema: type[WeatherInput] = WeatherInput
    
    def _run(self, city: str) -> Dict[str, Any]:
        """获取指定城市的当前天气。"""
        print(f"[工具] 🌤️ 正在获取 {city} 的天气信息...")
        try:
            # 参数验证
            if not city or not isinstance(city, str):
                return {"status": "error", "message": "城市名称不能为空且必须是字符串"}
            
            # 这是一个简化版的天气API实现，实际开发中需要调用真实的天气API
            weather_data = {
                "北京": {"condition": "晴天", "temperature": 25, "wind": "微风", "humidity": 45},
                "上海": {"condition": "多云", "temperature": 22, "wind": "微风", "humidity": 60},
                "广州": {"condition": "小雨", "temperature": 27, "wind": "微风", "humidity": 75},
                "深圳": {"condition": "阵雨", "temperature": 26, "wind": "微风", "humidity": 70},
                "杭州": {"condition": "晴天", "temperature": 24, "wind": "微风", "humidity": 50},
                "南京": {"condition": "多云", "temperature": 23, "wind": "微风", "humidity": 55},
                "武汉": {"condition": "晴天", "temperature": 26, "wind": "微风", "humidity": 45},
                "成都": {"condition": "阴天", "temperature": 20, "wind": "微风", "humidity": 65},
                "重庆": {"condition": "多云", "temperature": 22, "wind": "微风", "humidity": 60},
                "西安": {"condition": "晴天", "temperature": 22, "wind": "微风", "humidity": 40},
                "天津": {"condition": "晴天", "temperature": 23, "wind": "微风", "humidity": 45},
                "苏州": {"condition": "多云", "temperature": 21, "wind": "微风", "humidity": 60},
                "厦门": {"condition": "晴天", "temperature": 25, "wind": "微风", "humidity": 65},
                "青岛": {"condition": "多云", "temperature": 20, "wind": "微风", "humidity": 70},
                "大连": {"condition": "晴天", "temperature": 19, "wind": "微风", "humidity": 50},
                "珠海": {"condition": "多云", "temperature": 26, "wind": "微风", "humidity": 65},
                "安阳": {"condition": "晴天", "temperature": 23, "wind": "微风", "humidity": 55},
            }
            
            # 尝试匹配城市名称，支持模糊匹配
            exact_match = None
            partial_matches = []
            
            # 先尝试精确匹配，再尝试部分匹配
            for key in weather_data:
                if key == city:
                    exact_match = key
                    break
                elif key in city or city in key:
                    partial_matches.append(key)
            
            if exact_match:
                result = weather_data[exact_match]
                print(f"[工具] ✅ 已找到 {exact_match} 的天气信息")
                return {
                    "status": "success",
                    "data": {
                        "city": exact_match,
                        "condition": result["condition"],
                        "temperature": f"{result['temperature']}°C",
                        "wind": result["wind"],
                        "humidity": f"{result['humidity']}%"
                    }
                }
            elif partial_matches:
                best_match = partial_matches[0]
                result = weather_data[best_match]
                print(f"[工具] ✅ 找到最接近的匹配: {best_match}")
                return {
                    "status": "success",
                    "data": {
                        "city": best_match,
                        "condition": result["condition"],
                        "temperature": f"{result['temperature']}°C",
                        "wind": result["wind"],
                        "humidity": f"{result['humidity']}%",
                        "note": f"使用 {best_match} 作为 {city} 的最接近匹配"
                    }
                }
            else:
                available_cities = ", ".join(list(weather_data.keys())[:5]) + "等"
                message = f"无法获取{city}的天气信息，可用城市包括: {available_cities}"
                print(f"[工具] ❌ {message}")
                return {
                    "status": "error", 
                    "message": message
                }
        
        except Exception as e:
            # 捕获并记录所有异常
            error_msg = f"天气查询异常: {str(e)}"
            print(f"[工具] ❌ {error_msg}")
            print(f"[工具] 🔍 异常详情: {traceback.format_exc()}")
            
            return {
                "status": "error", 
                "message": error_msg
            }
    
    async def _arun(self, city: str) -> Dict[str, Any]:
        """异步执行（这里简单地调用同步方法）"""
        return self._run(city)

# 实例化工具
get_weather = WeatherTool()