# agent/graph.py
from typing import Dict, Any

from langgraph.graph import StateGraph, END
from langchain_community.chat_models import ChatZhipuAI

from src.agent.state import ItineraryState
from src.agent.nodes import (
    planner_node,
    major_transport_node,
    local_transport_node,
    web_search_node,
    synthesis_node,
    ask_user_node,
    intelligent_ask_user_node
)
from src.agent.router import (
    route_by_next_step,
    check_user_interaction_needed,
    check_error_condition,
    check_final_state
)

def create_itinerary_graph() -> StateGraph:
    """
    创建行程规划工作流图谱。
    
    这个图谱包含所有专家节点，以及它们之间的条件连接。
    规划师节点（planner_node）是中央控制节点，所有其他节点都会回到它。
    
    返回：
        一个编译好的LangGraph工作流
    """
    print("正在创建行程规划工作流图谱...")
    
    # 创建状态图，使用ItineraryState作为状态类型
    workflow = StateGraph(ItineraryState)
    
    # 添加所有节点
    workflow.add_node("planner", planner_node)
    workflow.add_node("major_transport", major_transport_node)
    workflow.add_node("local_transport", local_transport_node)
    workflow.add_node("web_search", web_search_node)
    workflow.add_node("synthesis", synthesis_node)
    workflow.add_node("ask_user", ask_user_node)
    workflow.add_node("intelligent_ask_user", intelligent_ask_user_node)
    
    # 设置入口点
    workflow.set_entry_point("planner")
    
    # 从规划师节点出发的条件路由
    # 根据next_step决定下一个执行的节点
    workflow.add_conditional_edges(
        "planner",
        route_by_next_step,
        {
            "major_transport": "major_transport",
            "local_transport": "local_transport",
            "web_search": "web_search",
            "synthesis": "synthesis",
            "ask_user": "ask_user",
            "intelligent_ask_user": "intelligent_ask_user",
            "end": END,
            # 默认循环回自身，继续规划
            "planner": "planner"
        }
    )
    
    # 所有专家节点都路由回规划师或者用户交互
    # 大交通专家 -> 规划师或者用户交互
    workflow.add_conditional_edges(
        "major_transport",
        check_user_interaction_needed,
        {
            "pause": "ask_user",
            "continue": "planner"
        }
    )
    
    # 小交通专家 -> 规划师或者用户交互
    workflow.add_conditional_edges(
        "local_transport",
        check_user_interaction_needed,
        {
            "pause": "ask_user",
            "continue": "planner"
        }
    )

    # 网络搜索专家 -> 规划师或者用户交互
    workflow.add_conditional_edges(
        "web_search",
        check_user_interaction_needed,
        {
            "pause": "ask_user",
            "continue": "planner"
        }
    )

    # 整合报告专家 -> 规划师或结束
    workflow.add_conditional_edges(
        "synthesis",
        check_final_state,
        {
            "complete": END,
            "incomplete": "planner"
        }
    )
    
    # 用户交互节点 -> 规划师
    workflow.add_edge("ask_user", "planner")

    # 智能用户交互节点 -> 规划师
    workflow.add_edge("intelligent_ask_user", "planner")
    
    # 编译图谱
    print("图谱创建完成，准备编译...")
    
    # 尝试编译图谱并处理可能的错误
    try:
        print("正在编译图谱，使用recursion_limit=50...")
        compiled_graph = workflow.compile(recursion_limit=50)
        print("图谱编译成功！")
        return compiled_graph
    except Exception as e:
        print(f"图谱编译失败，尝试使用不同参数: {str(e)}")
        try:
            # 尝试使用不同的编译参数
            compiled_graph = workflow.compile(graph_recursion_limit=50)
            print("使用graph_recursion_limit参数编译成功！")
            return compiled_graph
        except Exception as e2:
            print(f"第二次编译尝试失败: {str(e2)}")
            try:
                # 最后尝试无参数编译
                compiled_graph = workflow.compile()
                print("无参数编译成功！")
                return compiled_graph
            except Exception as e3:
                print(f"所有编译尝试都失败: {str(e3)}")
                raise RuntimeError(f"无法编译LangGraph工作流: {str(e3)}") from e3

# 创建行程规划工作流实例
itinerary_app = create_itinerary_graph()
