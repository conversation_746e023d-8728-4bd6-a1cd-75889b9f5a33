from fastapi import FastAPI, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import os
import sys
from pydantic import BaseModel
from datetime import datetime, timedelta
import json
import random
import requests
import re
from dotenv import load_dotenv
import traceback
import time
import uuid
import threading
from collections import deque


# 将项目根目录添加到 Python 路径，以允许绝对导入
# sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.agent.graph import itinerary_app  # 导入LangGraph多智能体系统
from langchain_core.messages import HumanMessage
from src.tools.gaode_api_tool import get_city_from_coordinates, get_railway_station, get_taxi_route, is_valid_coordinates

# 导入AG-UI集成
from src.ag_ui.fastapi_integration import setup_agui_routes
from src.ag_ui.events import generate_thread_id, generate_run_id, generate_message_id
from src.ag_ui.stream_manager import AsyncAGUIStreamManager

# 初始化 FastAPI 应用
app = FastAPI(
    title="智能旅行规划助手",
    description="一个由 LangGraph 和 FastAPI 驱动的智能旅行规划助手。",
    version="1.0.0"
)

# 配置模板
templates = Jinja2Templates(directory="src/web/templates")

# 设置AG-UI路由
agui_integration = setup_agui_routes(app)

class ChatRequest(BaseModel):
    message: str
    
# 加载项目根目录下的 .env 文件
load_dotenv()
# 高德地图API密钥
AMAP_KEY = os.getenv("AMAP_API_KEY")

# 模拟的城市数据
CHINESE_CITIES = [
    "北京", "上海", "广州", "深圳", "成都", "重庆", "杭州", "南京", 
    "武汉", "西安", "天津", "苏州", "郑州", "长沙", "青岛", "沈阳", 
    "宁波", "昆明", "东莞", "厦门", "大连", "合肥", "佛山", "福州"
]

# 模拟的城市坐标数据 (经纬度)
CITY_COORDS = {
    "北京": [39.9042, 116.4074],
    "上海": [31.2304, 121.4737],
    "广州": [23.1291, 113.2644],
    "深圳": [22.5431, 114.0579],
    "成都": [30.5723, 104.0665],
    "重庆": [29.4316, 106.9123],
    "杭州": [30.2741, 120.1551],
    "南京": [32.0603, 118.7969],
    "武汉": [30.5928, 114.3055],
    "西安": [34.3416, 108.9398],
    "天津": [39.3434, 117.3616],
    "苏州": [31.2989, 120.5853],
    "郑州": [34.7466, 113.6253],
    "长沙": [28.2278, 112.9388],
    "青岛": [36.0671, 120.3826],
    "沈阳": [41.8057, 123.4315],
    "宁波": [29.8683, 121.5440],
    "昆明": [25.0389, 102.7183],
    "东莞": [23.0430, 113.7633],
    "厦门": [24.4798, 118.0894],
    "大连": [38.9140, 121.6147],
    "合肥": [31.8206, 117.2272],
    "佛山": [23.0218, 113.1216],
    "福州": [26.0745, 119.2965]
}

def parse_duration_to_timedelta(duration_str: str) -> timedelta:
    """将 "X小时Y分钟Z秒" 格式的字符串解析为 timedelta 对象"""
    if not isinstance(duration_str, str):
        return timedelta()
        
    hours = int(re.search(r'(\d+)小时', duration_str).group(1)) if '小时' in duration_str else 0
    minutes = int(re.search(r'(\d+)分钟', duration_str).group(1)) if '分钟' in duration_str else 0
    seconds = int(re.search(r'(\d+)秒', duration_str).group(1)) if '秒' in duration_str else 0
    
    return timedelta(hours=hours, minutes=minutes, seconds=seconds)

def is_valid_coordinates(lng, lat):
    """检查坐标是否有效"""
    try:
        lng_float = float(lng)
        lat_float = float(lat)
        
        # 中国大陆经纬度范围
        if 73 <= lng_float <= 135 and 18 <= lat_float <= 53:
            return True
        else:
            print(f"坐标超出中国大陆范围: [{lng},{lat}]")
            return False
    except (ValueError, TypeError):
        print(f"无效的坐标格式: [{lng},{lat}]")
        return False

def get_city_from_coordinates(lng, lat):
    """通过高德地图逆地理编码API获取坐标所在城市"""
    if not is_valid_coordinates(lng, lat):
        return None
        
    print(f"正在获取坐标 [{lng},{lat}] 对应的城市...")
    url = f"https://restapi.amap.com/v3/geocode/regeo"
    params = {
        "key": AMAP_KEY,
        "location": f"{lng},{lat}",
        "extensions": "base"
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        print(f"高德逆地理编码API返回: {data}")
        
        if data.get("status") == "1" and data.get("regeocode"):
            address_component = data["regeocode"]["addressComponent"]
            city = address_component.get("city", "")
            
            # 如果city为空（可能是直辖市），则使用province
            if not city or (isinstance(city, list) and len(city) == 0):
                city = address_component.get("province", "")
                
            # 清除城市名称中的"市"字
            if isinstance(city, str) and city.endswith("市"):
                city = city[:-1]
            
            print(f"获取到城市名称: {city}")
            return city
        else:
            print(f"逆地理编码API调用失败: {data}")
            return None
    except Exception as e:
        print(f"逆地理编码API调用异常: {e}")
        return None

def get_railway_station(city):
    """通过高德地图POI搜索API获取城市的高铁站/火车站"""
    print(f"正在搜索{city}的高铁站/火车站...")
    url = "https://restapi.amap.com/v3/place/text"
    
    # 首先尝试搜索高铁站
    params = {
        "key": AMAP_KEY,
        "keywords": f"{city}高铁站",
        "city": city,
        "offset": 10,  # 返回10个结果
        "page": 1,
        "extensions": "base"
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        print(f"高德POI搜索API返回 (高铁站): {json.dumps(data, ensure_ascii=False)[:200]}...")
        
        stations = []
        if data.get("status") == "1" and data.get("pois") and len(data["pois"]) > 0:
            for poi in data["pois"]:
                name = poi.get("name", "")
                location = poi.get("location", "")
                if location and ("高铁站" in name or "火车站" in name):
                    lng, lat = location.split(",")
                    stations.append({
                        "name": name,
                        "lng": float(lng),
                        "lat": float(lat),
                        "score": 2 if "高铁站" in name else 1  # 高铁站优先级更高
                    })
        
        # 如果没找到高铁站，尝试搜索火车站
        if not stations:
            params["keywords"] = f"{city}火车站"
            response = requests.get(url, params=params)
            data = response.json()
            print(f"高德POI搜索API返回 (火车站): {json.dumps(data, ensure_ascii=False)[:200]}...")
            
            if data.get("status") == "1" and data.get("pois") and len(data["pois"]) > 0:
                for poi in data["pois"]:
                    name = poi.get("name", "")
                    location = poi.get("location", "")
                    if location:
                        lng, lat = location.split(",")
                        stations.append({
                            "name": name,
                            "lng": float(lng),
                            "lat": float(lat),
                            "score": 0
                        })
        
        # 如果找到了站点，按优先级排序并返回最佳结果
        if stations:
            stations.sort(key=lambda x: x["score"], reverse=True)
            best_station = stations[0]
            print(f"找到{city}的最佳车站: {best_station['name']}, 坐标: [{best_station['lng']},{best_station['lat']}]")
            return {
                "name": best_station["name"],
                "lng": best_station["lng"],
                "lat": best_station["lat"]
            }
    except Exception as e:
        print(f"POI搜索API调用异常: {e}")
    
    # 如果API调用失败或未找到站点，使用城市坐标作为备用
    base_coords = CITY_COORDS.get(city, [35, 105])
    fallback_station = {
        "name": f"{city}站",
        "lng": base_coords[1],
        "lat": base_coords[0]
    }
    print(f"未找到实际车站，使用备用坐标: {fallback_station}")
    return fallback_station



def get_station_coordinates(city):
    """获取城市高铁站的坐标（使用POI搜索API）"""
    print(f"获取{city}高铁站坐标...")
    
    # 使用高德POI搜索获取真实的高铁站
    station = get_railway_station(city)
    print(f"高铁站信息: {station}")
    return station

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """提供主 HTML 页面。"""
    # 设置默认日期为明天
    default_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    # 确保默认使用多智能体模式
    response = templates.TemplateResponse("index.html", {"request": request, "default_date": default_date, "use_langgraph": True})

    # 添加HTTP缓存控制，提高页面加载速度
    response.headers["Cache-Control"] = "public, max-age=3600"  # 缓存1小时
    return response

@app.get("/agui", response_class=HTMLResponse)
async def agui_interface(request: Request):
    """AG-UI兼容的实时流式界面"""
    return templates.TemplateResponse("agui_plan.html", {"request": request})

@app.get("/api/cities", response_class=JSONResponse)
async def get_cities():
    """返回城市列表。"""
    response = JSONResponse(content=CHINESE_CITIES)
    response.headers["Cache-Control"] = "public, max-age=86400"  # 缓存24小时
    return response

@app.post("/plan", response_class=HTMLResponse)
async def plan_trip(
    request: Request, 
    departure: str = Form(...), 
    arrival: str = Form(...), 
    date: str = Form(...), 
    criteria: str = Form(...),
    departure_lng: str = Form(None),
    departure_lat: str = Form(None),
    arrival_lng: str = Form(None),
    arrival_lat: str = Form(None)
):
    """重定向到多智能体模式接口"""
    # 重定向到多智能体接口
    print("标准模式已禁用，将使用多智能体模式处理请求")
    return RedirectResponse(url="/plan_langgraph", status_code=307)

# 添加以下导入
from fastapi.responses import JSONResponse, RedirectResponse
import time
import uuid
import threading
from collections import deque

# 创建一个全局字典用于存储规划任务的状态
planning_tasks = {}

# 定义规划状态相关的路由
@app.get("/waiting/{task_id}")
async def waiting_page(request: Request, task_id: str):
    """显示等待页面，用于展示LangGraph多智能体规划过程"""
    if task_id not in planning_tasks:
        return RedirectResponse(url="/")
    
    return templates.TemplateResponse("waiting.html", {
        "request": request,
        "task_id": task_id
    })

@app.get("/planning_status/{task_id}")
async def get_planning_status(task_id: str):
    """获取规划任务的当前状态"""
    if task_id not in planning_tasks:
        return JSONResponse(content={"status": "error", "message": "任务不存在"})
    
    task = planning_tasks[task_id]
    
    # 添加调试日志
    print(f"[状态检查] 任务 {task_id} 当前状态: {task.get('status')}")
    
    status_data = {
        "status": task.get("status", "in_progress"),
        "needs_clarification": task.get("needs_clarification", False),
        "clarification_question": task.get("clarification_question", ""),
        "thinking": task.get("thinking", []),
        "agent_status": {
            "planner": task.get("planner_status", "pending"),
            "major_transport": task.get("major_transport_status", "pending"),
            "local_transport": task.get("local_transport_status", "pending"),
            "synthesis": task.get("synthesis_status", "pending")
        }
    }

    if task["status"] == "completed":
        # 当任务完成时，直接返回重定向响应
        redirect_url = f"/results/{task_id}"
        return RedirectResponse(url=redirect_url, status_code=302)
    elif task["status"] == "error":
        status_data["error_message"] = task.get("error_message", "未知错误")

    # 添加缓存控制头，防止浏览器缓存响应
    response = JSONResponse(content=status_data)
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response

@app.post("/clarify/{task_id}")
async def submit_clarification(task_id: str, data: dict):
    """提交用户对AI澄清问题的回答"""
    if task_id not in planning_tasks:
        return JSONResponse(content={"status": "error", "message": "任务不存在"})
    
    task = planning_tasks[task_id]
    
    # 存储用户响应
    task["user_clarification_response"] = data.get("response", "")
    task["needs_clarification"] = False
    
    # 添加用户思考到思考过程
    task["thinking"].append({
        "agent": "user",
        "content": data.get("response", "")
    })
    
    return JSONResponse(content={"status": "success"})

@app.get("/results/{task_id}")
async def view_results(request: Request, task_id: str):
    """查看规划结果页面"""
    if task_id not in planning_tasks:
        # 如果任务不存在，尝试从缓存或数据库中恢复
        print(f"[警告] 尝试访问不存在的任务结果: {task_id}，将返回空结果页面")
        return templates.TemplateResponse("plan.html", {
            "request": request, 
            "plan": {},
            "thinking_process": [
                {"step": "系统提示", "content": "无法找到该规划任务的详细信息，可能是由于服务器重启或任务已过期。"}
            ]
        })
    
    task = planning_tasks[task_id]
    
    # 即使任务未完成，也尝试显示结果
    if task["status"] != "completed":
        print(f"[警告] 尝试查看未完成的任务结果: {task_id}，状态: {task['status']}")
    
    # 准备规划结果和思考过程
    plan = task.get("final_plan", {})
    thinking_process = []
    
    # 格式化思考过程
    for i, thought in enumerate(task.get("thinking", [])):
        step_name = ""
        if thought["agent"] == "major_transport":
            step_name = "大交通规划"
        elif thought["agent"] == "local_transport":
            step_name = "小交通规划"
        elif thought["agent"] == "synthesis":
            step_name = "结果合成"
        elif thought["agent"] == "user":
            step_name = "用户回答"
        else:
            step_name = f"分析步骤 {i+1}"
            
        thinking_process.append({
            "step": step_name,
            "content": thought["content"]
        })
    
    # 将结果传递给模板
    response = templates.TemplateResponse("plan.html", {
        "request": request, 
        "plan": plan,
        "thinking_process": thinking_process
    })
    
    # 缓存结果页面1小时，提高重复查看性能
    response.headers["Cache-Control"] = "private, max-age=3600"
    return response

# 异步运行LangGraph多智能体规划
def run_langgraph_planning(task_id, initial_state):
    """在后台线程中运行LangGraph规划流程"""
    task = planning_tasks[task_id]
    result = {}
    last_ran_node = "planner"

    try:
        thinking_queue = deque(maxlen=50)

        # 初始化状态
        task["planner_status"] = "working"
        task["major_transport_status"] = "pending"
        task["local_transport_status"] = "pending"
        task["synthesis_status"] = "pending"
        
        thinking_queue.append({"agent": "planner", "content": "正在分析您的旅行需求..."})
        task["thinking"] = list(thinking_queue)

        def process_stream(stream):
            nonlocal last_ran_node, result
            chunk_count = 0
            processed_thoughts = set()  # 跟踪已处理的思考内容，避免重复
            
            print(f"[Stream] 🚀 开始处理stream，当前last_ran_node: {last_ran_node}")

            for chunk in stream:
                chunk_count += 1
                print(f"[Stream] 📦 处理第{chunk_count}个chunk: {type(chunk)}")
                print(f"[Stream] 📦 Chunk内容: {chunk}")

                if not chunk:
                    print(f"[Stream] ⚠️ 跳过空chunk")
                    continue

                try:
                    # 从数据块中提取节点名称和最新状态
                    if not isinstance(chunk, dict) or not chunk.keys():
                        print(f"[Stream] ❌ Chunk不是有效字典: {type(chunk)}")
                        continue

                    node_name = list(chunk.keys())[0]
                    result = chunk[node_name]
                    print(f"[Stream] ✅ 提取节点: {node_name}")

                    # 更新智能体状态：上一个完成，当前工作
                    if node_name != last_ran_node:
                        print(f"[Stream] 🔄 节点切换: {last_ran_node} -> {node_name}")
                        task[f"{last_ran_node}_status"] = "completed"
                        task[f"{node_name}_status"] = "working"
                        last_ran_node = node_name
                    else:
                        print(f"[Stream] ➡️ 继续处理节点: {node_name}")
                    
                    # 减少思考过程更新频率
                    if "thinking_process" in result and result["thinking_process"]:
                        batch_update = []
                        for thought in result["thinking_process"]:
                            # 使用内容的哈希作为唯一标识
                            thought_id = hash(thought)
                            if thought_id not in processed_thoughts:
                                processed_thoughts.add(thought_id)
                                batch_update.append({
                                    "agent": node_name,
                                    "content": thought,
                                    "timestamp": time.time()
                                })
                        
                        # 流式更新思考过程，支持实时显示
                        if batch_update:
                            for thought in batch_update:
                                thinking_queue.append(thought)
                                # 立即更新task，支持流式输出
                                task["thinking"] = list(thinking_queue)
                                # 短暂延迟，让前端能够捕获增量更新
                                time.sleep(0.01)
                        
                        # 清空已处理的思考，避免重复添加
                        result["thinking_process"] = []

                except Exception as e:
                    print(f"[Stream] ❌ 处理chunk时出错: {str(e)}")
                    continue
                
                # 检查澄清状态
                if result.get("is_clarification_needed"):
                    if result.get("user_response"):
                        print(f"[Stream] ✅ 检测到用户回复，清除澄清标志: {result.get('user_response')}")
                        # 用户已回复，清除澄清标志，继续执行
                        result["is_clarification_needed"] = False
                    else:
                        # 需要澄清且没有用户回复，暂停stream
                        thinking_queue.append({
                            "agent": "system",
                            "content": f"🤖 AI需要澄清：{result.get('clarification_question', '请提供更多信息...')}",
                            "timestamp": time.time()
                        })
                        task["thinking"] = list(thinking_queue)
                        print(f"[Stream] ⏸️ 暂停stream等待用户澄清")
                        return True
            return False

        # 首次运行
        try:
            print(f"[LangGraph] 🚀 开始stream执行，初始状态准备")
            print(f"[LangGraph] 📋 初始状态: {initial_state}")
            # LangGraph stream不需要recursion_limit参数
            stream = itinerary_app.stream(initial_state)
            print(f"[LangGraph] ✅ Stream创建成功: {type(stream)}")
            needs_clarification = process_stream(stream)
            print(f"[LangGraph] 📋 Stream处理完成，需要澄清: {needs_clarification}")
        except Exception as e:
            print(f"[LangGraph] ❌ Stream执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

        # 用户澄清循环
        clarification_count = 0
        max_clarifications = 2  # 减少最大澄清次数
        while needs_clarification and clarification_count < max_clarifications:
            clarification_count += 1
            task["needs_clarification"] = True
            task["clarification_question"] = result.get("clarification_question", "请提供更多信息...")
            
            # 等待用户响应
            start_time = time.time()
            timeout = 120  # 2分钟超时
            while not task.get("user_clarification_response"):
                if time.time() - start_time > timeout:
                    raise Exception("用户响应超时，规划中止")
                time.sleep(1)
            
            # 获取用户响应并继续
            user_response = task.pop("user_clarification_response")
            result["user_response"] = user_response
            result["messages"].append(HumanMessage(content=user_response))
            task["needs_clarification"] = False

            # 关键修复：清除澄清标志，让系统继续执行
            result["is_clarification_needed"] = False
            print(f"[LangGraph] ✅ 清除澄清标志，用户回复: {user_response}")

            # 用户回复已在前端添加，这里不重复添加
            print(f"[LangGraph] 📝 用户回复已记录: {user_response}")

            # 使用智能交互节点处理用户回复
            result["next_step"] = "intelligent_ask_user"

            # 继续运行stream
            print(f"[LangGraph] 🔄 继续stream执行，当前状态: {result}")
            try:
                stream = itinerary_app.stream(result, {"recursion_limit": 50})
                print(f"[LangGraph] ✅ 继续Stream创建成功")
                needs_clarification = process_stream(stream)
                print(f"[LangGraph] 📋 继续Stream处理完成，需要澄清: {needs_clarification}")
            except Exception as e:
                print(f"[LangGraph] ❌ 继续Stream执行失败: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

        # 最终处理
        if "final_plan" in result:
            task["final_plan"] = result["final_plan"]
            task["status"] = "completed"
            task[f"{last_ran_node}_status"] = "completed"
            thinking_queue.append({"agent": "synthesis", "content": "行程规划已完成！"})
            task["thinking"] = list(thinking_queue)
            # 确保设置重定向URL
            task["redirect_url"] = f"/results/{task_id}"
        else:
            raise Exception("AI未能生成最终规划，可能是由于信息不足或内部错误。")

    except Exception as e:
        print(f"[规划任务 {task_id}] ❌ 规划过程中出现错误: {e}")
        traceback.print_exc()
        task["status"] = "error"
        task["error_message"] = str(e)
        if last_ran_node:
            task[f"{last_ran_node}_status"] = "error"

@app.post("/plan_langgraph")
async def plan_trip_langgraph(
    request: Request, 
    departure: str = Form(...), 
    arrival: str = Form(...), 
    date: str = Form(...), 
    criteria: str = Form(...),
    departure_lng: str = Form(None),
    departure_lat: str = Form(None),
    arrival_lng: str = Form(None),
    arrival_lat: str = Form(None)
):
    """
    使用LangGraph多智能体系统处理行程规划请求，并返回等待页面。
    """
    print(f"\n收到LangGraph行程规划请求: 从{departure}到{arrival}, 日期={date}, 标准={criteria}")
    print(f"坐标信息: 出发[{departure_lng}, {departure_lat}], 到达[{arrival_lng}, {arrival_lat}]")
    
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())
    
    # 构建查询文本
    query = f"请帮我规划从'{departure}'到'{arrival}'的行程，日期是{date}，希望'{criteria}'的方案。"
    
    # 初始化状态
    initial_state = {
        "messages": [HumanMessage(content=query)],
        "original_query": query,
        "departure_place": departure,
        "arrival_place": arrival,
        "travel_date": date,
        "criteria": criteria,
        "execution_history": [],
        "thinking_process": []
    }
    
    # 添加坐标信息（如果有）
    if departure_lng and departure_lat and is_valid_coordinates(departure_lng, departure_lat):
        initial_state["departure_coordinates"] = {"lng": departure_lng, "lat": departure_lat}
    
    if arrival_lng and arrival_lat and is_valid_coordinates(arrival_lng, arrival_lat):
        initial_state["arrival_coordinates"] = {"lng": arrival_lng, "lat": arrival_lat}
    
    # 创建规划任务
    planning_tasks[task_id] = {
        "status": "in_progress",
        "created_at": time.time(),
        "needs_clarification": False,
        "thinking": [],
        "planner_status": "waiting",
        "planner_progress": 0,
        "major_transport_status": "waiting",
        "major_transport_progress": 0,
        "local_transport_status": "waiting",
        "local_transport_progress": 0,
        "synthesis_status": "waiting",
        "synthesis_progress": 0
    }
    
    # 在后台线程中运行LangGraph规划
    planning_thread = threading.Thread(
        target=run_langgraph_planning,
        args=(task_id, initial_state),
        daemon=True
    )
    planning_thread.start()
    
    # 重定向到等待页面
    return RedirectResponse(url=f"/waiting/{task_id}", status_code=303)

@app.post("/chat", response_class=JSONResponse)
async def chat_with_agent(chat_request: ChatRequest):
    """与AI代理进行聊天的端点"""
    try:
        # 这里可以实现与LangGraph代理的聊天逻辑
        # 例如，将用户消息添加到状态中，并调用代理
        response_message = f"收到您的消息: '{chat_request.message}'. 聊天功能正在建设中。"
        return JSONResponse(content={"response": response_message})
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.post("/plan_agui")
async def plan_trip_agui(request: Request):
    """
    AG-UI兼容的行程规划端点
    使用Server-Sent Events流式返回AG-UI标准事件
    """
    try:
        body = await request.json()
        query = body.get("query", "")
        departure_coords = body.get("departure_coords")
        arrival_coords = body.get("arrival_coords")

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        # 创建AG-UI兼容的输入
        from src.ag_ui.fastapi_integration import RunAgentInput, Message

        thread_id = generate_thread_id()
        run_id = generate_run_id()

        # 构建消息
        messages = [
            Message(
                id=generate_message_id(),
                role="user",
                content=query
            )
        ]

        # 构建状态
        state = {}
        if departure_coords:
            state["departure_coordinates"] = departure_coords
        if arrival_coords:
            state["arrival_coordinates"] = arrival_coords

        input_data = RunAgentInput(
            thread_id=thread_id,
            run_id=run_id,
            messages=messages,
            state=state
        )

        # 使用AG-UI集成流式返回事件
        return StreamingResponse(
            agui_integration._run_agent_stream(input_data),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
            }
        )

    except Exception as e:
        print(f"AG-UI规划端点错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok"}
