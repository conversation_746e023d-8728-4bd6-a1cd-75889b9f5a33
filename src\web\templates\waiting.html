<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体规划中... - AI Trip Plan</title>

    <!-- 引入外部资源 - 使用国内CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- 自定义样式与动画 -->
    <style>
        body {
            font-family: 'Noto Sans SC', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e1e8f0 100%);
            transition: background-color 0.5s;
        }
        .font-serif { font-family: 'Noto Serif SC', serif; }
        .card-shadow { box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        
        /* 思考气泡动画 */
        .thinking-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: currentColor;
            animation: thinking 1.4s infinite ease-in-out both;
        }
        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes thinking {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        /* 智能体思考过程 - 流式打字机效果 */
        .thought-content {
            position: relative;
        }

        .thought-content p {
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.5;
        }

        /* 打字机光标效果 */
        .typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background-color: #4f46e5;
            margin-left: 2px;
            animation: blink 1s infinite;
            vertical-align: text-bottom;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 流式文本动画 - 修复显示问题 */
        .streaming-text {
            display: inline-block;
            min-height: 1.5em;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .agent-thinking {
             animation: fadeIn 0.5s ease-out forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 提问框动画 */
        .question-box {
            border: 2px solid #4f46e5;
            animation: pulse-border 2s infinite;
        }
        @keyframes pulse-border {
            0% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(79, 70, 229, 0); }
            100% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0); }
        }
        
        /* 角色图标 */
        .agent-icon {
            width: 32px; height: 32px;
            border-radius: 50%; display: flex;
            align-items: center; justify-content: center;
            color: white; margin-right: 12px;
            flex-shrink: 0; transition: all 0.3s ease;
        }

        .status-icon {
            margin-left: auto; font-size: 1.25rem;
            transition: all 0.5s ease-in-out;
        }

        .status-pending { color: #9ca3af; } /* gray-400 */
        .status-working { color: #4f46e5; animation: spin 1.5s linear infinite; } /* indigo-600 */
        .status-completed { color: #10b981; } /* green-500 */
        .status-error { color: #ef4444; } /* red-500 */

        .planner-icon { background-color: #4f46e5; }
        .major-transport-icon { background-color: #7c3aed; }
        .local-transport-icon { background-color: #10b981; }
        .synthesis-icon { background-color: #0ea5e9; }
        .user-icon { background-color: #6b7280; }
        .system-icon { background-color: #f97316; }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 平滑滚动效果 */
        #thinking-process {
            scroll-behavior: smooth;
            padding-right: 5px;
        }
        
        #thinking-process::-webkit-scrollbar {
            width: 6px;
        }
        
        #thinking-process::-webkit-scrollbar-track {
            background: rgba(243, 244, 246, 0.5);
            border-radius: 10px;
        }
        
        #thinking-process::-webkit-scrollbar-thumb {
            background: rgba(79, 70, 229, 0.3);
            border-radius: 10px;
        }
        
        #thinking-process::-webkit-scrollbar-thumb:hover {
            background: rgba(79, 70, 229, 0.5);
        }
    </style>
</head>
<body class="text-gray-800 min-h-screen flex flex-col">
    <div class="container mx-auto px-4 py-8 flex-grow">
        <header class="text-center mb-12">
            <h1 id="main-title" class="text-3xl font-bold font-serif text-indigo-900 mb-2 transition-all duration-500">
                AI规划中<span class="thinking-dots inline-flex space-x-1 ml-1">
                    <span class="thinking-dot"></span><span class="thinking-dot"></span><span class="thinking-dot"></span>
                </span>
            </h1>
            <p id="main-subtitle" class="text-gray-600 transition-all duration-500">正在为您生成最优行程，请稍候</p>
        </header>
        
        <main class="max-w-3xl mx-auto">
            <!-- 进度指示器 -->
            <div id="progress-container" class="bg-white rounded-xl p-6 card-shadow mb-8 transition-all duration-500">
                <h2 class="text-xl font-bold mb-5 text-indigo-800">多智能体协作蓝图</h2>
                <div id="agent-list" class="space-y-3">
                    <!-- Agent status will be dynamically inserted here -->
                </div>
            </div>
            
            <!-- 智能体思考过程 -->
            <div id="thinking-container" class="bg-white rounded-xl p-6 card-shadow mb-8 opacity-0 transition-opacity duration-700 delay-300">
                <h2 class="text-xl font-bold mb-4 text-indigo-800">规划思考过程</h2>
                <div id="thinking-process" class="space-y-4 max-h-96 overflow-y-auto pr-2">
                    <!-- Thinking process will be dynamically inserted here -->
                </div>
            </div>
            
            <!-- 用户交互区域 (默认隐藏) -->
            <div id="user-interaction" class="hidden bg-white rounded-xl p-6 card-shadow mb-8">
                 <h2 class="text-xl font-bold mb-4 text-indigo-800">需要您的帮助</h2>
                <div class="question-box p-4 rounded-lg bg-indigo-50 mb-4">
                    <p id="clarification-question" class="text-indigo-800">为了给您提供更精确的规划，请告诉我您的出发城市和目的地城市。</p>
                </div>
                
                <form id="clarification-form" class="space-y-4">
                    <div>
                        <input type="text" id="user-response" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="请输入您的回答...">
                    </div>
                    <div>
                        <button type="submit" class="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                            提交回答
                        </button>
                    </div>
                </form>
            </div>

            <!-- 最终结果按钮 -->
            <div id="result-button-container" class="text-center mt-10 hidden">
                 <button id="result-button" class="bg-green-500 text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:bg-green-600 transition-all transform hover:scale-105">
                    <i class="fas fa-check-circle mr-2"></i> 查看您的专属行程
                </button>
            </div>
        </main>
    </div>
    
    <footer class="bg-gray-100 py-4 text-center text-gray-600 mt-auto">
        <p>© 2024 AI Trip Plan | 智能旅行规划助手</p>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const agentList = document.getElementById('agent-list');
            const thinkingProcess = document.getElementById('thinking-process');
            const thinkingContainer = document.getElementById('thinking-container');
            const userInteraction = document.getElementById('user-interaction');
            const clarificationQuestion = document.getElementById('clarification-question');
            const clarificationForm = document.getElementById('clarification-form');
            const resultButtonContainer = document.getElementById('result-button-container');
            const resultButton = document.getElementById('result-button');
            const mainTitle = document.getElementById('main-title');
            const mainSubtitle = document.getElementById('main-subtitle');

            const agentUIs = {
                planner: { name: '规划师智能体', icon: 'fa-brain', color: 'planner-icon' },
                major_transport: { name: '大交通专家', icon: 'fa-train', color: 'major-transport-icon' },
                local_transport: { name: '小交通专家', icon: 'fa-car', color: 'local-transport-icon' },
                synthesis: { name: '结果合成', icon: 'fa-clipboard-check', color: 'synthesis-icon' }
            };

            function createAgentElement(id, agent) {
                const el = document.createElement('div');
                el.id = `agent-${id}`;
                el.className = 'flex items-center p-3 bg-gray-50 rounded-lg transition-all duration-300';
                el.innerHTML = `
                    <div class="agent-icon ${agent.color}"><i class="fas ${agent.icon}"></i></div>
                    <span class="font-medium text-gray-700">${agent.name}</span>
                    <div class="status-icon status-pending"><i class="fas fa-hourglass-half"></i></div>
                `;
                return el;
            }

            Object.entries(agentUIs).forEach(([id, agent]) => {
                agentList.appendChild(createAgentElement(id, agent));
            });

            function updateAgentUI(id, status) {
                const agentEl = document.getElementById(`agent-${id}`);
                if (!agentEl) return;
                
                const statusIcon = agentEl.querySelector('.status-icon');
                let iconClass = 'fa-hourglass-half';
                let statusClass = 'status-pending';

                if (status === 'working') {
                    iconClass = 'fa-cog';
                    statusClass = 'status-working';
                    agentEl.classList.add('bg-indigo-50', 'scale-105');
                } else if (status === 'completed') {
                    iconClass = 'fa-check-circle';
                    statusClass = 'status-completed';
                    agentEl.classList.remove('bg-indigo-50', 'scale-105');
                    agentEl.classList.add('opacity-70');
                } else if (status === 'error') {
                    iconClass = 'fa-times-circle';
                    statusClass = 'status-error';
                    agentEl.classList.add('bg-red-50');
                } else { // pending
                    agentEl.classList.remove('bg-indigo-50', 'scale-105', 'opacity-70', 'bg-red-50');
                }
                
                statusIcon.className = `status-icon ${statusClass}`;
                statusIcon.innerHTML = `<i class="fas ${iconClass}"></i>`;
            }

            let displayedThoughts = 0;
            let lastUpdate = 0;
            const updateInterval = 100; // 更快的更新间隔
            let streamingQueue = []; // 流式输出队列
            let isStreaming = false; // 是否正在流式输出

            // 简化的流式打字机效果
            function typeText(element, text, speed = 20) {
                return new Promise((resolve) => {
                    try {
                        let i = 0;
                        element.innerHTML = '';

                        function typeChar() {
                            if (i < text.length) {
                                element.textContent = text.substring(0, i + 1);
                                i++;
                                setTimeout(typeChar, speed);
                            } else {
                                // 确保完整文本显示
                                element.textContent = text;
                                resolve();
                            }
                        }

                        typeChar();
                    } catch (error) {
                        // 如果打字机效果失败，直接显示文本
                        console.warn('打字机效果失败，直接显示文本:', error);
                        element.textContent = text;
                        resolve();
                    }
                });
            }

            // 处理流式输出队列
            async function processStreamingQueue() {
                if (isStreaming || streamingQueue.length === 0) return;

                isStreaming = true;

                while (streamingQueue.length > 0) {
                    const thought = streamingQueue.shift();
                    await addThoughtWithStreaming(thought);
                    // 短暂延迟，让用户能看到流式效果
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                isStreaming = false;
            }

            // 添加带流式效果的思考
            async function addThoughtWithStreaming(thought) {
                const thoughtEl = document.createElement('div');
                thoughtEl.className = 'flex items-start opacity-0 agent-thinking';

                let agent = agentUIs[thought.agent] || { name: '系统', icon: 'fa-robot', color: 'system-icon' };
                if (thought.agent === 'user') {
                    agent = { name: '您', icon: 'fa-user', color: 'user-icon' };
                }

                thoughtEl.innerHTML = `
                    <div class="agent-icon ${agent.color}"><i class="fas ${agent.icon}"></i></div>
                    <div class="flex-1">
                        <div class="text-sm text-gray-500 mb-1 font-semibold">${agent.name}</div>
                        <div class="bg-gray-100 p-3 rounded-lg thought-content">
                           <p class="whitespace-pre-wrap"></p>
                        </div>
                    </div>
                `;

                thinkingProcess.appendChild(thoughtEl);

                // 淡入动画
                setTimeout(() => {
                    thoughtEl.classList.remove('opacity-0');
                }, 50);

                // 流式打字效果 - 根据内容长度调整速度
                const textElement = thoughtEl.querySelector('p');
                const speed = thought.content.length > 100 ? 10 : 15; // 长文本更快
                await typeText(textElement, thought.content, speed);

                // 滚动到底部
                thinkingProcess.scrollTop = thinkingProcess.scrollHeight;
            }

            function updateThinkingProcess(thoughts) {
                if (thoughts.length === 0) return;

                // 显示思考容器
                if (thinkingContainer.classList.contains('opacity-0')) {
                    thinkingContainer.classList.remove('opacity-0');
                }

                // 添加新的思考到队列
                if (thoughts.length > displayedThoughts) {
                    const newThoughts = thoughts.slice(displayedThoughts);
                    streamingQueue.push(...newThoughts);
                    displayedThoughts = thoughts.length;

                    // 开始处理队列
                    processStreamingQueue();
                }
            }

            // 优化轮询逻辑 - 更快的更新频率以支持流式输出
            let pollingTimeout;
            const initialPollingInterval = 500; // 初始500毫秒，更快的响应
            let currentPollingInterval = initialPollingInterval;
            const maxPollingInterval = 2000; // 最大2秒
            let errorCount = 0;
            const maxErrorsBeforeSlowdown = 3;

            function checkPlanningStatus() {
                fetch(`/planning_status/{{ task_id }}`, {
                    redirect: 'manual', // 阻止自动跟随重定向
                    cache: 'no-store' // 防止缓存
                })
                    .then(response => {
                        // 重置错误计数
                        errorCount = 0;
                        // 检查是否是重定向响应
                        if (response.type === 'opaqueredirect' || response.status === 302) {
                            // 规划完成，跳转到结果页面
                            const redirectUrl = `/results/{{ task_id }}`;
                            window.location.href = redirectUrl;
                            return;
                        }

                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then(data => {
                        // 如果没有数据（重定向情况），直接返回
                        if (!data) return;

                        Object.keys(agentUIs).forEach(id => {
                            updateAgentUI(id, data.agent_status[id]);
                        });

                        updateThinkingProcess(data.thinking);

                        if (data.needs_clarification) {
                            showClarificationRequest(data.clarification_question);
                        } else {
                            hideClarificationRequest();
                        }

                        if (data.status === 'completed') {
                            showCompletion(data.redirect_url);
                            // 停止轮询
                            clearTimeout(pollingTimeout);
                            return;
                        } else if (data.status === 'error') {
                            showError(data.error_message);
                            return; // 停止轮询
                        } else {
                            // 如果有活动状态或正在流式输出，使用较短的轮询间隔
                            const hasActiveAgent = Object.values(data.agent_status).some(status => status === 'working');
                            const hasNewThoughts = data.thinking && data.thinking.length > displayedThoughts;

                            if (hasActiveAgent || hasNewThoughts || isStreaming) {
                                currentPollingInterval = initialPollingInterval;
                            } else {
                                currentPollingInterval = Math.min(currentPollingInterval + 100, maxPollingInterval);
                            }

                            // 继续轮询
                            pollingTimeout = setTimeout(checkPlanningStatus, currentPollingInterval);
                        }
                    })
                    .catch(error => {
                        errorCount++;
                        
                        // 检查是否已完成规划，避免显示错误消息
                        if (document.getElementById('main-title').textContent.includes('规划完成')) {
                            return;
                        }
                        
                        // 增加网络错误后的重试间隔
                        if (errorCount > maxErrorsBeforeSlowdown) {
                            currentPollingInterval = Math.min(currentPollingInterval * 1.5, 5000); // 最大5秒
                        }
                        
                        // 重试
                        pollingTimeout = setTimeout(checkPlanningStatus, currentPollingInterval);
                    });
            }

            function showCompletion(redirectUrl) {
                // 停止所有轮询
                clearTimeout(pollingTimeout);

                // 确保有有效的重定向URL
                const finalUrl = redirectUrl || `/results/{{ task_id }}`;

                // 更新UI状态
                mainTitle.textContent = '🎉 规划完成！';
                document.querySelector('.thinking-dots').style.display = 'none';

                // 显示结果按钮
                resultButtonContainer.classList.remove('hidden');
                resultButton.innerHTML = '<i class="fas fa-check-circle mr-2"></i> 立即查看行程';
                resultButton.onclick = () => {
                    window.location.href = finalUrl;
                };

                // 执行JavaScript跳转
                setTimeout(() => {
                    window.location.href = finalUrl;
                }, 1000);
            }

            function showError(message) {
                 mainTitle.textContent = '😕 规划出错';
                 mainSubtitle.textContent = '抱歉，我们在规划时遇到了一些问题。';
                 document.querySelector('.thinking-dots').style.display = 'none';

                 addThought({ agent: 'system', content: `错误详情: ${message}\n\n5秒后将返回首页。`});

                 setTimeout(() => {
                    window.location.href = '/';
                }, 5000);
            }
            
            function addThought(thought) {
                // 将思考添加到流式队列
                streamingQueue.push(thought);
                processStreamingQueue();
            }

            function showClarificationRequest(question) {
                userInteraction.classList.remove('hidden');
                clarificationQuestion.textContent = question || '请提供更多信息以完成您的行程规划';
                clarificationForm.querySelector('input').focus();
            }
        
            function hideClarificationRequest() {
                userInteraction.classList.add('hidden');
            }

            clarificationForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const input = document.getElementById('user-response');
                const response = input.value.trim();
                if (!response) return;

                // 立即添加用户回复到显示中
                addThought({ agent: 'user', content: response });

                // 禁用表单防止重复提交
                const submitButton = clarificationForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                submitButton.textContent = '提交中...';

                fetch(`/clarify/{{ task_id }}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ response: response })
                })
                .then(() => {
                    input.value = '';
                    hideClarificationRequest();
                })
                .catch(err => {
                    console.error('Failed to submit clarification:', err);
                    submitButton.disabled = false;
                    submitButton.textContent = '提交回答';
                });
            });
            
            // 开始轮询 - 更快启动以支持流式输出
            setTimeout(checkPlanningStatus, 200);
        });
    </script>
</body>
</html> 