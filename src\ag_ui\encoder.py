"""
AG-UI Protocol Event Encoder
Handles encoding events for Server-Sent Events (SSE) streaming
"""

import json
from typing import Any, Dict, Union
from .events import AGUIEvent, BaseEvent


class EventEncoder:
    """Encodes AG-UI events for streaming transmission"""
    
    def __init__(self, format_type: str = "sse"):
        """
        Initialize the event encoder
        
        Args:
            format_type: The format type for encoding ("sse" for Server-Sent Events)
        """
        self.format_type = format_type
    
    def encode(self, event: Union[AGUIEvent, Dict[str, Any]]) -> str:
        """
        Encode an event for transmission
        
        Args:
            event: The event to encode (either an event object or dict)
            
        Returns:
            Encoded event string ready for transmission
        """
        if isinstance(event, BaseEvent):
            # Convert Pydantic model to dict
            event_dict = event.model_dump(exclude_none=True)
        elif isinstance(event, dict):
            event_dict = event
        else:
            raise ValueError(f"Unsupported event type: {type(event)}")
        
        # Convert to camelCase for frontend compatibility
        event_dict = self._to_camel_case(event_dict)
        
        if self.format_type == "sse":
            return self._encode_sse(event_dict)
        else:
            raise ValueError(f"Unsupported format type: {self.format_type}")
    
    def _encode_sse(self, event_dict: Dict[str, Any]) -> str:
        """
        Encode event as Server-Sent Event
        
        Args:
            event_dict: Event data as dictionary
            
        Returns:
            SSE formatted string
        """
        json_data = json.dumps(event_dict, ensure_ascii=False, separators=(',', ':'))
        return f"data: {json_data}\n\n"
    
    def _to_camel_case(self, data: Any) -> Any:
        """
        Convert snake_case keys to camelCase for frontend compatibility
        
        Args:
            data: Data to convert
            
        Returns:
            Data with camelCase keys
        """
        if isinstance(data, dict):
            return {
                self._snake_to_camel(key): self._to_camel_case(value)
                for key, value in data.items()
            }
        elif isinstance(data, list):
            return [self._to_camel_case(item) for item in data]
        else:
            return data
    
    def _snake_to_camel(self, snake_str: str) -> str:
        """
        Convert snake_case string to camelCase
        
        Args:
            snake_str: String in snake_case
            
        Returns:
            String in camelCase
        """
        components = snake_str.split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    def get_content_type(self) -> str:
        """
        Get the appropriate content type for the encoding format
        
        Returns:
            Content type string
        """
        if self.format_type == "sse":
            return "text/event-stream"
        else:
            return "application/json"
    
    def get_headers(self) -> Dict[str, str]:
        """
        Get the appropriate headers for streaming
        
        Returns:
            Dictionary of headers
        """
        headers = {
            "Content-Type": self.get_content_type(),
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
        
        if self.format_type == "sse":
            headers.update({
                "X-Accel-Buffering": "no",  # Disable nginx buffering
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
            })
        
        return headers


class EventStream:
    """Manages a stream of AG-UI events"""
    
    def __init__(self, encoder: EventEncoder = None):
        """
        Initialize the event stream
        
        Args:
            encoder: Event encoder to use (defaults to SSE encoder)
        """
        self.encoder = encoder or EventEncoder("sse")
        self._events = []
    
    def emit(self, event: Union[AGUIEvent, Dict[str, Any]]) -> str:
        """
        Emit an event and return the encoded string
        
        Args:
            event: Event to emit
            
        Returns:
            Encoded event string
        """
        encoded = self.encoder.encode(event)
        self._events.append(event)
        return encoded
    
    def get_events(self) -> list:
        """Get all emitted events"""
        return self._events.copy()
    
    def clear(self):
        """Clear all events"""
        self._events.clear()


# Convenience functions
def encode_event(event: Union[AGUIEvent, Dict[str, Any]], format_type: str = "sse") -> str:
    """
    Convenience function to encode a single event
    
    Args:
        event: Event to encode
        format_type: Format type for encoding
        
    Returns:
        Encoded event string
    """
    encoder = EventEncoder(format_type)
    return encoder.encode(event)


def create_sse_stream() -> EventStream:
    """
    Create a new SSE event stream
    
    Returns:
        New EventStream instance configured for SSE
    """
    return EventStream(EventEncoder("sse"))
