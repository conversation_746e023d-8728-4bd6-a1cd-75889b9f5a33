"""
LLM工具 - 提供统一的大语言模型调用接口
"""

import os
from typing import Dict, Any, Optional
from langchain_community.chat_models import ChatZhipuAI
from langchain_core.messages import HumanMessage, SystemMessage

class LLMTool:
    """大语言模型工具类"""
    
    def __init__(self):
        """初始化LLM工具"""
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """初始化大语言模型"""
        try:
            # 从环境变量获取API密钥
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if not api_key:
                print("[LLM工具] ⚠️ 未找到ZHIPUAI_API_KEY环境变量")
                return
            
            # 初始化智谱AI模型
            self.llm = ChatZhipuAI(
                api_key=api_key,
                model="glm-4-plus",
                temperature=0.7,
                max_tokens=4000
            )
            print("[LLM工具] ✅ 智谱AI模型初始化成功")
            
        except Exception as e:
            print(f"[LLM工具] ❌ 初始化LLM失败: {e}")
            self.llm = None
    
    def call_llm(self, prompt: str, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        调用大语言模型
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词（可选）
            
        Returns:
            包含调用结果的字典
        """
        if not self.llm:
            return {
                'success': False,
                'error': 'LLM未初始化',
                'content': ''
            }
        
        try:
            # 构建消息列表
            messages = []
            
            if system_prompt:
                messages.append(SystemMessage(content=system_prompt))
            
            messages.append(HumanMessage(content=prompt))
            
            # 调用LLM
            response = self.llm.invoke(messages)
            
            return {
                'success': True,
                'content': response.content,
                'error': None
            }
            
        except Exception as e:
            print(f"[LLM工具] ❌ 调用LLM失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': ''
            }
    
    def is_available(self) -> bool:
        """检查LLM是否可用"""
        return self.llm is not None
