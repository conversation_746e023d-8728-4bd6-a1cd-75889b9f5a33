"""
AG-UI Compatible LangGraph
Enhanced version of the original graph that emits AG-UI events
"""

from typing import Dict, Any
from langgraph.graph import StateGraph, END

from src.agent.state import ItineraryState
from src.agent.agui_nodes import (
    agui_planner_node,
    agui_major_transport_node, 
    agui_local_transport_node,
    agui_synthesis_node
)
from src.agent.router import (
    route_by_next_step,
    check_user_interaction_needed,
    check_error_condition,
    check_final_state
)
from src.agent.nodes import (
    web_search_node,
    ask_user_node,
    intelligent_ask_user_node
)


def create_agui_itinerary_graph() -> StateGraph:
    """
    Create AG-UI compatible itinerary planning workflow graph
    
    This graph includes all expert nodes with AG-UI event emission,
    while maintaining the same conditional connections as the original.
    
    Returns:
        A compiled LangGraph workflow with AG-UI event support
    """
    print("正在创建AG-UI兼容的行程规划工作流图谱...")
    
    # Create state graph using ItineraryState
    workflow = StateGraph(ItineraryState)
    
    # Add AG-UI compatible nodes
    workflow.add_node("planner", agui_planner_node)
    workflow.add_node("major_transport", agui_major_transport_node)
    workflow.add_node("local_transport", agui_local_transport_node)
    workflow.add_node("synthesis", agui_synthesis_node)
    
    # Add original nodes that don't need AG-UI modification yet
    workflow.add_node("web_search", web_search_node)
    workflow.add_node("ask_user", ask_user_node)
    workflow.add_node("intelligent_ask_user", intelligent_ask_user_node)
    
    # Set entry point
    workflow.set_entry_point("planner")
    
    # Add conditional edges from planner to other nodes
    workflow.add_conditional_edges(
        "planner",
        route_by_next_step,
        {
            "major_transport": "major_transport",
            "local_transport": "local_transport", 
            "web_search": "web_search",
            "synthesis": "synthesis",
            "ask_user": "ask_user",
            "intelligent_ask_user": "intelligent_ask_user",
            "planner": "planner",
            "end": END
        }
    )
    
    # Add edges from expert nodes back to planner
    workflow.add_conditional_edges(
        "major_transport",
        route_by_next_step,
        {
            "planner": "planner",
            "local_transport": "local_transport",
            "synthesis": "synthesis",
            "end": END
        }
    )
    
    workflow.add_conditional_edges(
        "local_transport", 
        route_by_next_step,
        {
            "planner": "planner",
            "synthesis": "synthesis",
            "end": END
        }
    )
    
    workflow.add_conditional_edges(
        "web_search",
        route_by_next_step,
        {
            "planner": "planner",
            "synthesis": "synthesis", 
            "end": END
        }
    )
    
    workflow.add_conditional_edges(
        "synthesis",
        check_final_state,
        {
            "complete": END,
            "incomplete": "planner"
        }
    )
    
    # User interaction nodes
    workflow.add_conditional_edges(
        "ask_user",
        check_user_interaction_needed,
        {
            "continue": "planner",
            "pause": "ask_user"
        }
    )
    
    workflow.add_conditional_edges(
        "intelligent_ask_user",
        check_user_interaction_needed,
        {
            "continue": "planner", 
            "pause": "intelligent_ask_user"
        }
    )
    
    # Compile the graph
    print("正在编译AG-UI兼容的工作流图谱...")
    compiled_graph = workflow.compile()
    print("✅ AG-UI兼容的行程规划工作流图谱创建完成")
    
    return compiled_graph


# Create the AG-UI compatible app instance
agui_itinerary_app = create_agui_itinerary_graph()


def get_agui_graph_info() -> Dict[str, Any]:
    """
    Get information about the AG-UI compatible graph
    
    Returns:
        Dictionary containing graph information
    """
    return {
        "name": "AG-UI Compatible Itinerary Planning Graph",
        "version": "1.0.0",
        "nodes": [
            "planner",
            "major_transport", 
            "local_transport",
            "web_search",
            "synthesis",
            "ask_user",
            "intelligent_ask_user"
        ],
        "agui_compatible_nodes": [
            "planner",
            "major_transport",
            "local_transport", 
            "synthesis"
        ],
        "features": [
            "AG-UI event emission",
            "Real-time streaming",
            "Tool call events",
            "State delta updates",
            "Thinking process streaming"
        ]
    }


def create_hybrid_graph(use_agui_events: bool = True) -> StateGraph:
    """
    Create a hybrid graph that can optionally use AG-UI events
    
    Args:
        use_agui_events: Whether to use AG-UI compatible nodes
        
    Returns:
        Compiled LangGraph workflow
    """
    if use_agui_events:
        return create_agui_itinerary_graph()
    else:
        # Use original graph
        from src.agent.graph import create_itinerary_graph
        return create_itinerary_graph()


# Utility functions for graph management
def validate_agui_graph() -> bool:
    """
    Validate that the AG-UI graph is properly configured
    
    Returns:
        True if valid, False otherwise
    """
    try:
        graph = create_agui_itinerary_graph()
        # Basic validation - check if graph can be created
        return graph is not None
    except Exception as e:
        print(f"AG-UI graph validation failed: {str(e)}")
        return False


def get_graph_visualization() -> str:
    """
    Get a text representation of the graph structure
    
    Returns:
        String representation of the graph
    """
    return """
AG-UI Compatible Itinerary Planning Graph:

Entry Point: planner
├── planner (AG-UI) → [major_transport, local_transport, web_search, synthesis, ask_user, intelligent_ask_user, end]
├── major_transport (AG-UI) → [planner, local_transport, synthesis, end]
├── local_transport (AG-UI) → [planner, synthesis, end]
├── web_search → [planner, synthesis, end]
├── synthesis (AG-UI) → [end, planner]
├── ask_user → [planner, ask_user]
└── intelligent_ask_user → [planner, intelligent_ask_user]

AG-UI Features:
- Real-time event streaming
- Tool call event emission
- State delta updates
- Thinking process streaming
- Step lifecycle events
"""


# Export the main graph instance
__all__ = [
    "create_agui_itinerary_graph",
    "agui_itinerary_app", 
    "get_agui_graph_info",
    "create_hybrid_graph",
    "validate_agui_graph",
    "get_graph_visualization"
]
