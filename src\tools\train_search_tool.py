from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import traceback
from typing import Dict, Any, Union, Optional

from src.clients.train_client import TrainAPIClient
from src.services.train_service import analyze_train_data

# 实例化API客户端
train_client = TrainAPIClient()

# 定义输入模式
class TrainSearchInput(BaseModel):
    departure: str = Field(..., description="出发城市或车站，例如\"北京\"")
    arrival: str = Field(..., description="到达城市或车站，例如\"上海\"")
    criteria: str = Field(default="最便宜", description="搜索标准。选项包括\"最便宜\"、\"最贵\"、\"最快\"、\"综合最优\"")
    date: Optional[str] = Field(default=None, description="出发日期，格式为 YYYY-MM-DD。如果未提供，则默认为明天")

# 使用继承BaseTool的方式定义工具，确保兼容最新版本的LangChain
class TrainSearchTool(BaseTool):
    name: str = "search_train"
    description: str = "根据指定标准查找两个城市之间的高铁选项"
    args_schema: type[TrainSearchInput] = TrainSearchInput
    
    def _run(self, departure: str, arrival: str, criteria: str = "最便宜", date: Optional[str] = None) -> Dict[str, Any]:
        """实际执行工具逻辑"""
        print(f"\n[工具] 🚄 正在调用 search_train 工具")
        print(f"[工具] 📍 参数: 从={departure}, 到={arrival}, 标准={criteria}, 日期={date or '明天'}")

        try:
            # 参数验证
            if not departure or not isinstance(departure, str):
                return {"status": "error", "message": "出发地不能为空且必须是字符串"}
            
            if not arrival or not isinstance(arrival, str):
                return {"status": "error", "message": "目的地不能为空且必须是字符串"}
            
            # 日期处理
            if not date:
                date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
            else:
                try:
                    # 验证日期格式
                    datetime.strptime(date, "%Y-%m-%d")
                except ValueError:
                    return {"status": "error", "message": f"日期格式错误，应为YYYY-MM-DD: {date}"}
            
            # 验证并设置默认搜索标准
            valid_criteria = ["最便宜", "最贵", "最快", "综合最优"]
            if criteria not in valid_criteria:
                print(f"[工具] ⚠️ 搜索标准'{criteria}'无效，使用默认值: '最便宜'")
                criteria = "最便宜"

            # 1. 调用API客户端获取原始数据（支持直达和中转）
            raw_data = train_client.query_trains(departure, arrival, date)

            if raw_data is None:
                return {"status": "error", "message": "API请求失败，无法获取数据"}
                
            if "error" in raw_data:
                error_msg = f"搜索失败: {raw_data['error']}"
                print(f"[工具] ❌ {error_msg}")
                return {"status": "error", "message": error_msg}

            # 2. 调用服务分析数据
            result = analyze_train_data(raw_data, criteria)

            if "error" in result:
                error_msg = f"分析失败: {result['error']}"
                print(f"[工具] ❌ {error_msg}")
                return {"status": "error", "message": error_msg}
            
            # 3. 构建结构化返回数据
            structured_data = {
                "train": {
                    "code": result['train_code'],
                    "type": result['train_code'][0],  # G/D/C等车次类型
                    "departure": {
                        "station": result['departure']['station'],
                        "date": result['departure']['date'],
                        "time": result['departure']['time']
                    },
                    "arrival": {
                        "station": result['arrival']['station'],
                        "time": result['arrival']['time']
                    },
                    "duration": result['duration'],
                    "seat": {
                        "type": result['seat']['type'],
                        "price": float(result['seat']['price']),
                        "available": result['seat']['available']
                    },
                    # 添加中转信息
                    "isTransfer": result.get('isTransfer', False),
                    "transferInfo": result.get('transferInfo', {})
                },
                "search_info": {
                    "departure_city": departure,
                    "arrival_city": arrival,
                    "date": date,
                    "criteria": criteria,
                    "other_trains_count": result['other_trains_count']
                }
            }
            
            # 4. 为日志生成可读性描述
            response_summary = f"从 {departure} 到 {arrival} 的 {criteria} 火车选项: "
            response_summary += f"{result['train_code']} ({result['departure']['time']}→{result['arrival']['time']})"
            print(f"[工具] ✅ 搜索完成: {response_summary}")
            
            return {
                "status": "success",
                "data": structured_data
            }
            
        except Exception as e:
            # 捕获并记录所有异常
            error_msg = f"工具执行异常: {str(e)}"
            print(f"[工具] ❌ {error_msg}")
            print(f"[工具] 🔍 异常详情: {traceback.format_exc()}")
            
            return {
                "status": "error", 
                "message": error_msg
            }
    
    async def _arun(self, departure: str, arrival: str, criteria: str = "最便宜", date: Optional[str] = None) -> Dict[str, Any]:
        """异步执行（这里简单地调用同步方法）"""
        return self._run(departure, arrival, criteria, date)

# 实例化工具
search_train = TrainSearchTool()
