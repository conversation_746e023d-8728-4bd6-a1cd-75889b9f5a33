# main.py (修正版 - 支持上下文)

import os
from dotenv import load_dotenv

# 确保这行在最前面，以加载环境变量
load_dotenv()

import sys
import os

# 将“src”目录添加到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from langchain_core.messages import HumanMessage, AIMessage
from agent.graph import app

# 添加身份识别和特殊回复功能
def is_identity_question(text: str) -> bool:
    """检查是否是询问模型身份的问题"""
    identity_keywords = [
        "你是谁", "你是什么模型", "你的模型是什么", "什么AI", "什么人工智能",
        "你叫什么", "who are you", "what model", "what ai", "你的名字",
        "你基于什么", "你的开发者", "what is your name"
    ]
    return any(keyword in text.lower() for keyword in identity_keywords)

def get_identity_response() -> str:
    """返回标准的身份回复"""
    return "您好，我是一个智能助手，可以帮助您规划行程。我可以查询天气、火车票等信息。有什么可以帮您的吗？"

def run_agent():
    print("\n" + "=" * 60)
    print("🤖 智能助手系统启动")
    print("=" * 60)
    print("支持功能:")
    print("1. 📚 一般问答与信息查询")
    print("2. ☁️ 天气查询 (例如: '北京今天天气怎么样?')")
    print("3. 🚄 高铁票务查询：")
    print("   - 最便宜方案 (例如: '查询从北京到上海的最便宜动车')")
    print("   - 最快方案 (例如: '查询从广州到深圳的最快动车')")
    print("   - 最贵方案 (例如: '查询从北京到上海的最贵高铁')")
    print("   - 综合最优 (例如: '帮我找一下从成都到重庆的综合最优的高铁方案')")
    print("输入 'exit' 退出系统")
    print("=" * 60 + "\n")

    # 1. 在循环外创建一个列表，用于存储整个对话历史
    conversation_history = []

    while True:
        user_input = input("您: ")
        if user_input.lower() == 'exit':
            print("再见！")
            break

        # 检查是否是身份相关问题
        if is_identity_question(user_input):
            # 记录用户问题到历史
            conversation_history.append(HumanMessage(content=user_input))

            # 生成标准身份回复
            identity_response = get_identity_response()
            print(f"助手: {identity_response}")
            print("\n---")

            # 记录回复到历史
            conversation_history.append(AIMessage(content=identity_response))
            continue  # 跳过后续处理

        # 2. 将用户的当前输入作为 HumanMessage 添加到历史记录中
        conversation_history.append(HumanMessage(content=user_input))

        # 3. 将完整的对话历史作为输入传递给 LangGraph 应用
        inputs = {"messages": conversation_history}

        print("助手: ", end="", flush=True)
        final_answer_content = ""
        # 使用一个变量来捕获最终的完整状态
        final_state = None
        for output in app.stream(inputs, stream_mode="values"):
            last_message = output["messages"][-1]

            # 流式打印最终答案的逻辑保持不变
            if last_message.type == "ai" and not last_message.tool_calls:
                print(last_message.content, end="", flush=True)
                final_answer_content = last_message.content

            # 持续更新，以获取循环结束时的最终状态
            final_state = output

        print("\n---") # 打印换行和分隔符

        # 4. 将助手的完整回复也添加到历史记录中，为下一次对话做准备
        if final_state:
            # 从最终状态中获取完整的助手消息（可能是 AIMessage 或 ToolMessage）
            # 我们只关心最终的那个AI回答
            ai_response_message = final_state["messages"][-1]
            conversation_history.append(ai_response_message)

if __name__ == "__main__":
    run_agent()