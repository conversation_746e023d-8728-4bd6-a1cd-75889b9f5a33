# AG-UI 事件映射策略

## 概述

本文档定义了将现有LangGraph多智能体系统改造为AG-UI协议兼容系统的详细事件映射策略。

## 1. 生命周期事件映射

### RUN_STARTED / RUN_FINISHED / RUN_ERROR
```python
# 映射场景：整个行程规划流程的开始和结束
# 触发时机：
- RUN_STARTED: 用户提交规划请求时
- RUN_FINISHED: 生成最终行程规划时
- RUN_ERROR: 发生不可恢复错误时

# 实现位置：
- FastAPI端点接收请求时发送RUN_STARTED
- synthesis_node完成时发送RUN_FINISHED
- 异常处理中发送RUN_ERROR
```

### STEP_STARTED / STEP_FINISHED
```python
# 映射场景：每个智能体节点的执行生命周期
# 节点映射：
- planner: "规划师分析"
- major_transport: "大交通规划"
- local_transport: "小交通规划"  
- web_search: "信息搜索"
- synthesis: "方案综合"
- ask_user: "用户交互"

# 实现方式：
def emit_step_events(node_name: str, state: ItineraryState):
    yield StepStartedEvent(step_name=node_name)
    # 执行节点逻辑
    result = execute_node(state)
    yield StepFinishedEvent(step_name=node_name)
```

## 2. 文本消息事件映射

### TEXT_MESSAGE_START / TEXT_MESSAGE_CONTENT / TEXT_MESSAGE_END
```python
# 映射场景：智能体思考过程的流式输出
# 数据源：state["thinking_process"]

# 实现策略：
1. 每个智能体开始思考时发送TEXT_MESSAGE_START
2. 思考内容分块流式发送TEXT_MESSAGE_CONTENT
3. 思考完成时发送TEXT_MESSAGE_END

# 示例实现：
def stream_thinking_process(agent_name: str, thoughts: List[str]):
    message_id = generate_message_id()
    
    yield TextMessageStartEvent(
        message_id=message_id,
        role="assistant"
    )
    
    for thought in thoughts:
        yield TextMessageContentEvent(
            message_id=message_id,
            delta=f"[{agent_name}] {thought}\n"
        )
    
    yield TextMessageEndEvent(message_id=message_id)
```

## 3. 工具调用事件映射

### TOOL_CALL_START / TOOL_CALL_ARGS / TOOL_CALL_END
```python
# 映射场景：所有外部API调用
# 工具清单：
- search_train: 火车查询
- search_flight: 航班查询  
- get_weather: 天气查询
- get_railway_station: 车站查询
- get_taxi_route: 驾车路线
- get_transit_route: 公交路线

# 实现模式：
def wrap_tool_call(tool_name: str, **kwargs):
    tool_call_id = generate_tool_call_id()
    
    # 开始工具调用
    yield ToolCallStartEvent(
        tool_call_id=tool_call_id,
        tool_call_name=tool_name
    )
    
    # 流式发送参数
    args_json = json.dumps(kwargs)
    for chunk in chunk_string(args_json, 50):
        yield ToolCallArgsEvent(
            tool_call_id=tool_call_id,
            delta=chunk
        )
    
    # 结束工具调用
    yield ToolCallEndEvent(tool_call_id=tool_call_id)
    
    # 执行实际工具调用
    result = execute_tool(tool_name, **kwargs)
    
    # 返回结果
    yield ToolCallResultEvent(
        tool_call_id=tool_call_id,
        content=json.dumps(result)
    )
```

## 4. 状态管理事件映射

### STATE_SNAPSHOT / STATE_DELTA
```python
# 映射场景：ItineraryState的状态同步
# 策略：
- STATE_SNAPSHOT: 流程开始时发送完整状态
- STATE_DELTA: 每次状态变更时发送增量更新

# 关键状态字段映射：
state_mapping = {
    "departure_city": "出发城市",
    "arrival_city": "目的地城市", 
    "major_transport_options": "大交通选项",
    "selected_major_transport": "选中的大交通",
    "local_transport_options": "小交通方案",
    "final_plan": "最终规划"
}

# 实现方式：
def emit_state_delta(old_state: dict, new_state: dict):
    patches = create_json_patch(old_state, new_state)
    if patches:
        yield StateDeltaEvent(delta=patches)
```

### MESSAGES_SNAPSHOT
```python
# 映射场景：完整对话历史的同步
# 触发时机：
- 流程开始时
- 用户澄清后
- 流程结束时

# 消息类型映射：
- HumanMessage -> user role
- AIMessage -> assistant role  
- 工具结果 -> tool role
```

## 5. 特殊事件映射

### RAW / CUSTOM
```python
# RAW事件：用于传递原始LangGraph事件
# CUSTOM事件：用于传递特定业务事件

custom_events = {
    "route_visualization": "路线可视化数据",
    "timeline_update": "时间轴更新",
    "clarification_needed": "需要用户澄清",
    "transport_selection": "交通方式选择"
}
```

## 6. 事件流序列示例

### 完整规划流程的事件序列
```
1. RUN_STARTED (thread_id, run_id)
2. STATE_SNAPSHOT (初始状态)
3. STEP_STARTED (step_name="planner")
4. TEXT_MESSAGE_START (规划师开始分析)
5. TEXT_MESSAGE_CONTENT (分析过程)
6. TEXT_MESSAGE_END (分析完成)
7. STEP_FINISHED (step_name="planner")
8. STATE_DELTA (更新解析结果)
9. STEP_STARTED (step_name="major_transport")
10. TOOL_CALL_START (tool_name="search_train")
11. TOOL_CALL_ARGS (查询参数)
12. TOOL_CALL_END
13. TOOL_CALL_RESULT (查询结果)
14. TEXT_MESSAGE_START (大交通专家分析)
15. TEXT_MESSAGE_CONTENT (分析内容)
16. TEXT_MESSAGE_END
17. STEP_FINISHED (step_name="major_transport")
18. STATE_DELTA (更新交通选项)
... (继续其他节点)
19. STEP_STARTED (step_name="synthesis")
20. TEXT_MESSAGE_START (综合分析)
21. TEXT_MESSAGE_CONTENT (最终规划)
22. TEXT_MESSAGE_END
23. STEP_FINISHED (step_name="synthesis")
24. STATE_DELTA (最终规划结果)
25. MESSAGES_SNAPSHOT (完整对话历史)
26. RUN_FINISHED
```

## 7. 错误处理策略

### 错误事件映射
```python
# 工具调用失败
if tool_call_failed:
    yield ToolCallResultEvent(
        tool_call_id=tool_call_id,
        content=json.dumps({"error": error_message})
    )

# 节点执行失败
if node_failed:
    yield RUN_ERROR(
        message=f"节点 {node_name} 执行失败: {error_message}"
    )

# 用户交互超时
if clarification_timeout:
    yield RUN_ERROR(
        message="用户响应超时，规划中止"
    )
```

## 8. 实现优先级

1. **高优先级**：生命周期事件、文本消息事件
2. **中优先级**：工具调用事件、状态管理事件  
3. **低优先级**：特殊事件、自定义事件

## 9. 兼容性保证

- 保持现有LangGraph节点逻辑不变
- 通过装饰器模式添加事件发射
- 向后兼容现有API接口
- 渐进式迁移策略
