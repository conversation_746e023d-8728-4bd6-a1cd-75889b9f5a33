from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import traceback
import random
from typing import Dict, Any, Union, Optional

# 定义输入模式
class FlightSearchInput(BaseModel):
    departure: str = Field(..., description="出发城市，例如\"北京\"")
    arrival: str = Field(..., description="到达城市，例如\"上海\"")
    criteria: str = Field(default="最便宜", description="搜索标准。选项包括\"最便宜\"、\"最贵\"、\"最快\"、\"综合最优\"")
    date: Optional[str] = Field(default=None, description="出发日期，格式为 YYYY-MM-DD。如果未提供，则默认为明天")

# 机票查询工具
class FlightSearchTool(BaseTool):
    name: str = "search_flight"
    description: str = "根据指定标准查找两个城市之间的航班选项"
    args_schema: type[FlightSearchInput] = FlightSearchInput
    
    def _run(self, departure: str, arrival: str, criteria: str = "最便宜", date: Optional[str] = None) -> Dict[str, Any]:
        """实际执行工具逻辑"""
        print(f"\n[工具] ✈️ 正在调用 search_flight 工具")
        print(f"[工具] 📍 参数: 从={departure}, 到={arrival}, 标准={criteria}, 日期={date or '明天'}")
        
        try:
            # 处理日期
            if date is None:
                date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
            
            # 生成模拟航班数据
            flight_data = self._generate_mock_flight_data(departure, arrival, date)
            
            # 根据标准选择最佳航班
            best_flight = self._select_best_flight(flight_data, criteria)
            
            if not best_flight:
                return {"status": "error", "message": f"未找到从{departure}到{arrival}的航班"}
            
            # 构建结构化数据
            structured_data = {
                "flight": {
                    "code": best_flight["flight_number"],
                    "airline": best_flight["airline"],
                    "aircraft": best_flight["aircraft"],
                    "departure": {
                        "airport": best_flight["dep_airport"],
                        "time": best_flight["dep_time"],
                        "terminal": best_flight["dep_terminal"]
                    },
                    "arrival": {
                        "airport": best_flight["arr_airport"],
                        "time": best_flight["arr_time"],
                        "terminal": best_flight["arr_terminal"]
                    },
                    "duration": best_flight["duration"],
                    "seat": {
                        "type": best_flight["seat_class"],  # 统一使用type字段
                        "class": best_flight["seat_class"], # 保留class字段以兼容
                        "price": best_flight["price"]
                    }
                },
                "criteria": criteria,
                "search_date": date
            }
            
            # 为日志生成可读性描述
            response_summary = f"从 {departure} 到 {arrival} 的 {criteria} 航班选项: "
            response_summary += f"{best_flight['flight_number']} ({best_flight['dep_time']}→{best_flight['arr_time']})"
            print(f"[工具] ✅ 搜索完成: {response_summary}")
            
            return {
                "status": "success",
                "data": structured_data
            }
            
        except Exception as e:
            error_msg = f"工具执行异常: {str(e)}"
            print(f"[工具] ❌ {error_msg}")
            print(f"[工具] 🔍 异常详情: {traceback.format_exc()}")
            
            return {
                "status": "error", 
                "message": error_msg
            }
    
    def _generate_mock_flight_data(self, departure: str, arrival: str, date: str) -> list:
        """生成模拟航班数据"""
        # 机场映射
        airport_mapping = {
            "北京": {"code": "PEK", "name": "北京首都国际机场"},
            "上海": {"code": "PVG", "name": "上海浦东国际机场"},
            "广州": {"code": "CAN", "name": "广州白云国际机场"},
            "深圳": {"code": "SZX", "name": "深圳宝安国际机场"},
            "成都": {"code": "CTU", "name": "成都双流国际机场"},
            "重庆": {"code": "CKG", "name": "重庆江北国际机场"},
            "杭州": {"code": "HGH", "name": "杭州萧山国际机场"},
            "南京": {"code": "NKG", "name": "南京禄口国际机场"},
            "武汉": {"code": "WUH", "name": "武汉天河国际机场"},
            "西安": {"code": "XIY", "name": "西安咸阳国际机场"},
            "天津": {"code": "TSN", "name": "天津滨海国际机场"},
            "青岛": {"code": "TAO", "name": "青岛胶东国际机场"},
            "厦门": {"code": "XMN", "name": "厦门高崎国际机场"},
            "昆明": {"code": "KMG", "name": "昆明长水国际机场"},
            "大连": {"code": "DLC", "name": "大连周水子国际机场"},
            "沈阳": {"code": "SHE", "name": "沈阳桃仙国际机场"},
            "长沙": {"code": "CSX", "name": "长沙黄花国际机场"},
            "郑州": {"code": "CGO", "name": "郑州新郑国际机场"},
            "合肥": {"code": "HFE", "name": "合肥新桥国际机场"},
            "福州": {"code": "FOC", "name": "福州长乐国际机场"},
            "珠海": {"code": "ZUH", "name": "珠海金湾机场"}
        }
        
        # 获取机场信息
        dep_airport = airport_mapping.get(departure, {"code": "UNK", "name": f"{departure}机场"})
        arr_airport = airport_mapping.get(arrival, {"code": "UNK", "name": f"{arrival}机场"})
        
        # 航空公司列表
        airlines = [
            {"code": "CA", "name": "中国国际航空"},
            {"code": "MU", "name": "中国东方航空"},
            {"code": "CZ", "name": "中国南方航空"},
            {"code": "HU", "name": "海南航空"},
            {"code": "3U", "name": "四川航空"},
            {"code": "9C", "name": "春秋航空"},
            {"code": "FM", "name": "上海航空"},
            {"code": "MF", "name": "厦门航空"}
        ]
        
        # 机型列表
        aircraft_types = ["A320", "A321", "A330", "B737", "B738", "B777", "B787"]
        
        flights = []
        
        # 生成3-6个航班
        for i in range(random.randint(3, 6)):
            airline = random.choice(airlines)
            aircraft = random.choice(aircraft_types)
            
            # 生成航班号
            flight_number = f"{airline['code']}{random.randint(1000, 9999)}"
            
            # 生成起飞时间（6:00-22:00）
            dep_hour = random.randint(6, 22)
            dep_minute = random.choice([0, 15, 30, 45])
            dep_time = f"{dep_hour:02d}:{dep_minute:02d}"
            
            # 计算飞行时长（1-4小时）
            flight_duration_minutes = random.randint(60, 240)
            duration_hours = flight_duration_minutes // 60
            duration_minutes = flight_duration_minutes % 60
            duration = f"{duration_hours}小时{duration_minutes:02d}分钟"
            
            # 计算到达时间
            arr_hour = dep_hour + duration_hours
            arr_minute = dep_minute + duration_minutes
            if arr_minute >= 60:
                arr_hour += 1
                arr_minute -= 60
            if arr_hour >= 24:
                arr_hour -= 24
            arr_time = f"{arr_hour:02d}:{arr_minute:02d}"
            
            # 生成价格（根据舱位等级）
            base_price = random.randint(400, 1500)
            seat_classes = [
                {"class": "经济舱", "price": base_price},
                {"class": "商务舱", "price": base_price * 2.5},
                {"class": "头等舱", "price": base_price * 4}
            ]
            
            for seat_class in seat_classes:
                flights.append({
                    "flight_number": flight_number,
                    "airline": airline["name"],
                    "aircraft": aircraft,
                    "dep_airport": dep_airport["name"],
                    "arr_airport": arr_airport["name"],
                    "dep_time": dep_time,
                    "arr_time": arr_time,
                    "dep_terminal": f"T{random.randint(1, 3)}",
                    "arr_terminal": f"T{random.randint(1, 3)}",
                    "duration": duration,
                    "duration_minutes": flight_duration_minutes,
                    "seat_class": seat_class["class"],
                    "price": seat_class["price"]
                })
        
        return flights
    
    def _select_best_flight(self, flights: list, criteria: str) -> Optional[Dict[str, Any]]:
        """根据标准选择最佳航班"""
        if not flights:
            return None
        
        # 只考虑经济舱
        economy_flights = [f for f in flights if f["seat_class"] == "经济舱"]
        
        if not economy_flights:
            return None
        
        if criteria == "最便宜":
            return min(economy_flights, key=lambda x: x["price"])
        elif criteria == "最贵":
            return max(economy_flights, key=lambda x: x["price"])
        elif criteria == "最快":
            return min(economy_flights, key=lambda x: x["duration_minutes"])
        elif criteria == "综合最优":
            # 综合考虑价格和时间
            best_flight = None
            best_score = -1
            
            for flight in economy_flights:
                # 价格分数（价格越低分数越高）
                price_score = 1 - (flight["price"] / 2000)
                # 时间分数（时间越短分数越高）
                time_score = 1 - (flight["duration_minutes"] / 300)
                
                # 综合分数（价格权重0.6，时间权重0.4）
                score = price_score * 0.6 + time_score * 0.4
                
                if score > best_score:
                    best_score = score
                    best_flight = flight
            
            return best_flight
        else:
            return economy_flights[0]
    
    async def _arun(self, departure: str, arrival: str, criteria: str = "最便宜", date: Optional[str] = None) -> Dict[str, Any]:
        """异步执行（这里简单地调用同步方法）"""
        return self._run(departure, arrival, criteria, date)

# 实例化工具
search_flight = FlightSearchTool()
