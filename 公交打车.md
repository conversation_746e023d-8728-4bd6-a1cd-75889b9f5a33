# web服务

## 公交路径规划

公交路径规划 API 可以规划综合各类公共（火车、公交、地铁）交通方式的通勤方案，并且返回通勤方案的数据。

#### **公交路径规划 API URL：**

| **URL**                                                      | **请求方式** |
| ------------------------------------------------------------ | ------------ |
| https://restapi.amap.com/v3/direction/transit/integrated?parameters | GET          |

parameters 代表的参数包括必填参数和可选参数。所有参数均使用和号字符(&)进行分隔。下面的列表枚举了这些参数及其使用规则。

#### **请求参数**

| **参数名**  | **含义**                  | **规则说明**                                                 | **是否必须**       | **缺省值** |
| ----------- | ------------------------- | ------------------------------------------------------------ | ------------------ | ---------- |
| key         | 请求服务权限标识          | 用户在高德地图官网 [申请 Web 服务 API 类型 KEY](https://lbs.amap.com/dev/) | 必填               | 无         |
| origin      | 出发点                    | 规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801   经纬度小数点不超过6位 | 必填               | 无         |
| destination | 目的地                    | 规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801   经纬度小数点不超过6位 | 必填               | 无         |
| city        | 城市/跨城规划时的起点城市 | 目前支持市内公交换乘/跨城公交的起点城市。可选值：城市名称/citycode | 必填               | 无         |
| cityd       | 跨城公交规划时的终点城市  | 跨城公交规划必填参数。可选值：城市名称/citycode              | 可选（ 跨城必填 ） | 无         |
| extensions  | 返回结果详略              | 可选值：base(default)/allbase：返回基本信息；all：返回全部信息 | 可选               | base       |
| strategy    | 公交换乘策略              | 可选值：0：最快捷模式；1：最经济模式；2：最少换乘模式；3：最少步行模式；5：不乘地铁模式 | 可选               | 0          |
| nightflag   | 是否计算夜班车            | 可选值：0：不计算夜班车；1：计算夜班车                       | 可选               | 0          |
| date        | 出发日期                  | 根据出发时间和日期，筛选可乘坐的公交路线，格式示例：date=2014-3-19。在无需设置预计出发时间时，请不要在请求之中携带此参数。 | 可选               | 无         |
| time        | 出发时间                  | 根据出发时间和日期，筛选可乘坐的公交路线，格式示例：time=22:34。在无需设置预计出发时间时，请不要在请求之中携带此参数。 | 可选               | 无         |
| sig         | 数字签名                  | [数字签名获取和使用方法](https://lbs.amap.com/faq/account/key/72) | 可选               | 无         |
| output      | 返回数据格式类型          | 可选值：JSON，XML                                            | 可选               | JSON       |
| callback    | 回调函数                  | callback 值是用户定义的函数名称，此参数只在 output=JSON 时有效 | 可选               | 无         |

#### **服务示例**

```
https://restapi.amap.com/v3/direction/transit/integrated?origin=116.481499,39.990475&destination=116.465063,39.999538&city=010&output=xml&key=<用户的key>
```

| 参数        | 值                                                           | 备注                                                         | 必选 |
| :---------- | :----------------------------------------------------------- | :----------------------------------------------------------- | :--- |
| origin      |                                                              | lon,lat（经度,纬度），如117.500244, 40.417801 经纬度小数点不超过6位 | 是   |
| destination |                                                              | lon,lat（经度,纬度），如117.500244, 40.417801 经纬度小数点不超过6位 | 是   |
| city        |                                                              | 支持市内公交换乘/跨城公交的起点城市，规则：城市名称/citycode | 是   |
| cityd       |                                                              | 跨城公交规划必填参数。规则：城市名称/citycode                | 否   |
| strategy    | 0                                  1                                  2                                 3                                  5 | 0：最快捷模式; 1：最经济模式; 2：最少换乘模式; 3：最少步行模式; 5：不乘地铁模式 | 否   |
| nightflag   | 0                                  1                         | 是否计算夜班车,1:是；0：否                                   | 否   |

运行

#### **返回结果参数说明**

公交规划的响应结果的格式由请求参数 output 指定。

| **名称**         | **含义**             | **规则说明**                                                 |                                                              |
| ---------------- | -------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| status           | 返回状态             | 值为0或11：成功；0：失败                                     |                                                              |
| info             | 返回的状态信息       | status 为0时，info 返回错误原；否则返回“OK”。详情参阅 [info 状态表](https://lbs.amap.com/api/webservice/info/) |                                                              |
| count            | 公交换乘方案数目     |                                                              |                                                              |
| route            | 公交换乘信息列表     |                                                              |                                                              |
|                  | origin               | 起点坐标                                                     |                                                              |
| destination      | 终点坐标             |                                                              |                                                              |
| distance         | 起点和终点的步行距离 | 单位：米                                                     |                                                              |
| taxi_cost        | 出租车费用           | 单位：元                                                     |                                                              |
| transits         | 公交换乘方案列表     |                                                              |                                                              |
|                  | transit              | 公交换乘方案                                                 |                                                              |
|                  | cost                 | 此换乘方案价格                                               | 单位：元                                                     |
| duration         | 此换乘方案预期时间   | 单位：秒                                                     |                                                              |
| nightflag        | 是否是夜班车         | 0：非夜班车；1：夜班车                                       |                                                              |
| walking_distance | 此方案总步行距离     | 单位：米                                                     |                                                              |
| emergency        |                      | 取值为all时返回                                              |                                                              |
|                  | linetype             | 事件类型                                                     | 1：影响乘坐；2：不影响乘坐                                   |
| eventTagDesc     | 事件标签             | 值为："提示“、”甩站“、”突发“、”停运“                         |                                                              |
| ldescription     | 事件的线路上的文案   |                                                              |                                                              |
| busid            | 线路id               |                                                              |                                                              |
| busname          | 线路名               |                                                              |                                                              |
| segments         | 换乘路段列表         |                                                              |                                                              |
|                  | walking              | 此路段步行导航信息                                           | 详见 [步行方案信息列表](https://lbs.amap.com/api/webservice/guide/api/direction#walking) |
| bus              | 此路段公交导航信息   | 详见 [公交方案信息列表](https://lbs.amap.com/api/webservice/guide/api/direction#bus) |                                                              |
| entrance         | 地铁入口             | 只在地铁路段有值，详见 [出入口信息列表](https://lbs.amap.com/api/webservice/guide/api/direction#alleyway) |                                                              |
| exit             | 地铁出口             | 只在地铁路段有值，详见 [出入口信息列表](https://lbs.amap.com/api/webservice/guide/api/direction#alleyway) |                                                              |
| railway          | 乘坐火车的信息       | 详情见只在地铁路段有值，详见 [火车换乘信息列表](https://lbs.amap.com/api/webservice/guide/api/direction#train) |                                                              |



- 步行方案信息列表

| **名称**         | **含义**           | **规则说明**     |      |
| ---------------- | ------------------ | ---------------- | ---- |
| origin           | 起点坐标           |                  |      |
| destination      | 终点坐标           |                  |      |
| distance         | 每段线路步行距离   | 单位：米         |      |
| duration         | 步行预计时间       | 单位：秒         |      |
|                  | steps              | 步行路段列表     |      |
|                  | instruction        | 此段路的行走介绍 |      |
| road             | 路的名字           |                  |      |
| distance         | 此段路的距离       |                  |      |
| duration         | 此段路预计消耗时间 | 单位：秒         |      |
| polyline         | 此段路的坐标       |                  |      |
| action           | 步行主要动作       |                  |      |
| assistant_action | 步行辅助动作       |                  |      |



- 公交方案信息列表

| **名称**     | **含义**             | **规则说明**                                                |                |
| ------------ | -------------------- | ----------------------------------------------------------- | -------------- |
| buslines     | 步行路段列表         |                                                             |                |
|              | departure_stop       | 此段起乘站信息                                              | 格式如：中关村 |
|              | name                 | 站点名字                                                    |                |
| id           | 站点 id              |                                                             |                |
| location     | 站点经纬度           |                                                             |                |
| arrival_stop | 此段下车站           | 格式如：中关村                                              |                |
|              | name                 | 站点名字                                                    |                |
| id           | 站点 id              |                                                             |                |
| location     | 站点经纬度           |                                                             |                |
| name         | 公交路线名称         | 格式如：445路(南十里居--地铁望京西站)                       |                |
| id           | 公交路线 id          |                                                             |                |
| type         | 公交类型             | 格式如：地铁线路                                            |                |
| distance     | 公交行驶距离         | 单位：米                                                    |                |
| duration     | 公交预计行驶时间     | 单位：秒                                                    |                |
| polyline     | 此路段坐标集         | 格式为坐标串，如：116.481247,39.990704;116.481270,39.990726 |                |
| start_time   | 首班车时间           | 格式如：0600，代表06：00                                    |                |
| end_time     | 末班车时间           | 格式如：2300，代表23：00                                    |                |
| via_num      | 此段途经公交站数     |                                                             |                |
| via_stops    | 此段途经公交站点列表 |                                                             |                |
|              | name                 | 途径公交站点信息                                            |                |
| id           | 公交站点编号         |                                                             |                |
| location     | 公交站点经纬度       |                                                             |                |



- 出入口信息列表

| **name** | **入口名称** |
| -------- | ------------ |
| location | 入口经纬度   |



- 火车换乘信息列表

| **名称**       | **说明**                                   |                |
| -------------- | ------------------------------------------ | -------------- |
| id             | 线路 id 编号                               |                |
| time           | 该线路车段耗时                             |                |
| name           | 线路名称                                   |                |
| trip           | 线路车次号                                 |                |
| distance       | 该 item 换乘段的行车总距离                 |                |
| type           | 线路车次类型                               |                |
| departure_stop | 火车始发站信息                             |                |
|                | id                                         | 上车站点 ID    |
| name           | 上车站点名称                               |                |
| location       | 上车站点经纬度                             |                |
| adcode         | 上车站点所在城市的 adcode                  |                |
| time           | 上车点发车时间                             |                |
| start          | 是否始发站，1表示为始发站，0表示非始发站   |                |
| arrival_stop   | 火车到站信息                               |                |
|                | id                                         | 下车站点 ID    |
| name           | 下车站点名称                               |                |
| location       | 下车站点经纬度                             |                |
| adcode         | 下车站点所在城市的 adcode                  |                |
| time           | 到站时间，如大于24:00，则表示跨天          |                |
| end            | 是否为终点站，1表示为终点站，0表示非终点站 |                |
| via_stop       | 途径站点信息，extensions=all 时返回        |                |
|                | name                                       | 途径站点的名称 |
| id             | 途径站点的 ID                              |                |
| location       | 途径站点的坐标点                           |                |
| time           | 途径站点的进站时间，如大于24:00,则表示跨天 |                |
| wait           | 途径站点的停靠时间，单位：分钟             |                |
| alters         | 聚合的备选方案，extensions=all 时返回      |                |
|                | id                                         | 备选方案 ID    |
| name           | 备选线路名称                               |                |
| spaces         | 仓位及价格信息                             |                |
|                | code                                       | 仓位编码       |
| cost           | 仓位费用                                   |                |



- 火车路线类型表

| **线路类型代码** | **公共交通工具备注** | **线路类型代码** | **公共交通工具备注**   |
| ---------------- | -------------------- | ---------------- | ---------------------- |
| 2010             | 普客火车             | 2015             | T字头的特快火车        |
| 2011             | G字头的高铁火车      | 2016             | K字头的快车火车        |
| 2012             | D字头的动车火车      | 2017             | L字头，Y字头的临时火车 |
| 2013             | C字头的城际火车      | 2018             | S字头的郊区线火车      |
| 2014             | Z字头的直达特快火车  |                  |                        |



- 仓位级别表

| **仓位级别** | **仓位备注**     | **仓位级别** | **仓位备注**     |
| ------------ | ---------------- | ------------ | ---------------- |
| 0            | 不分仓位级别     | 20           | 火车高级软卧下铺 |
| 9            | 特等座           | 21           | 火车商务座       |
| 10           | 火车硬座         | 22           | 长途汽车座席     |
| 11           | 火车软座         | 23           | 长途汽车卧席上铺 |
| 12           | 火车软座1等座    | 24           | 长途汽车卧席中铺 |
| 13           | 火车软座2等座    | 25           | 长途汽车卧席下铺 |
| 14           | 火车硬卧上铺     | 30           | 飞机经济舱       |
| 15           | 火车硬卧中铺     | 31           | 飞机商务舱       |
| 16           | 火车硬卧下铺     | 40           | 客轮经济舱       |
| 17           | 火车软卧上铺     | 41           | 客轮3等舱        |
| 18           | 火车软卧下铺     | 42           | 客轮2等舱        |
| 19           | 火车高级软卧上铺 | 43           | 客轮豪华舱       |







## 驾车路径规划

驾车路径规划 API 可以规划以小客车、轿车通勤出行的方案，并且返回通勤方案的数据。

#### **驾车路径规划 API URL：**

| **URL**                                                  | **请求方式** |
| -------------------------------------------------------- | ------------ |
| https://restapi.amap.com/v3/direction/driving?parameters | GET          |

parameters 代表的参数包括必填参数和可选参数。所有参数均使用和号字符(&)进行分隔。下面的列表枚举了这些参数及其使用规则。

#### **请求参数**

| **参数名称**    | **含义**                                                     | **规则说明**                                                 | **是否必须** | **缺省值** |
| --------------- | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------ | ---------- |
| key             | 用户唯一标识                                                 | 用户在高德地图官网申请                                       | 是           | 无         |
| origin          | 出发点                                                       | 经度在前，纬度在后，经度和纬度用","分割，经纬度小数点后不得超过6位。格式为x1,y1\|x2,y2\|x3,y3。由于在实际使用过程中，存在定位飘点的情况。为了解决此类问题，允许传入多个起点用于计算车头角度。最多允许传入3个坐标对，每对坐标之间距离必须超过2m。 虽然对每对坐标之间长度没有上限，但是如果超过4米会有概率性出现不准确的情况。使用三个点来判断距离和角度的有效性，如果两者都有效，使用第一个点和最后一个点计算的角度设置抓路的角度，规划路径时以最后一个坐标对进行规划。 | 是           | 无         |
| destination     | 目的地                                                       | 经度在前，纬度在后，经度和纬度用","分割，经纬度小数点后不得超过6位。 | 是           | 无         |
| originid        | 出发点 poiid                                                 | 起点为 POI 时，建议填充此值，可提升路线规划准确性            | 否           | 无         |
| destinationid   | 目的地 poiid                                                 | 当终点为 POI 时，建议填充此值                                | 否           | 无         |
| destinationtype | 终点的 poi 类别                                              | 当用户知道终点 POI 的类别时候，建议填充此值                  | 否           | 无         |
| strategy        | 驾车选择策略                                                 | **下方10~20的策略，会返回多条路径规划结果。（高德地图 APP 策略也包含在内，强烈建议从此策略之中选择）****下方策略 0~9的策略，仅会返回一条路径规划结果。****下方策略返回多条路径规划结果**10，返回结果会躲避拥堵，路程较短，尽量缩短时间，**与高德地图的默认策略也就是不进行任何勾选一致**11，返回三个结果包含：时间最短；距离最短；躲避拥堵 （由于有更优秀的算法，建议用10代替）12，返回的结果考虑路况，尽量躲避拥堵而规划路径，**与高德地图的“躲避拥堵”策略一致**13，返回的结果不走高速，**与高德地图“不走高速”策略一致**14，返回的结果尽可能规划收费较低甚至免费的路径，**与高德地图“避免收费”策略一致**15，返回的结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，**与高德地图的“躲避拥堵&不走高速”策略一致**16，返回的结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，**与高德地图的“避免收费&不走高速”策略一致**17，返回路径规划结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，**与高德地图的“躲避拥堵&避免收费”策略一致**18，返回的结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，**与高德地图的“避免拥堵&避免收费&不走高速”策略一致**19，返回的结果会优先选择高速路，**与高德地图的“高速优先”策略一致**20，返回的结果会优先考虑高速路，并且会考虑路况躲避拥堵，**与高德地图的“躲避拥堵&高速优先”策略一致****下方策略仅返回一条路径规划结果**0，速度优先，此路线不一定距离最短1，费用优先，不走收费路段，且耗时最少的路线2，常规最快，综合距离/耗时规划结果3，速度优先，不走快速路，例如京通快速路（因为策略迭代，建议使用13）4，躲避拥堵，但是可能会存在绕路的情况，耗时可能较长5，多策略（同时使用速度优先、费用优先、距离优先三个策略计算路径）。其中必须说明，就算使用三个策略算路，会根据路况不固定的返回一~三条路径规划信息。6，速度优先，不走高速，但是不排除走其余收费路段7，费用优先，不走高速且避免所有收费路段8，躲避拥堵和收费，可能存在走高速的情况，并且考虑路况不走拥堵路线，但有可能存在绕路和时间较长9，躲避拥堵和收费，不走高速 | 否           | 0          |
| waypoints       | 途经点                                                       | 经度和纬度用","分割，经度在前，纬度在后，小数点后不超过6位，坐标点之间用";"分隔最大数目：16个坐标点。如果输入多个途径点，则按照用户输入的顺序进行路径规划 | 否           | 无         |
| avoidpolygons   | 避让区域                                                     | 区域避让，支持32个避让区域，每个区域最多可有16个顶点经度和纬度用","分割，经度在前，纬度在后，小数点后不超过6位，坐标点之间用";"分隔，区域之间用"\|"分隔。如果是四边形则有四个坐标点，如果是五边形则有五个坐标点；避让区域不能超过81平方公里，否则避让区域会失效。 | 否           | 无         |
| province        | 用汉字填入车牌省份缩写，用于判断是否限行                     | 例如：京                                                     | 否           | 无         |
| number          | 填入除省份及标点之外，车牌的字母和数字（需大写）。用于判断限行相关。 | 例如:NH1N11支持6位传统车牌和7位新能源车牌                    | 否           | 无         |
| cartype         | 车辆类型                                                     | 0：普通汽车(默认值) 1：纯电动车 2：插电混动车                | 否           | 0          |
| ferry           | 在路径规划中，是否使用轮渡                                   | 0:使用渡轮(默认) 1:不使用渡轮                                | 否           | 0          |
| roadaggregation | 是否返回路径聚合信息                                         | false:不返回路径聚合信息true:返回路径聚合信息，在 steps 上层增加 roads 做聚合 | 否           | false      |
| nosteps         | 是否返回 steps 字段内容                                      | 当取值为0时，steps 字段内容正常返回；当取值为1时，steps 字段内容为空； | 否           | 0          |
| sig             | 数字签名                                                     | 数字签名认证用户必填                                         | 否           | 无         |
| output          | 返回数据格式类型                                             | 可选值：JSON，XML                                            | 否           | JSON       |
| callback        | 回调函数                                                     | callback 值是用户定义的函数名称，此参数只在 output=JSON 时有效 | 否           | 无         |
| extensions      | 返回结果控制                                                 | 可选值：base/allbase:返回基本信息；all：返回全部信息         | 是           | base       |

#### **服务示例**

```
https://restapi.amap.com/v3/direction/driving?origin=116.481028,39.989643&destination=116.465302,40.004717&extensions=all&output=xml&key=<用户的key>
```

| 参数          | 值                                        | 备注                                                         | 必选 |
| :------------ | :---------------------------------------- | :----------------------------------------------------------- | :--- |
| origin        |                                           | lon,lat（经度,纬度），如117.500244, 40.417801 经纬度小数点不超过6位 | 是   |
| destination   |                                           | lon,lat（经度,纬度），如117.500244, 40.417801 经纬度小数点不超过6位 | 是   |
| extensions    | base                                  all | base:返回基本信息；all：返回全部信息                         | 否   |
| strategy      |                                           | 参见上方 strategy 的说明                                     | 否   |
| waypoints     |                                           | 途经点，最大数目：16个坐标点                                 | 否   |
| avoidpolygons |                                           | 避让区域，如果是四边形则有四个坐标点，如果是五边形则有五个坐标点 | 否   |

运行

#### **返回结果参数说明**

驾车规划的响应结果的格式由请求参数output指定。

| **名称**       | **含义**             | **规则说明**                                                 |                                                              |
| -------------- | -------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| status         | 结果状态值，值为0或1 | 0：请求失败；1：请求成功                                     |                                                              |
| info           | 返回状态说明         | status 为0时，info 返回错误原因，否则返回“OK”。详情参阅 [info 状态表](https://lbs.amap.com/api/webservice/info/) |                                                              |
| count          | 驾车路径规划方案数目 |                                                              |                                                              |
| route          | 驾车路径规划信息列表 |                                                              |                                                              |
|                | origin               | 起点坐标                                                     | 规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位 |
| destination    | 终点坐标             | 规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位 |                                                              |
| taxi_cost      | 打车费用             | 单位：元，注意：extensions=all 时才会返回                    |                                                              |
| paths          | 驾车换乘方案         |                                                              |                                                              |
|                | path                 | 驾车换乘方案                                                 |                                                              |
| distance       | 行驶距离             | 单位：米                                                     |                                                              |
| duration       | 预计行驶时间         | 单位：秒                                                     |                                                              |
| strategy       | 导航策略             |                                                              |                                                              |
| tolls          | 此导航方案道路收费   | 单位：元                                                     |                                                              |
| restriction    | 限行结果             | 0 代表限行已规避或未限行，即该路线没有限行路段1 代表限行无法规避，即该线路有限行路段 |                                                              |
| traffic_lights | 红绿灯个数           |                                                              |                                                              |
| toll_distance  | 收费路段距离         |                                                              |                                                              |
| steps          | 导航路段             |                                                              |                                                              |
|                | step                 | 导航路段                                                     | 详情见 [导航路段信息 step 列表](https://lbs.amap.com/api/webservice/guide/api/direction#limit) |



- 导航路段信息step列表

| **名称**         | **含义**         | **规则说明**                                                 |          |
| ---------------- | ---------------- | ------------------------------------------------------------ | -------- |
| instruction      | 行驶指示         |                                                              |          |
| orientation      | 方向             |                                                              |          |
| road             | 道路名称         |                                                              |          |
| distance         | 此路段距离       | 单位：米                                                     |          |
| tolls            | 此段收费         | 单位：元                                                     |          |
| toll_distance    | 收费路段距离     | 单位：米                                                     |          |
| toll_road        | 主要收费道路     |                                                              |          |
| polyline         | 此路段坐标点串   | 格式为坐标串，如：116.481247,39.990704;116.481270,39.990726  |          |
| action           | 导航主要动作     | 详见 [驾车动作列表](https://lbs.amap.com/api/webservice/guide/api/direction#motor) |          |
| assistant_action | 导航辅助动作     | 详见 [驾车动作列表](https://lbs.amap.com/api/webservice/guide/api/direction#motor) |          |
| tmcs             | 驾车导航详细信息 | 其中包含 tmc 对象                                            |          |
|                  | distance         | 此段路的长度                                                 | 单位：米 |
| status           | 此段路的交通情况 | 未知、畅通、缓行、拥堵、严重拥堵                             |          |
| polyline         | 此段路的轨迹     | 规格：x1,y1;x2,y2                                            |          |
| cities           | 路线途经行政区划 |                                                              |          |
|                  | name             | 名称                                                         |          |
| citycode         | 途径城市编码     |                                                              |          |
| adcode           | 途径区域编码     |                                                              |          |
| districts        |                  |                                                              |          |
|                  | name             | 途径区县名称                                                 |          |
| adcode           | 途径区县 adcode  |                                                              |          |



- 