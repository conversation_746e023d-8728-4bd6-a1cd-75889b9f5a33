# agent/state.py
from typing import TypedDict, Annotated, Sequence, List, Dict, Optional, Any, Union
import operator
from langchain_core.messages import BaseMessage
from datetime import datetime

class ItineraryState(TypedDict, total=False):
    """
    行程规划状态，作为多智能体协作的'会议纪要'
    包含整个规划过程的所有中间数据和最终结果
    """
    # 基础消息流，用特定方式累加
    messages: Annotated[Sequence[BaseMessage], operator.add]
    
    # 用户原始查询和解析后的结构化需求
    original_query: str  # 用户的原始输入
    structured_request: Dict[str, Any]  # 解析后的结构化需求
    
    # 出行基本信息
    departure_city: Optional[str]  # 出发城市
    arrival_city: Optional[str]  # 目的地城市
    departure_place: Optional[str]  # 具体出发地点
    arrival_place: Optional[str]  # 具体目的地点
    departure_coordinates: Optional[Dict[str, float]]  # 出发地经纬度 {lng: 经度, lat: 纬度}
    arrival_coordinates: Optional[Dict[str, float]]  # 目的地经纬度
    travel_date: Optional[str]  # 出行日期
    passenger_count: Optional[int]  # 出行人数
    criteria: Optional[str]  # 用户偏好标准：最便宜、最快、最贵、综合最优
    transport_preference: Optional[str]  # 用户交通偏好：飞机、高铁、火车等
    
    # 大交通规划状态和结果
    major_transport_options: List[Dict[str, Any]]  # 大交通选项列表(高铁/火车)
    selected_major_transport: Optional[Dict[str, Any]]  # 用户选择或系统推荐的大交通方案
    
    # 小交通规划状态和结果
    departure_station: Optional[Dict[str, Any]]  # 出发站信息 {name: 站名, coordinates: {lng, lat}}
    arrival_station: Optional[Dict[str, Any]]  # 到达站信息
    local_transport_options: Dict[str, Any]  # 小交通方案 {departure: {...}, arrival: {...}}
    
    # 地图数据
    map_data: Optional[Dict[str, Any]]  # 用于前端地图展示的数据
    
    # 时间线数据
    timeline: Optional[Dict[str, Any]]  # 行程时间线，包含关键时间点
    
    # 交互式澄清相关状态
    is_clarification_needed: bool  # 是否需要向用户请求澄清
    clarification_question: Optional[str]  # 向用户提问的澄清问题
    clarification_options: Optional[List[str]]  # 可选的澄清选项
    user_clarification_response: Optional[str]  # 用户的澄清回答
    
    # 工具调用状态和结果
    current_tool: Optional[str]  # 当前正在调用的工具
    tool_results: Dict[str, Dict[str, Any]]  # 工具调用结果 {工具名: {status: 'success'/'error', data: 结果}}
    errors: List[Dict[str, Any]]  # 错误记录 [{tool: 工具名, message: 错误信息, timestamp: 时间}]
    
    # 失败计数和递归控制
    failed_searches: Dict[str, int]  # 搜索失败记录 {search_key: 失败次数}
    major_transport_failure_count: int  # 大交通查询失败的总次数
    clarification_count: int  # 用户澄清次数
    alternative_transport: Optional[str]  # 用户提供的备选交通方式
    
    # 执行流程控制
    current_step: str  # 当前执行步骤
    next_step: Optional[str]  # 下一步执行步骤
    execution_history: List[str]  # 执行历史记录
    
    # 最终规划结果
    final_plan: Optional[Dict[str, Any]]  # 最终行程计划，包含所有细节
    thinking_process: List[str]  # 规划过程中的思考步骤记录
    
    # 调试信息
    debug_info: Dict[str, Any]  # 调试信息，包含各种中间状态和日志

    
    