# agent/nodes.py
from typing import Dict, Any, List, Optional, TypedDict, Literal, Union
from datetime import datetime, timedelta
import traceback
import json
import re

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_community.chat_models import ChatZhipuAI
from langchain.tools import BaseTool

from src.agent.state import ItineraryState
from src.tools.train_search_tool import search_train
from src.tools.flight_search_tool import search_flight
from src.tools.search_tool import get_weather
from src.tools.gaode_api_tool import (
    get_city_from_coordinates,
    get_railway_station,
    get_taxi_route,
    get_transit_route,
    is_valid_coordinates
)
from src.tools.travel_advice_generator import TravelAdviceGenerator

def get_airport_info(city: str) -> dict:
    """获取城市机场信息"""
    # 机场信息映射
    airport_mapping = {
        "北京": {"name": "北京首都国际机场", "location": "116.584556,40.080111"},
        "上海": {"name": "上海浦东国际机场", "location": "121.805214,31.143378"},
        "广州": {"name": "广州白云国际机场", "location": "113.298786,23.392436"},
        "深圳": {"name": "深圳宝安国际机场", "location": "113.810833,22.639444"},
        "成都": {"name": "成都双流国际机场", "location": "103.947086,30.578528"},
        "重庆": {"name": "重庆江北国际机场", "location": "106.641678,29.719217"},
        "杭州": {"name": "杭州萧山国际机场", "location": "120.434342,30.229503"},
        "南京": {"name": "南京禄口国际机场", "location": "118.862025,31.742042"},
        "武汉": {"name": "武汉天河国际机场", "location": "114.208417,30.783556"},
        "西安": {"name": "西安咸阳国际机场", "location": "108.751592,34.447119"},
        "天津": {"name": "天津滨海国际机场", "location": "117.346183,39.124353"},
        "青岛": {"name": "青岛胶东国际机场", "location": "120.374722,36.266111"},
        "厦门": {"name": "厦门高崎国际机场", "location": "118.127958,24.544178"},
        "昆明": {"name": "昆明长水国际机场", "location": "102.929167,25.101944"},
        "大连": {"name": "大连周水子国际机场", "location": "121.539167,38.965556"},
        "沈阳": {"name": "沈阳桃仙国际机场", "location": "123.483333,41.639722"},
        "长沙": {"name": "长沙黄花国际机场", "location": "113.219633,28.189158"},
        "郑州": {"name": "郑州新郑国际机场", "location": "113.840889,34.519722"},
        "合肥": {"name": "合肥新桥国际机场", "location": "117.298056,31.781111"},
        "福州": {"name": "福州长乐国际机场", "location": "119.663333,25.935056"},
        "珠海": {"name": "珠海金湾机场", "location": "113.376111,22.006389"}
    }

    airport_info = airport_mapping.get(city)
    if airport_info:
        lng, lat = airport_info["location"].split(",")
        return {
            "status": "success",
            "name": airport_info["name"],
            "location": lng + "," + lat,
            "type": "机场"
        }
    else:
        return {
            "status": "error",
            "message": f"未找到{city}的机场信息"
        }


# 初始化智普AI模型，用于各个节点
llm = ChatZhipuAI(model="glm-4", temperature=0.01)

def planner_node(state: ItineraryState) -> ItineraryState:
    """
    总规划师节点：负责解析用户需求，安排任务，并决定下一步执行哪个专家节点。
    这是工作流的核心控制节点，每个专家节点执行完后都会回到这里。
    使用LLM进行智能决策和任务规划。
    """
    print("\n[规划师] 🧠 正在分析当前状态并决定下一步行动...")

    # 🔍 DEBUG: 检查传入的state
    print(f"[规划师] 🔍 DEBUG - 传入的criteria: '{state.get('criteria', '未设置')}'")
    print(f"[规划师] 🔍 DEBUG - 传入的original_query: '{state.get('original_query', '未设置')}'")
    print(f"[规划师] 🔍 DEBUG - state中的所有键: {list(state.keys())}")

    # 追踪执行记录
    if "execution_history" not in state:
        state["execution_history"] = []
    state["execution_history"].append("planner")
    state["current_step"] = "planner"

    # 确保state中有next_step字段
    if "next_step" not in state:
        state["next_step"] = "planner"

    # 第一次运行时，使用LLM解析用户需求并制定规划策略
    if "structured_request" not in state or not state.get("structured_request"):
        # 获取最后一条用户消息
        messages = state.get("messages", [])
        last_user_message = next((msg for msg in reversed(messages)
                               if isinstance(msg, HumanMessage)), None)

        if last_user_message:
            user_query = last_user_message.content
            state["original_query"] = user_query

            # 使用LLM进行智能需求分析和规划
            print("[规划师] 🤖 使用AI分析用户需求并制定规划策略...")

            # 构建智能分析提示
            analysis_prompt = f"""
            你是一位资深的旅行规划师，具有丰富的经验和常识判断能力。

            用户查询：{user_query}

            已知信息：
            - 出发地：{state.get('departure_place', '未知')}
            - 目的地：{state.get('arrival_place', '未知')}
            - 出行日期：{state.get('travel_date', '未知')}
            - 偏好标准：{state.get('criteria', '未知')}

            **智能分析要求**：
            1. 如果用户明确表达了偏好（如"最快"、"最慢"、"最便宜"），就不要再问相关问题
            2. 如果用户说"最慢"，应该推荐普通火车而不是高铁
            3. 如果用户说"最快"，长距离应该推荐飞机
            4. 只有在真正缺少关键信息时才需要澄清
            5. 使用常识判断，不要问显而易见的问题

            **地理常识（重要）**：
            - 珠海、广州、深圳、汕头、湛江等都在广东省内，属于"省内"出行
            - 北京、上海、天津是直辖市，与其他省份的出行属于"跨省"
            - 省内短距离（<500公里）优先推荐高铁、汽车，不推荐飞机
            - 跨省长距离（>1000公里）可以推荐飞机

            请分析以下内容并以JSON格式返回：
            1. 提取和验证出行基本信息
            2. 分析出行距离和交通方式建议
            3. 识别真正需要澄清的关键信息（不要问无意义的问题）
            4. 制定合理的规划步骤

            返回格式：
            {{
                "出发地": "具体地点",
                "目的地": "具体地点",
                "日期": "YYYY-MM-DD",
                "出行类型": "市内/省内/跨省",
                "建议交通方式": ["高铁", "飞机", "汽车"],
                "需要澄清": ["如果有需要澄清的问题"],
                "规划步骤": ["步骤1", "步骤2", "..."],
                "风险评估": "可能遇到的问题",
                "预计耗时": "整个行程预计时间"
            }}
            """

            try:
                analysis_messages = [
                    SystemMessage(content="你是专业的旅行规划AI助手，擅长分析用户需求并制定详细规划。"),
                    HumanMessage(content=analysis_prompt)
                ]

                # 添加超时保护
                import time
                start_time = time.time()
                response = llm.invoke(analysis_messages)
                end_time = time.time()
                print(f"[大交通专家] ⏱️ AI分析耗时: {end_time - start_time:.2f}秒")

                response_content = response.content.strip()

                # 解析LLM响应
                if response_content.startswith('```json'):
                    response_content = response_content[7:-3]
                elif response_content.startswith('```'):
                    response_content = response_content[3:-3]

                # 查找JSON部分
                json_start = response_content.find('{')
                json_end = response_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    response_content = response_content[json_start:json_end]

                analysis_result = json.loads(response_content)

                # 更新状态信息
                state["structured_request"] = analysis_result

                # 🔧 修复criteria问题：优先使用state中的criteria，作为备用才从查询中提取
                print(f"[规划师] 🔍 检查用户查询: '{user_query}'")
                current_criteria = state.get('criteria', '未知')
                print(f"[规划师] 🔍 当前criteria: '{current_criteria}'")

                # 如果state中已经有有效的criteria，优先使用它
                if current_criteria and current_criteria != '未知' and current_criteria != '综合最优':
                    print(f"[规划师] ✅ 使用state中的用户偏好: {current_criteria}")
                else:
                    # 作为备用，从查询文本中提取偏好（支持带引号的匹配）
                    if "最快" in user_query or "'最快'" in user_query or '"最快"' in user_query:
                        state["criteria"] = "最快"
                        print(f"[规划师] 🔧 从查询中检测到用户偏好：最快 (原: {current_criteria})")
                    elif "最慢" in user_query or "'最慢'" in user_query or '"最慢"' in user_query:
                        state["criteria"] = "最慢"
                        print(f"[规划师] 🔧 从查询中检测到用户偏好：最慢 (原: {current_criteria})")
                    elif "最便宜" in user_query or "'最便宜'" in user_query or '"最便宜"' in user_query:
                        state["criteria"] = "最便宜"
                        print(f"[规划师] 🔧 从查询中检测到用户偏好：最便宜 (原: {current_criteria})")
                    elif "最贵" in user_query or "'最贵'" in user_query or '"最贵"' in user_query or "豪华" in user_query:
                        state["criteria"] = "最贵"
                        print(f"[规划师] 🔧 从查询中检测到用户偏好：最贵/豪华 (原: {current_criteria})")
                    else:
                        print(f"[规划师] ℹ️ 查询中未检测到明确偏好，保持原criteria: {current_criteria}")

                print(f"[规划师] 🎯 最终确定的用户偏好: '{state.get('criteria', '综合最优')}'")

                # 记录AI分析过程
                if "thinking_process" not in state:
                    state["thinking_process"] = []

                state["thinking_process"].append(f"🤖 AI分析：{user_query}")
                state["thinking_process"].append(f"📍 识别出行：从{analysis_result.get('出发地', '未知')}到{analysis_result.get('目的地', '未知')}")
                state["thinking_process"].append(f"🚀 出行类型：{analysis_result.get('出行类型', '未知')}")
                state["thinking_process"].append(f"🛤️ 建议交通：{', '.join(analysis_result.get('建议交通方式', []))}")

                if analysis_result.get('需要澄清'):
                    state["thinking_process"].append(f"❓ 需要澄清：{', '.join(analysis_result['需要澄清'])}")

                # 检查是否需要用户澄清
                if analysis_result.get('需要澄清') and len(analysis_result['需要澄清']) > 0:
                    state["is_clarification_needed"] = True
                    state["clarification_question"] = f"为了更好地为您规划行程，我需要确认以下信息：{'; '.join(analysis_result['需要澄清'])}"
                    state["next_step"] = "ask_user"
                    print(f"[规划师] ❓ 需要用户澄清：{state['clarification_question']}")
                    return state

                print(f"[规划师] ✅ AI分析完成：{analysis_result.get('出行类型', '未知')}出行，建议{', '.join(analysis_result.get('建议交通方式', []))}")

            except Exception as e:
                print(f"[规划师] ⚠️ AI分析失败，使用预设信息: {str(e)}")
                # 回退到预设信息
                structured_request = {
                    "出发地": state.get("departure_place", ""),
                    "目的地": state.get("arrival_place", ""),
                    "日期": state.get("travel_date", "")
                }
                state["structured_request"] = structured_request

        # 确保城市信息总是被提取（无论AI分析是否成功）
        if "departure_city" not in state and "departure_coordinates" in state:
            try:
                # 尝试从坐标获取城市名
                from src.tools.gaode_api_tool import get_city_from_coordinates
                coords = state["departure_coordinates"]
                city_result = get_city_from_coordinates(coords["lng"], coords["lat"])
                if city_result and city_result.get("city"):
                    state["departure_city"] = city_result["city"]
                    print(f"[规划师] ✅ 从坐标获取到出发城市: {city_result['city']}")
            except Exception as e:
                print(f"[规划师] ⚠️ 从坐标获取出发城市失败: {str(e)}")

        if "arrival_city" not in state and "arrival_coordinates" in state:
            try:
                # 尝试从坐标获取城市名
                from src.tools.gaode_api_tool import get_city_from_coordinates
                coords = state["arrival_coordinates"]
                city_result = get_city_from_coordinates(coords["lng"], coords["lat"])
                if city_result and city_result.get("city"):
                    state["arrival_city"] = city_result["city"]
                    print(f"[规划师] ✅ 从坐标获取到目的城市: {city_result['city']}")
            except Exception as e:
                print(f"[规划师] ⚠️ 从坐标获取目的城市失败: {str(e)}")
                
                # 生成初步规划计划列表
                if "thinking_process" not in state:
                    state["thinking_process"] = []
                
                departure_place = state.get("departure_place", "出发地")
                arrival_place = state.get("arrival_place", "目的地")
                travel_date = state.get("travel_date", "未指定日期")
                
                # 添加行程规划概述
                planning_summary = f"正在为您规划从{departure_place}到{arrival_place}的行程，出发日期为{travel_date}。以下是我的规划步骤："
                state["thinking_process"].append(planning_summary)
                
                # 添加详细的规划步骤
                plan_steps = [
                    f"1. 分析出行需求：从{departure_place}到{arrival_place}，确定最佳交通方式",
                    f"2. 搜索最优高铁/火车方案：考虑时间、价格和舒适度因素",
                    f"3. 规划从{departure_place}到出发车站的本地交通路线",
                    f"4. 规划从到达车站到{arrival_place}的本地交通路线",
                    f"5. 分析{arrival_place}周边环境，提供出行提示",
                    f"6. 整合所有信息，生成完整行程时间轴",
                    f"7. 优化行程安排，确保时间合理、衔接顺畅"
                ]
                
                for step in plan_steps:
                    state["thinking_process"].append(step)
                
                # 添加行程建议
                travel_tips = f"根据目前信息分析，您需要预留充足时间前往车站。建议提前规划从{departure_place}到火车站的交通路线，以及从到达站到{arrival_place}的路线。"
                state["thinking_process"].append(travel_tips)
            else:
                # 使用LLM解析用户查询为结构化请求
                system_prompt = """
                你是一个专业的旅行需求分析师。你的任务是将用户的自然语言旅行请求解析为结构化数据。
                提取以下信息（如果存在）：
                1. 出发地（城市或具体地点）
                2. 目的地（城市或具体地点）
                3. 出行日期
                4. 出行人数
                5. 任何特殊偏好或要求
                
                以JSON格式返回，仅返回JSON，不要有其他文本。
                """
                
                parse_messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"请分析这个旅行请求：{user_query}")
                ]
                
                try:
                    response = llm.invoke(parse_messages)
                    response_content = response.content.strip()
                    
                    # 尝试处理可能的非JSON响应
                    if not response_content.startswith('{'):
                        # 查找第一个JSON开始的位置
                        json_start = response_content.find('{')
                        if json_start >= 0:
                            response_content = response_content[json_start:]
                    
                    # 查找JSON结束的位置
                    json_end = response_content.rfind('}') + 1
                    if json_end > 0:
                        response_content = response_content[:json_end]
                    
                    # 解析JSON
                    parsed_json = json.loads(response_content)
                    state["structured_request"] = parsed_json
                    print(f"[规划师] ✅ 已解析用户需求: {json.dumps(parsed_json, ensure_ascii=False)}")
                    
                    # 提取出发地和目的地信息
                    if "出发地" in parsed_json:
                        state["departure_place"] = parsed_json["出发地"]
                        # 暂时假设城市名是出发地的一部分，后续会由专家节点精确确定
                        if "城市" in parsed_json:
                            state["departure_city"] = parsed_json["城市"]
                    
                    if "目的地" in parsed_json:
                        state["arrival_place"] = parsed_json["目的地"]
                        # 同样假设
                        if "目的城市" in parsed_json:
                            state["arrival_city"] = parsed_json["目的城市"]
                    
                    if "日期" in parsed_json:
                        state["travel_date"] = parsed_json["日期"]
                    
                    if "人数" in parsed_json:
                        state["passenger_count"] = parsed_json["人数"]
                    
                    # 生成初步规划计划列表
                    if "thinking_process" not in state:
                        state["thinking_process"] = []
                    
                    departure_place = state.get("departure_place", "出发地")
                    arrival_place = state.get("arrival_place", "目的地")
                    travel_date = state.get("travel_date", "未指定日期")
                    
                    # 添加行程规划概述
                    planning_summary = f"正在为您规划从{departure_place}到{arrival_place}的行程，出发日期为{travel_date}。以下是我的规划步骤："
                    state["thinking_process"].append(planning_summary)
                    
                    # 添加详细的规划步骤
                    plan_steps = [
                        f"1. 分析出行需求：从{departure_place}到{arrival_place}，确定最佳交通方式",
                        f"2. 搜索最优高铁/火车方案：考虑时间、价格和舒适度因素",
                        f"3. 规划从{departure_place}到出发车站的本地交通路线",
                        f"4. 规划从到达车站到{arrival_place}的本地交通路线",
                        f"5. 分析{arrival_place}周边环境，提供出行提示",
                        f"6. 整合所有信息，生成完整行程时间轴",
                        f"7. 优化行程安排，确保时间合理、衔接顺畅"
                    ]
                    
                    for step in plan_steps:
                        state["thinking_process"].append(step)
                    
                    # 添加行程建议
                    travel_tips = f"根据目前信息分析，您需要预留充足时间前往车站。建议提前规划从{departure_place}到火车站的交通路线，以及从到达站到{arrival_place}的路线。"
                    state["thinking_process"].append(travel_tips)
                    
                except Exception as e:
                    print(f"[规划师] ❌ 解析用户需求时出错: {e}")
                    error_detail = {"tool": "parse_request", "message": str(e), 
                                  "timestamp": datetime.now().isoformat()}
                    if "errors" not in state:
                        state["errors"] = []
                    state["errors"].append(error_detail)
                    
                    # 尝试通过提问澄清
                    state["is_clarification_needed"] = True
                    state["clarification_question"] = "请明确告诉我您的出发地、目的地和出行日期，这样我才能为您规划行程。"
                    state["next_step"] = "ask_user"
                    return state
    
    # 使用LLM进行智能决策，决定下一步行动
    print("[规划师] 🤖 使用AI分析当前状态并决定下一步行动...")

    # 构建当前状态摘要
    current_status = {
        "已有信息": {
            "出发地": state.get("departure_place", "未知"),
            "目的地": state.get("arrival_place", "未知"),
            "出发城市": state.get("departure_city", "未知"),
            "目的城市": state.get("arrival_city", "未知"),
            "日期": state.get("travel_date", "未知"),
            "坐标信息": "已有" if state.get("departure_coordinates") and state.get("arrival_coordinates") else "缺失"
        },
        "已完成任务": {
            "大交通查询": "已完成" if state.get("major_transport_options") else "未完成",
            "大交通选择": "已完成" if state.get("selected_major_transport") else "未完成",
            "小交通规划": "已完成" if state.get("local_transport_options") else "未完成",
            "最终合成": "已完成" if state.get("final_plan") else "未完成"
        },
        "执行历史": state.get("execution_history", []),
        "错误记录": len(state.get("errors", []))
    }

    decision_prompt = f"""
    你是一位专业的旅行规划协调员，需要分析当前规划状态并决定下一步行动。

    当前状态：
    {json.dumps(current_status, ensure_ascii=False, indent=2)}

    可选的下一步行动：
    1. "major_transport" - 查询大交通方案（高铁/火车/飞机）
    2. "local_transport" - 规划小交通方案（打车/公交）
    3. "web_search" - 获取辅助信息（天气、景点等）
    4. "synthesis" - 整合所有信息生成最终规划
    5. "ask_user" - 向用户询问澄清信息
    6. "end" - 结束规划流程

    决策规则：
    - 如果缺少基本信息（出发地、目的地、城市），选择 "ask_user"
    - 如果有基本信息但没有大交通方案，选择 "major_transport"
    - 如果有大交通方案但没有小交通方案，选择 "local_transport"
    - 如果大小交通都有但缺少辅助信息，选择 "web_search"
    - 如果所有信息齐全，选择 "synthesis"
    - 如果已有最终规划，选择 "end"

    请分析当前状态并返回JSON格式的决策：
    {{
        "下一步行动": "action_name",
        "决策理由": "详细说明为什么选择这个行动",
        "预期结果": "这个行动预期达成什么目标",
        "风险评估": "可能遇到的问题"
    }}
    """

    try:
        decision_messages = [
            SystemMessage(content="你是专业的旅行规划协调AI，擅长分析状态并做出最优决策。"),
            HumanMessage(content=decision_prompt)
        ]

        response = llm.invoke(decision_messages)
        response_content = response.content.strip()

        # 解析LLM响应
        if response_content.startswith('```json'):
            response_content = response_content[7:-3]
        elif response_content.startswith('```'):
            response_content = response_content[3:-3]

        json_start = response_content.find('{')
        json_end = response_content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            response_content = response_content[json_start:json_end]

        decision_result = json.loads(response_content)

        # 记录AI决策过程
        if "thinking_process" not in state:
            state["thinking_process"] = []

        state["thinking_process"].append(f"🤖 AI决策：{decision_result.get('决策理由', '未知理由')}")
        state["thinking_process"].append(f"🎯 下一步：{decision_result.get('下一步行动', '未知行动')}")
        state["thinking_process"].append(f"📈 预期：{decision_result.get('预期结果', '未知结果')}")

        next_action = decision_result.get("下一步行动", "ask_user")
        state["next_step"] = next_action

        print(f"[规划师] 🎯 AI决策：{next_action} - {decision_result.get('决策理由', '未知理由')}")

        return state

    except Exception as e:
        print(f"[规划师] ⚠️ AI决策失败，使用规则决策: {str(e)}")

        # 回退到规则决策
        if not state.get("departure_city") or not state.get("arrival_city"):
            state["next_step"] = "ask_user"
            state["is_clarification_needed"] = True
            state["clarification_question"] = "请提供出发城市和目的城市信息。"
        elif not state.get("major_transport_options"):
            state["next_step"] = "major_transport"
        elif not state.get("local_transport_options"):
            state["next_step"] = "local_transport"
        elif not state.get("final_plan"):
            state["next_step"] = "synthesis"
        else:
            state["next_step"] = "end"
        return state
    
    # 如果需要更多信息，但目前无法自动获取，则需要向用户提问
    if not state.get("departure_city") or not state.get("arrival_city"):
        print(f"[规划师] ❓ 缺少必要信息，需要向用户澄清...")
        state["is_clarification_needed"] = True
        state["clarification_question"] = "为了规划您的行程，请告诉我您的出发城市和目的地城市。"
        state["next_step"] = "ask_user"
        return state
    
    # 默认回到自己，等待更多信息
    state["next_step"] = "planner"
    return state


def major_transport_node(state: ItineraryState) -> ItineraryState:
    """
    大交通专家节点：负责规划城市间的高铁/火车/飞机交通方案。
    使用LLM进行智能分析，调用相应工具获取交通信息，并进行方案评估。
    """
    try:
        print("\n[大交通专家] 🚄 正在规划城市间交通方案...")
        print(f"[大交通专家] 🔍 DEBUG - 函数开始执行，state类型: {type(state)}")
        print(f"[大交通专家] 🔍 DEBUG - state内容: {dict(state) if hasattr(state, 'keys') else 'state不是字典类型'}")

        # 追踪执行记录
        if "execution_history" not in state:
            state["execution_history"] = []
        state["execution_history"].append("major_transport")
        state["current_step"] = "major_transport"

        # 确保state中有next_step字段
        if "next_step" not in state:
            state["next_step"] = "planner"

        print(f"[大交通专家] ✅ DEBUG - 基础设置完成")

        return _major_transport_node_impl(state)

    except Exception as e:
        print(f"[大交通专家] ❌ CRITICAL ERROR - 函数执行失败: {str(e)}")
        import traceback
        traceback.print_exc()

        # 确保返回有效状态
        if "next_step" not in state:
            state["next_step"] = "planner"
        if "execution_history" not in state:
            state["execution_history"] = []
        state["execution_history"].append("major_transport_error")

        # 记录错误
        if "errors" not in state:
            state["errors"] = []
        state["errors"].append({
            "tool": "major_transport",
            "message": f"节点执行失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })

        return state

def _major_transport_node_impl(state: ItineraryState) -> ItineraryState:
    """major_transport_node的实际实现"""
    
    # 检查必要参数
    departure_city = state.get("departure_city")
    arrival_city = state.get("arrival_city")
    travel_date = state.get("travel_date")
    criteria = state.get("criteria", "综合最优")

    # 获取用户的交通偏好
    user_transport_preference = state.get("transport_preference", "")
    print(f"[大交通专家] 🎯 用户交通偏好: '{user_transport_preference}'")

    print(f"[大交通专家] 🔍 检查参数 - 出发城市: {departure_city}, 目的城市: {arrival_city}, 日期: {travel_date}")
    print(f"[大交通专家] 🔍 检查坐标 - 出发坐标: {state.get('departure_coordinates')}, 目的坐标: {state.get('arrival_coordinates')}")

    # 如果没有城市信息，尝试从坐标提取
    if not departure_city and "departure_coordinates" in state:
        try:
            print(f"[大交通专家] 🔄 尝试从出发坐标提取城市信息...")
            from src.tools.gaode_api_tool import get_city_from_coordinates
            coords = state["departure_coordinates"]
            print(f"[大交通专家] 📍 出发坐标: {coords}")
            city_result = get_city_from_coordinates(coords["lng"], coords["lat"])
            print(f"[大交通专家] 🏙️ 城市查询结果: {city_result}")
            if city_result and city_result.get("city"):
                departure_city = city_result["city"]
                state["departure_city"] = departure_city
                print(f"[大交通专家] ✅ 从坐标提取出发城市: {departure_city}")
            else:
                print(f"[大交通专家] ❌ 城市查询结果为空或无效")
        except Exception as e:
            print(f"[大交通专家] ⚠️ 从坐标提取出发城市失败: {str(e)}")
            import traceback
            traceback.print_exc()

    if not arrival_city and "arrival_coordinates" in state:
        try:
            print(f"[大交通专家] 🔄 尝试从目的坐标提取城市信息...")
            from src.tools.gaode_api_tool import get_city_from_coordinates
            coords = state["arrival_coordinates"]
            print(f"[大交通专家] 📍 目的坐标: {coords}")
            city_result = get_city_from_coordinates(coords["lng"], coords["lat"])
            print(f"[大交通专家] 🏙️ 城市查询结果: {city_result}")
            if city_result and city_result.get("city"):
                arrival_city = city_result["city"]
                state["arrival_city"] = arrival_city
                print(f"[大交通专家] ✅ 从坐标提取目的城市: {arrival_city}")
            else:
                print(f"[大交通专家] ❌ 城市查询结果为空或无效")
        except Exception as e:
            print(f"[大交通专家] ⚠️ 从坐标提取目的城市失败: {str(e)}")
            import traceback
            traceback.print_exc()

    if not departure_city or not arrival_city:
        print(f"[大交通专家] ❌ 仍然缺少城市信息 - 出发城市: {departure_city}, 目的城市: {arrival_city}")

        # 记录错误
        error_detail = {
            "tool": "major_transport",
            "message": "缺少出发城市或目的地城市信息",
            "timestamp": datetime.now().isoformat()
        }
        if "errors" not in state:
            state["errors"] = []
        state["errors"].append(error_detail)

        # 向用户请求澄清
        state["is_clarification_needed"] = True
        state["clarification_question"] = "请明确您的出发城市和目的地城市，以便我为您查询交通信息。"
        state["next_step"] = "ask_user"
        return state

    # 使用LLM分析最佳交通方式
    print("[大交通专家] 🤖 使用AI分析最佳交通方式...")
    print("[大交通专家] 🚨 代码已更新 - 测试标记 2025-07-25")

    transport_analysis_prompt = f"""
    你是一位专业的交通规划专家，需要分析两个城市之间的最佳交通方式。

    出行信息：
    - 出发城市：{departure_city}
    - 目的城市：{arrival_city}
    - 出行日期：{travel_date}
    - 用户偏好：{criteria}
    - 用户明确要求的交通方式：{user_transport_preference if user_transport_preference else '无特殊要求'}

    **智能分析规则（必须严格遵守）**：
    1. 如果用户明确指定了交通方式（如"飞机"、"高铁"、"火车"），必须优先考虑用户的明确要求！
    2. 🚨 如果用户偏好是"最快"且距离>1000公里，必须推荐飞机！这是硬性规则！
       - 珠海到北京：飞机3小时15分钟 vs 火车11小时17分钟
       - 广州到北京：飞机3小时 vs 火车8小时
       - 深圳到北京：飞机3小时 vs 火车10小时
    3. 🚨 省内短距离（如珠海到汕头、广州到深圳）不推荐飞机，优先高铁！
    4. 如果用户偏好是"最慢"，应该推荐普通火车而不是高铁！
    5. 如果用户偏好是"最便宜"，推荐火车！
    6. 使用常识判断，不要违背基本逻辑！时间对比是客观事实！

    **地理常识**：
    - 广东省内：珠海、广州、深圳、汕头、湛江等，距离通常<500公里，优先高铁
    - 跨省长距离：如广东到北京、上海等，距离>1000公里，可考虑飞机

    请分析以下内容并以JSON格式返回：
    1. 推荐的交通方式（高铁、火车、飞机）
    2. 各种交通方式的优缺点分析
    3. 基于用户偏好的最佳选择（必须符合常识！）
    4. 可能遇到的问题和备选方案

    **特别注意**：
    - 珠海到北京是典型的长距离路线（>1000公里）
    - 如果用户要求"最快"，飞机3小时 vs 火车11小时，必须选择飞机！
    - 省内短距离（如长沙到衡阳）不推荐飞机，优先高铁！
    - 不要被传统思维束缚，要基于实际时间对比！

    **JSON格式要求**：
    - 必须是标准的JSON格式
    - 所有字符串都要用双引号
    - 对象之间必须用逗号分隔
    - 不要添加注释或额外的说明

    返回格式（严格按照此格式）：
    {{
        "推荐交通方式": ["飞机", "高铁"],
        "首选方案": "飞机",
        "分析结果": {{
            "飞机": {{"优点": ["速度最快", "时间效率高"], "缺点": ["价格较高", "受天气影响"], "适用性": "高"}},
            "高铁": {{"优点": ["准点率高", "舒适度好"], "缺点": ["时间较长"], "适用性": "中"}},
            "火车": {{"优点": ["价格便宜"], "缺点": ["时间最长"], "适用性": "低"}}
        }},
        "距离评估": "长距离",
        "预计查询成功率": "高",
        "备选建议": "如果首选方案失败的备选方案"
    }}
    """

    try:
        analysis_messages = [
            SystemMessage(content="你是专业的交通规划AI专家，擅长分析城市间交通方案。"),
            HumanMessage(content=transport_analysis_prompt)
        ]

        response = llm.invoke(analysis_messages)
        response_content = response.content.strip()

        # 解析LLM响应
        if response_content.startswith('```json'):
            response_content = response_content[7:-3]
        elif response_content.startswith('```'):
            response_content = response_content[3:-3]

        json_start = response_content.find('{')
        json_end = response_content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            response_content = response_content[json_start:json_end]

        # 尝试修复常见的JSON格式问题
        try:
            analysis_result = json.loads(response_content)
        except json.JSONDecodeError as e:
            print(f"[大交通专家] 🔧 尝试修复JSON格式问题: {str(e)}")
            try:
                import re
                # 修复常见的JSON格式问题
                fixed_content = response_content

                # 1. 在 } 和 " 之间添加逗号（最常见的问题）
                fixed_content = re.sub(r'}\s*\n\s*"', '},\n  "', fixed_content)

                # 2. 在 " 和 { 之间添加冒号和空格
                fixed_content = re.sub(r'"\s*\n\s*{', '": {', fixed_content)

                # 3. 移除最后一个多余的逗号
                fixed_content = re.sub(r',\s*}', '}', fixed_content)
                fixed_content = re.sub(r',\s*]', ']', fixed_content)

                # 4. 移除注释行（如果有）
                fixed_content = re.sub(r'//.*?\n', '\n', fixed_content)

                # 5. 修复缺少逗号的常见模式
                # 在 "value" 和 "key" 之间添加逗号
                fixed_content = re.sub(r'"\s*\n\s*"', '",\n  "', fixed_content)

                print(f"[大交通专家] 🔧 修复后的JSON: {fixed_content[:200]}...")
                analysis_result = json.loads(fixed_content)
                print(f"[大交通专家] ✅ JSON修复成功")

            except Exception as fix_error:
                print(f"[大交通专家] ❌ JSON修复失败: {str(fix_error)}")
                raise e  # 抛出原始错误

        # 记录AI分析过程
        if "thinking_process" not in state:
            state["thinking_process"] = []

        state["thinking_process"].append(f"🚄 交通分析：从{departure_city}到{arrival_city}")
        state["thinking_process"].append(f"🎯 AI推荐：{analysis_result.get('首选方案', '高铁')}（{analysis_result.get('距离评估', '未知距离')}）")

        # 根据分析结果选择查询策略
        preferred_transport = analysis_result.get("首选方案", "高铁")
        recommended_transports = analysis_result.get("推荐交通方式", ["高铁"])

        print(f"[大交通专家] 🎯 AI推荐交通方式：{preferred_transport}")
        print(f"[大交通专家] 📋 AI分析结果: {analysis_result}")

    except json.JSONDecodeError as e:
        print(f"[大交通专家] ⚠️ JSON解析失败: {str(e)}")
        print(f"[大交通专家] 🔍 AI响应内容: {response_content if 'response_content' in locals() else '无响应'}")

        # 尝试从响应中提取首选方案
        if 'response_content' in locals() and '"首选方案"' in response_content:
            if '"飞机"' in response_content:
                preferred_transport = "飞机"
                print(f"[大交通专家] 🔧 从响应中提取到首选方案: 飞机")
            elif '"高铁"' in response_content:
                preferred_transport = "高铁"
                print(f"[大交通专家] 🔧 从响应中提取到首选方案: 高铁")
            else:
                preferred_transport = "高铁"
        else:
            preferred_transport = "高铁"
        recommended_transports = ["高铁"]

    except Exception as e:
        print(f"[大交通专家] ⚠️ AI分析失败，使用默认策略: {str(e)}")
        print(f"[大交通专家] 🔍 AI响应内容: {response_content if 'response_content' in locals() else '无响应'}")
        import traceback
        traceback.print_exc()
        preferred_transport = "高铁"
        recommended_transports = ["高铁"]
    
    # 如果已经有大交通选项，不重复查询
    if state.get("major_transport_options"):
        print("[大交通专家] ℹ️ 已有大交通方案，无需重复查询")
        state["next_step"] = "planner"
        return state
    
    # 添加失败计数，防止无限循环
    failure_count = state.get("major_transport_failure_count", 0)
    search_key = f"{departure_city}_{arrival_city}_{travel_date}"
    failed_searches = state.get("failed_searches", {})
    
    # 检查是否已经尝试过这个搜索且失败了多次
    if search_key in failed_searches and failed_searches[search_key] >= 2:
        print(f"[大交通专家] ⚠️ 已经尝试过{failed_searches[search_key]}次查询{departure_city}到{arrival_city}的火车，无法获取数据")
        
        # 提供替代方案建议
        state["is_clarification_needed"] = True
        state["clarification_question"] = f"我尝试了多次但无法找到从{departure_city}到{arrival_city}的直达高铁/火车。可能需要考虑转乘方案或其他交通方式，如飞机。您希望我为您提供哪种替代交通方式的信息？"
        state["next_step"] = "ask_user"
        state["major_transport_failure_count"] = failure_count + 1  # 增加失败计数
        
        # 清空搜索状态，允许用户提供新的选择
        if state.get("major_transport_failure_count", 0) > 5:
            print("[大交通专家] 🔄 搜索失败次数过多，执行强制退出策略")
            state["final_plan"] = {
                "title": "行程规划失败",
                "overview": f"无法找到从{departure_city}到{arrival_city}的可行交通方案",
                "error_message": "多次查询未果，请尝试不同的出发地或目的地"
            }
            state["next_step"] = "end"  # 强制结束规划流程
        
        return state
    
    # 根据用户偏好和AI分析结果智能选择查询策略
    transport_results = {}

    # 如果用户明确指定了交通方式，优先使用用户偏好
    if user_transport_preference:
        if "飞机" in user_transport_preference or "航班" in user_transport_preference:
            preferred_transport = "飞机"
            print(f"[大交通专家] 👤 用户明确要求飞机，优先查询航班")
        elif "高铁" in user_transport_preference or "火车" in user_transport_preference:
            preferred_transport = "高铁"
            print(f"[大交通专家] 👤 用户明确要求高铁/火车，优先查询火车")

    # 根据用户偏好智能选择交通方式
    print(f"[大交通专家] 🔍 DEBUG - user_transport_preference: '{user_transport_preference}'")
    print(f"[大交通专家] 🔍 DEBUG - criteria: '{criteria}'")
    print(f"[大交通专家] 🔍 DEBUG - original_query: '{state.get('original_query', '无')}'")
    print(f"[大交通专家] 🔍 DEBUG - preferred_transport (AI分析后): '{preferred_transport}'")

    if not user_transport_preference:
        print(f"[大交通专家] 🔍 用户没有明确交通偏好，根据criteria进行智能选择")
        if "最快" in criteria:
            # 判断是否为长距离（珠海到北京等）
            long_distance_routes = ["珠海-北京", "广州-北京", "深圳-北京", "上海-北京", "珠海-上海"]
            current_route = f"{departure_city}-{arrival_city}"
            print(f"[大交通专家] 🔍 DEBUG - current_route: '{current_route}'")
            print(f"[大交通专家] 🔍 DEBUG - long_distance_routes: {long_distance_routes}")

            route_match = any(route in current_route or current_route in route for route in long_distance_routes)
            print(f"[大交通专家] 🔍 DEBUG - route_match: {route_match}")

            if route_match:
                preferred_transport = "飞机"
                print(f"[大交通专家] ⚡ 用户要求最快方案且为长距离路线，强制推荐飞机")
            else:
                print(f"[大交通专家] ℹ️ 不是长距离路线，保持AI推荐: {preferred_transport}")
        elif "最慢" in criteria:
            preferred_transport = "火车"  # 普通火车比高铁慢
            print(f"[大交通专家] 🐌 用户要求最慢方案，推荐普通火车")
        elif "最便宜" in criteria:
            preferred_transport = "火车"  # 火车通常比飞机便宜
            print(f"[大交通专家] 💰 用户要求最便宜方案，推荐火车")
    else:
        print(f"[大交通专家] 👤 用户有明确交通偏好，跳过智能选择")

    # 🚨 绝对强制逻辑：根据距离和偏好选择合适的交通方式
    if not user_transport_preference:
        # 检查是否为跨省长距离路线
        is_long_distance = False
        is_short_distance = False

        # 跨省长距离路线
        if (departure_city == "珠海" and "北京" in arrival_city) or \
           (departure_city == "广州" and "北京" in arrival_city) or \
           (departure_city == "深圳" and "北京" in arrival_city) or \
           ("珠海" in departure_city and "北京" in arrival_city):
            is_long_distance = True

        # 省内短距离路线
        elif (departure_city == "珠海" and arrival_city == "汕头") or \
             (departure_city == "珠海" and arrival_city == "广州") or \
             (departure_city == "广州" and arrival_city == "深圳") or \
             (departure_city == "深圳" and arrival_city == "汕头") or \
             (departure_city == "张家界" and arrival_city == "衡阳") or \
             (departure_city == "长沙" and arrival_city == "衡阳") or \
             (departure_city == "长沙" and arrival_city == "张家界"):
            is_short_distance = True

        print(f"[大交通专家] 🔍 距离检查: {departure_city} -> {arrival_city}")
        print(f"[大交通专家] 🔍 长距离: {is_long_distance}, 短距离: {is_short_distance}")

        # 根据距离和偏好进行强制修正
        if "最快" in criteria:
            if is_long_distance and preferred_transport != "飞机":
                print(f"[大交通专家] 🚨 强制修正：用户要求最快+跨省长距离，必须选择飞机！")
                print(f"[大交通专家] 🚨 原AI选择: {preferred_transport} -> 强制修正为: 飞机")
                print(f"[大交通专家] 🚨 理由: 飞机3小时 vs 火车11小时，飞机明显更快！")
                preferred_transport = "飞机"
            elif is_short_distance and preferred_transport == "飞机":
                print(f"[大交通专家] 🚨 强制修正：省内短距离不推荐飞机，改为高铁！")
                print(f"[大交通专家] 🚨 原AI选择: {preferred_transport} -> 强制修正为: 高铁")
                print(f"[大交通专家] 🚨 理由: 省内短距离高铁更方便，无需到机场")
                preferred_transport = "高铁"
            elif is_long_distance:
                print(f"[大交通专家] ✅ 跨省长距离+最快，AI正确选择了飞机")
            elif is_short_distance:
                print(f"[大交通专家] ✅ 省内短距离+最快，AI正确选择了高铁")

        # 🚨 对于省内短距离，无论什么偏好都不推荐飞机（常识判断）
        elif is_short_distance and preferred_transport == "飞机":
            print(f"[大交通专家] 🚨 常识修正：省内短距离不推荐飞机，改为高铁！")
            print(f"[大交通专家] 🚨 原AI选择: {preferred_transport} -> 强制修正为: 高铁")
            print(f"[大交通专家] 🚨 理由: 长沙到衡阳约200公里，高铁1-2小时，飞机不实用")
            preferred_transport = "高铁"

    # 首先尝试用户偏好或AI推荐的首选交通方式
    print(f"[大交通专家] 🎯 最终选择的交通方式: '{preferred_transport}'")

    if preferred_transport == "高铁" or preferred_transport == "火车":
        state["current_tool"] = "search_train"

        try:
            print(f"[大交通专家] 🔍 正在查询 {departure_city} 到 {arrival_city} 的{preferred_transport}...")

            # 智能匹配目的地车站
            search_arrival_city = arrival_city
            if "北京北站" in state.get("arrival_place", ""):
                search_arrival_city = "北京"  # 让API自动选择最合适的北京车站
                print(f"[大交通专家] 🎯 检测到用户要去北京北站，将查询到北京的火车")

            result = search_train.invoke({
                "departure": departure_city,
                "arrival": search_arrival_city,
                "criteria": criteria,
                "date": travel_date
            })
            transport_results["train"] = result

        except Exception as e:
            print(f"[大交通专家] ❌ {preferred_transport}查询失败: {str(e)}")
            transport_results["train"] = {"status": "error", "message": str(e)}

    # 如果推荐飞机，优先查询飞机
    if preferred_transport == "飞机":
        try:
            print(f"[大交通专家] ✈️ 正在查询 {departure_city} 到 {arrival_city} 的航班...")
            # 这里应该调用航班查询工具，目前先用真实的模拟数据
            # 根据实际航线提供合理的飞行时间
            if departure_city == "珠海" and arrival_city == "北京":
                flight_duration = "3小时15分钟"
                flight_price = 1200
                departure_airport = "珠海金湾机场"
                arrival_airport = "北京首都国际机场"
            else:
                flight_duration = "2小时30分钟"
                flight_price = 800
                departure_airport = f"{departure_city}机场"
                arrival_airport = f"{arrival_city}机场"

            flight_result = {
                "status": "success",
                "data": {
                    "flight": {
                        "code": "CZ3999",
                        "type": "航班",
                        "departure": {"airport": departure_airport, "time": "08:00", "date": travel_date},
                        "arrival": {"airport": arrival_airport, "time": "11:15"},
                        "duration": flight_duration,
                        "price": flight_price,
                        "seat": {"type": "经济舱", "available": "有票", "price": flight_price}
                    },
                    "search_info": {
                        "departure_city": departure_city,
                        "arrival_city": arrival_city,
                        "date": travel_date,
                        "criteria": criteria
                    }
                }
            }
            transport_results["flight"] = flight_result

        except Exception as e:
            print(f"[大交通专家] ❌ 航班查询失败: {str(e)}")
            transport_results["flight"] = {"status": "error", "message": str(e)}

    # 如果首选方案失败，尝试备选方案
    if preferred_transport == "飞机" and transport_results.get("flight", {}).get("status") == "error":
        print(f"[大交通专家] 🔄 航班查询失败，尝试火车作为备选方案...")
        try:
            result = search_train.invoke({
                "departure": departure_city,
                "arrival": arrival_city,
                "criteria": criteria,
                "date": travel_date
            })
            transport_results["train"] = result
        except Exception as e:
            print(f"[大交通专家] ❌ 备选火车查询也失败: {str(e)}")
            transport_results["train"] = {"status": "error", "message": str(e)}

    # 使用LLM分析查询结果并选择最佳方案
    print("[大交通专家] 🤖 使用AI分析查询结果...")
    print(f"[大交通专家] 🔍 DEBUG - 传递给AI的用户偏好: '{criteria}'")

    result_analysis_prompt = f"""
    你是交通规划专家，需要分析查询结果并选择最佳方案。

    查询结果：
    {json.dumps(transport_results, ensure_ascii=False, indent=2)}

    用户偏好：{criteria}
    用户明确要求的交通方式：{user_transport_preference if user_transport_preference else '无特殊要求'}
    出行路线：{departure_city} → {arrival_city}
    出行日期：{travel_date}

    **重要规则**：
    1. 如果用户明确指定了交通方式，必须优先选择用户要求的方式（除非完全无法查询到）！
    2. 如果用户偏好是"最快"，必须选择用时最短的交通方式！
    3. 如果用户偏好是"最便宜"，必须选择价格最低的交通方式！
    4. 对比时间时，飞机3小时明显比火车11小时快，必须选择飞机！
    5. 对比价格时，火车通常比飞机便宜，如果用户要求最便宜应该选择火车！

    请分析结果并返回JSON格式的建议：
    {{
        "最佳方案": "train/flight",
        "选择理由": "详细说明选择理由",
        "方案评估": {{
            "时间效率": "评分1-10",
            "价格合理性": "评分1-10",
            "便利性": "评分1-10"
        }},
        "风险提示": "可能的问题和注意事项",
        "是否需要备选": true/false
    }}
    """

    try:
        result_messages = [
            SystemMessage(content="你是专业的交通方案评估AI，擅长分析查询结果并提供最优建议。"),
            HumanMessage(content=result_analysis_prompt)
        ]

        response = llm.invoke(result_messages)
        response_content = response.content.strip()

        # 解析LLM响应
        if response_content.startswith('```json'):
            response_content = response_content[7:-3]
        elif response_content.startswith('```'):
            response_content = response_content[3:-3]

        json_start = response_content.find('{')
        json_end = response_content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            response_content = response_content[json_start:json_end]

        selection_result = json.loads(response_content)

        # 根据AI建议选择最佳方案
        best_option = selection_result.get("最佳方案", "train")

        # 强制逻辑检查：确保选择符合用户偏好
        if "最快" in criteria and transport_results.get("flight", {}).get("status") == "success":
            if transport_results.get("train", {}).get("status") == "success":
                # 比较时间
                flight_duration = transport_results["flight"]["data"]["flight"]["duration"]
                train_duration = transport_results["train"]["data"]["train"]["duration"]
                print(f"[大交通专家] ⚡ 时间对比 - 飞机: {flight_duration}, 火车: {train_duration}")

                # 强制选择飞机（因为用户要求最快）
                best_option = "flight"
                print(f"[大交通专家] 🚀 用户要求最快，强制选择飞机")
        elif "最慢" in criteria and transport_results.get("train", {}).get("status") == "success":
            # 用户要求最慢，选择火车
            best_option = "train"
            print(f"[大交通专家] 🐌 用户要求最慢，选择火车")
        elif "最便宜" in criteria and transport_results.get("train", {}).get("status") == "success":
            # 用户要求最便宜，选择火车
            best_option = "train"
            print(f"[大交通专家] 💰 用户要求最便宜，选择火车")

        result = transport_results.get(best_option, transport_results.get("train", {}))

        # 记录AI选择过程
        state["thinking_process"].append(f"🤖 AI评估：{selection_result.get('选择理由', '未知理由')}")
        state["thinking_process"].append(f"🏆 最佳方案：{best_option}")

        print(f"[大交通专家] 🏆 最终选择：{best_option} - {selection_result.get('选择理由', '未知理由')}")

    except Exception as e:
        print(f"[大交通专家] ⚠️ AI分析失败，使用默认选择: {str(e)}")
        # 回退到简单选择逻辑
        if transport_results.get("train", {}).get("status") == "success":
            result = transport_results["train"]
        elif transport_results.get("flight", {}).get("status") == "success":
            result = transport_results["flight"]
        else:
            result = {"status": "error", "message": "所有交通方式查询都失败"}
        
    # 记录工具调用结果
    if "tool_results" not in state:
        state["tool_results"] = {}
    state["tool_results"]["major_transport"] = transport_results

    # 处理查询结果
    if result["status"] == "success":
        transport_data = result["data"]

        # 判断是火车还是航班
        if "train" in transport_data:
            transport_type = "train"
            transport_info = transport_data["train"]
            print(f"[大交通专家] ✅ 已找到高铁/火车方案: {transport_info['code']}")

            # 提取出发站和到达站信息
            state["departure_station"] = {
                "name": transport_info["departure"]["station"],
                "city": departure_city,
                "type": "station"
            }
            state["arrival_station"] = {
                "name": transport_info["arrival"]["station"],
                "city": arrival_city,
                "type": "station"
            }

            thinking = (f"已找到从{departure_city}到{arrival_city}的最佳高铁/火车方案：" +
                       f"{transport_info['code']}，" +
                       f"从{transport_info['departure']['station']}出发，" +
                       f"到达{transport_info['arrival']['station']}，" +
                       f"历时{transport_info['duration']}，" +
                       f"票价{transport_info['seat']['price']}元")

        elif "flight" in transport_data:
            transport_type = "flight"
            transport_info = transport_data["flight"]
            print(f"[大交通专家] ✅ 已找到航班方案: {transport_info['code']}")

            # 提取出发机场和到达机场信息
            state["departure_station"] = {
                "name": transport_info["departure"]["airport"],
                "city": departure_city,
                "type": "airport"
            }
            state["arrival_station"] = {
                "name": transport_info["arrival"]["airport"],
                "city": arrival_city,
                "type": "airport"
            }

            thinking = (f"已找到从{departure_city}到{arrival_city}的最佳航班方案：" +
                       f"{transport_info['code']}，" +
                       f"从{transport_info['departure']['airport']}起飞，" +
                       f"到达{transport_info['arrival']['airport']}，" +
                       f"飞行时间{transport_info['duration']}，" +
                       f"票价{transport_info.get('price', '未知')}元")

        # 将查询结果保存到状态
        state["major_transport_options"] = [transport_data]
        state["selected_major_transport"] = transport_data

        # 记录思考过程
        if "thinking_process" not in state:
            state["thinking_process"] = []
        state["thinking_process"].append(thinking)

        print(f"[大交通专家] ✅ 大交通规划完成，返回规划师节点")
        state["next_step"] = "planner"

    else:
            # 查询失败
            error_msg = result["message"]
            print(f"[大交通专家] ❌ 查询高铁/火车失败: {error_msg}")
            
            # 记录错误
            error_detail = {
                "tool": "search_train",
                "message": error_msg,
                "timestamp": datetime.now().isoformat()
            }
            if "errors" not in state:
                state["errors"] = []
            state["errors"].append(error_detail)
            
            # 更新失败搜索记录
            search_key = f"{departure_city}_{arrival_city}_{travel_date}"
            if "failed_searches" not in state:
                state["failed_searches"] = {}
            
            if search_key in state["failed_searches"]:
                state["failed_searches"][search_key] += 1
            else:
                state["failed_searches"][search_key] = 1
            
            print(f"[大交通专家] ℹ️ 查询 {search_key} 已失败 {state['failed_searches'][search_key]} 次")

            # 如果火车查询失败，尝试机票查询
            if not state.get("flight_search_attempted"):
                print(f"[大交通专家] ✈️ 火车查询失败，尝试机票查询作为备选方案...")
                state["flight_search_attempted"] = True

                try:
                    # 调用机票查询工具
                    flight_result = search_flight.invoke({
                        "departure": departure_city,
                        "arrival": arrival_city,
                        "criteria": criteria,  # 使用用户的真实偏好
                        "date": travel_date
                    })

                    # 记录工具调用结果
                    if "tool_results" not in state:
                        state["tool_results"] = {}
                    state["tool_results"]["search_flight"] = flight_result

                    # 处理机票查询结果
                    if flight_result["status"] == "success":
                        flight_data = flight_result["data"]
                        print(f"[大交通专家] ✅ 已找到航班方案: {flight_data['flight']['code']}")

                        # 将查询结果保存到状态
                        state["major_transport_options"] = [flight_data]
                        # 默认选择第一个（最优）选项
                        state["selected_major_transport"] = flight_data

                        # 提取出发机场和到达机场信息
                        state["departure_station"] = {
                            "name": flight_data["flight"]["departure"]["airport"],
                            "city": departure_city,
                            "type": "airport"
                        }
                        state["arrival_station"] = {
                            "name": flight_data["flight"]["arrival"]["airport"],
                            "city": arrival_city,
                            "type": "airport"
                        }

                        # 记录思考过程
                        if "thinking_process" not in state:
                            state["thinking_process"] = []

                        thinking = f"由于未找到合适的高铁/火车方案，已为您查找到航班替代方案：{flight_data['flight']['code']}次航班，{flight_data['flight']['departure']['time']}从{flight_data['flight']['departure']['airport']}起飞，{flight_data['flight']['arrival']['time']}到达{flight_data['flight']['arrival']['airport']}，飞行时长{flight_data['flight']['duration']}，票价¥{flight_data['flight']['seat']['price']}。"
                        state["thinking_process"].append(thinking)

                        print("[大交通专家] ✅ 大交通规划完成（使用航班方案），返回规划师节点")
                        state["next_step"] = "planner"
                        return state
                    else:
                        print(f"[大交通专家] ❌ 机票查询也失败了: {flight_result.get('message', '未知错误')}")

                except Exception as e:
                    print(f"[大交通专家] ❌ 机票查询时发生错误: {str(e)}")

    # 如果所有查询都失败了，向用户澄清
    if not transport_results or all(r.get("status") == "error" for r in transport_results.values()):
        state["is_clarification_needed"] = True
        state["clarification_question"] = f"很抱歉，我无法找到从{departure_city}到{arrival_city}的直达高铁/火车或航班。您是否可以提供备选的交通方式或城市？"
        state["next_step"] = "ask_user"
        return state

    # 返回规划师节点
    state["next_step"] = "planner"
    return state



# 后续需要实现的其他专家节点
def local_transport_node(state: ItineraryState) -> ItineraryState:
    """
    本地交通专家节点：负责规划目的地内的交通方案。
    处理从出发地点到火车站、以及从火车站到目的地的交通。
    """
    print("\n[小交通专家] 🚗 正在规划本地交通方案...")

    # 追踪执行记录
    state["execution_history"].append("local_transport")
    state["current_step"] = "local_transport"

    # 确保state中有next_step字段
    if "next_step" not in state:
        state["next_step"] = "planner"

    # 获取用户偏好标准
    user_criteria = state.get("criteria", "综合最优")
    print(f"[小交通专家] 🎯 用户偏好标准: {user_criteria}")
    print(f"[小交通专家] 🔍 DEBUG - criteria字段: {state.get('criteria')}")
    print(f"[小交通专家] 🔍 DEBUG - structured_request偏好: {state.get('structured_request', {}).get('偏好标准')}")
    
    # 检查必要参数
    departure_coordinates = state.get("departure_coordinates", {})
    arrival_coordinates = state.get("arrival_coordinates", {})
    departure_city = state.get("departure_city")
    arrival_city = state.get("arrival_city")
    departure_place = state.get("departure_place", departure_city or "出发地")
    arrival_place = state.get("arrival_place", arrival_city or "目的地")
    
    # 检查坐标是否有效
    has_valid_coordinates = (
        departure_coordinates and 
        arrival_coordinates and 
        is_valid_coordinates(departure_coordinates.get("lng"), departure_coordinates.get("lat")) and
        is_valid_coordinates(arrival_coordinates.get("lng"), arrival_coordinates.get("lat"))
    )
    
    if not has_valid_coordinates:
        print("[小交通专家] ⚠️ 缺少有效的坐标信息，无法规划详细路线")
        # 记录错误
        error_detail = {
            "tool": "local_transport",
            "message": "缺少有效的出发地或目的地坐标",
            "timestamp": datetime.now().isoformat()
        }
        if "errors" not in state:
            state["errors"] = []
        state["errors"].append(error_detail)
        
        # 向用户请求澄清
        state["is_clarification_needed"] = True
        state["clarification_question"] = "请提供完整的出发地和目的地信息，包括准确的地址，以便我规划详细的交通路线。"
        state["next_step"] = "ask_user"
        return state
    
    # 检查是否有车站/机场信息，如果没有则尝试获取
    if not state.get("departure_station") or not state.get("departure_station").get("location"):
        if departure_city:
            # 检查是否是机场类型
            departure_station = state.get("departure_station", {})
            if departure_station.get("type") == "airport":
                print(f"[小交通专家] ✈️ 正在获取{departure_city}的机场信息...")
                station_info = get_airport_info(departure_city)
            else:
                print(f"[小交通专家] 🔍 正在获取{departure_city}的车站信息...")
                station_info = get_railway_station(departure_city)
            if station_info["status"] == "success":
                # 更新出发站信息
                departure_station = state.get("departure_station", {})
                if not departure_station:
                    departure_station = {"city": departure_city}
                
                departure_station["name"] = station_info["name"]
                departure_station["type"] = station_info["type"]
                
                # 提取经纬度
                if "location" in station_info:
                    location = station_info["location"]
                    if "," in location:
                        lng, lat = location.split(",")
                        departure_station["location"] = {"lng": lng, "lat": lat}
                
                state["departure_station"] = departure_station
                print(f"[小交通专家] ✅ 已获取到{departure_city}的车站: {station_info['name']}")
            else:
                print(f"[小交通专家] ⚠️ 无法获取{departure_city}的车站信息: {station_info['message']}")
                # 使用简化的车站坐标（城市中心点偏移）
                if not state.get("departure_station", {}).get("location"):
                    print(f"[小交通专家] ℹ️ 使用模拟坐标作为{departure_city}车站位置")
                    departure_station = state.get("departure_station", {})
                    if not departure_station:
                        departure_station = {"city": departure_city, "name": f"{departure_city}火车站"}
                    
                    # 使用出发地坐标稍作偏移作为车站坐标（仅用于演示）
                    dep_lng = float(departure_coordinates["lng"]) + 0.01
                    dep_lat = float(departure_coordinates["lat"]) + 0.01
                    departure_station["location"] = {"lng": str(dep_lng), "lat": str(dep_lat)}
                    state["departure_station"] = departure_station
    
    # 同样处理到达站信息
    if not state.get("arrival_station") or not state.get("arrival_station").get("location"):
        if arrival_city:
            # 检查是否是机场类型
            arrival_station = state.get("arrival_station", {})
            if arrival_station.get("type") == "airport":
                print(f"[小交通专家] ✈️ 正在获取{arrival_city}的机场信息...")
                station_info = get_airport_info(arrival_city)
            else:
                print(f"[小交通专家] 🔍 正在获取{arrival_city}的车站信息...")
                station_info = get_railway_station(arrival_city)
            if station_info["status"] == "success":
                # 更新到达站信息
                arrival_station = state.get("arrival_station", {})
                if not arrival_station:
                    arrival_station = {"city": arrival_city}
                
                arrival_station["name"] = station_info["name"]
                arrival_station["type"] = station_info["type"]
                
                # 提取经纬度
                if "location" in station_info:
                    location = station_info["location"]
                    if "," in location:
                        lng, lat = location.split(",")
                        arrival_station["location"] = {"lng": lng, "lat": lat}
                
                state["arrival_station"] = arrival_station
                print(f"[小交通专家] ✅ 已获取到{arrival_city}的车站: {station_info['name']}")
            else:
                print(f"[小交通专家] ⚠️ 无法获取{arrival_city}的车站信息: {station_info['message']}")
                # 使用简化的车站坐标（城市中心点偏移）
                if not state.get("arrival_station", {}).get("location"):
                    print(f"[小交通专家] ℹ️ 使用模拟坐标作为{arrival_city}车站位置")
                    arrival_station = state.get("arrival_station", {})
                    if not arrival_station:
                        arrival_station = {"city": arrival_city, "name": f"{arrival_city}火车站"}
                    
                    # 使用到达地坐标稍作偏移作为车站坐标（仅用于演示）
                    arr_lng = float(arrival_coordinates["lng"]) - 0.01
                    arr_lat = float(arrival_coordinates["lat"]) - 0.01
                    arrival_station["location"] = {"lng": str(arr_lng), "lat": str(arr_lat)}
                    state["arrival_station"] = arrival_station
    
    # 获取出发站和到达站的坐标
    departure_station = state.get("departure_station", {})
    arrival_station = state.get("arrival_station", {})
    
    dep_station_location = departure_station.get("location", {})
    arr_station_location = arrival_station.get("location", {})
    
    # 初始化本地交通方案
    local_transport = {"departure": {}, "arrival": {}}
    
    # 规划出发地到出发站的路线（打车 vs 公交）
    if dep_station_location and is_valid_coordinates(dep_station_location.get("lng"), dep_station_location.get("lat")):
        print("[小交通专家] 🚗🚌 正在规划从出发地到车站/机场的最优路线...")

        taxi_route = None
        transit_route = None

        # 1. 查询打车路线
        try:
            print("[小交通专家] 🚗 查询打车路线...")
            taxi_route = get_taxi_route(
                departure_coordinates["lng"],
                departure_coordinates["lat"],
                dep_station_location["lng"],
                dep_station_location["lat"]
            )
            if taxi_route["status"] == "success":
                print(f"[小交通专家] ✅ 打车路线: {taxi_route['duration']}, 费用¥{taxi_route.get('taxi_cost', 0)}")
        except Exception as e:
            print(f"[小交通专家] ❌ 查询打车路线失败: {str(e)}")

        # 2. 查询公交路线
        try:
            print("[小交通专家] 🚌 查询公交路线...")
            transit_route = get_transit_route(
                departure_coordinates["lng"],
                departure_coordinates["lat"],
                dep_station_location["lng"],
                dep_station_location["lat"],
                departure_city
            )
            if transit_route["status"] == "success":
                cost_text = transit_route.get('cost_text', f"¥{transit_route.get('cost', 0)}")
                if transit_route.get('cost', 0) == 0:
                    cost_text = "具体费用请联系运营商"
                print(f"[小交通专家] ✅ 公交路线: {transit_route['duration']}, 费用{cost_text}")
        except Exception as e:
            print(f"[小交通专家] ❌ 查询公交路线失败: {str(e)}")

        # 3. 根据用户偏好选择最优方案
        best_route = None
        best_mode = ""

        if taxi_route and taxi_route["status"] == "success" and transit_route and transit_route["status"] == "success":
            # 两种方案都成功，根据用户偏好选择
            taxi_time = taxi_route.get("raw_duration_seconds", float('inf'))
            transit_time = transit_route.get("raw_duration_seconds", float('inf'))

            # 确保成本是数字类型
            try:
                taxi_cost = float(taxi_route.get("taxi_cost", 0))
            except (ValueError, TypeError):
                taxi_cost = 0

            try:
                transit_cost = float(transit_route.get("cost", 0))
            except (ValueError, TypeError):
                transit_cost = 0

            # 根据用户偏好选择
            if "最便宜" in user_criteria or "价格" in user_criteria:
                if transit_cost <= taxi_cost:
                    best_route = transit_route
                    best_mode = "公交"
                    transit_cost_text = "具体费用请联系运营商" if transit_cost == 0 else f"¥{transit_cost}"
                    taxi_cost_text = f"¥{taxi_cost}"
                    print(f"[小交通专家] 💰 选择公交方案（价格更低：{transit_cost_text} vs {taxi_cost_text}）")
                else:
                    best_route = taxi_route
                    best_mode = "打车"
                    transit_cost_text = "具体费用请联系运营商" if transit_cost == 0 else f"¥{transit_cost}"
                    taxi_cost_text = f"¥{taxi_cost}"
                    print(f"[小交通专家] 💰 选择打车方案（价格更低：{taxi_cost_text} vs {transit_cost_text}）")
            elif "最快" in user_criteria or "时间" in user_criteria:
                if taxi_time <= transit_time:
                    best_route = taxi_route
                    best_mode = "打车"
                    print(f"[小交通专家] ⚡ 选择打车方案（时间更短）")
                else:
                    best_route = transit_route
                    best_mode = "公交"
                    print(f"[小交通专家] ⚡ 选择公交方案（时间更短）")
            else:  # 综合最优
                # 综合考虑时间和价格，价格权重更高
                taxi_score = (1 - min(taxi_time / 3600, 1)) * 0.3 + (1 - min(taxi_cost / 100, 1)) * 0.7
                transit_score = (1 - min(transit_time / 3600, 1)) * 0.3 + (1 - min(transit_cost / 100, 1)) * 0.7

                if taxi_score >= transit_score:
                    best_route = taxi_route
                    best_mode = "打车"
                    print(f"[小交通专家] 🎯 选择打车方案（综合评分更高）")
                else:
                    best_route = transit_route
                    best_mode = "公交"
                    print(f"[小交通专家] 🎯 选择公交方案（综合评分更高）")

        elif taxi_route and taxi_route["status"] == "success":
            best_route = taxi_route
            best_mode = "打车"
            print(f"[小交通专家] 🚗 使用打车方案（公交查询失败）")

        elif transit_route and transit_route["status"] == "success":
            best_route = transit_route
            best_mode = "公交"
            print(f"[小交通专家] 🚌 使用公交方案（打车查询失败）")

        if best_route:
            # 添加交通方式标识
            best_route["transport_mode"] = best_mode
            local_transport["departure"] = best_route
        else:
            print(f"[小交通专家] ❌ 所有交通方式查询都失败")
            local_transport["departure"] = {
                "status": "error",
                "message": "无法获取到车站/机场的交通路线",
                "distance": 0,
                "duration": "未知",
                "transport_mode": "未知"
            }
    
    # 规划到达站到目的地的路线（打车 vs 公交）
    if arr_station_location and is_valid_coordinates(arr_station_location.get("lng"), arr_station_location.get("lat")):
        print("[小交通专家] 🚗🚌 正在规划从车站/机场到目的地的最优路线...")

        taxi_route = None
        transit_route = None

        # 1. 查询打车路线
        try:
            print("[小交通专家] 🚗 查询打车路线...")
            taxi_route = get_taxi_route(
                arr_station_location["lng"],
                arr_station_location["lat"],
                arrival_coordinates["lng"],
                arrival_coordinates["lat"]
            )
            if taxi_route["status"] == "success":
                print(f"[小交通专家] ✅ 打车路线: {taxi_route['duration']}, 费用¥{taxi_route.get('taxi_cost', 0)}")
        except Exception as e:
            print(f"[小交通专家] ❌ 查询打车路线失败: {str(e)}")

        # 2. 查询公交路线
        try:
            print("[小交通专家] 🚌 查询公交路线...")
            transit_route = get_transit_route(
                arr_station_location["lng"],
                arr_station_location["lat"],
                arrival_coordinates["lng"],
                arrival_coordinates["lat"],
                arrival_city
            )
            if transit_route["status"] == "success":
                cost_text = transit_route.get('cost_text', f"¥{transit_route.get('cost', 0)}")
                if transit_route.get('cost', 0) == 0:
                    cost_text = "具体费用请联系运营商"
                print(f"[小交通专家] ✅ 公交路线: {transit_route['duration']}, 费用{cost_text}")
        except Exception as e:
            print(f"[小交通专家] ❌ 查询公交路线失败: {str(e)}")

        # 3. 选择最优方案（时间最短）
        best_route = None
        best_mode = ""

        if taxi_route and taxi_route["status"] == "success" and transit_route and transit_route["status"] == "success":
            # 两种方案都成功，根据用户偏好选择
            taxi_time = taxi_route.get("raw_duration_seconds", float('inf'))
            transit_time = transit_route.get("raw_duration_seconds", float('inf'))

            # 确保成本是数字类型
            try:
                taxi_cost = float(taxi_route.get("taxi_cost", 0))
            except (ValueError, TypeError):
                taxi_cost = 0

            try:
                transit_cost = float(transit_route.get("cost", 0))
            except (ValueError, TypeError):
                transit_cost = 0

            # 根据用户偏好选择
            if "最便宜" in user_criteria or "价格" in user_criteria:
                if transit_cost <= taxi_cost:
                    best_route = transit_route
                    best_mode = "公交"
                    transit_cost_text = "具体费用请联系运营商" if transit_cost == 0 else f"¥{transit_cost}"
                    taxi_cost_text = f"¥{taxi_cost}"
                    print(f"[小交通专家] 💰 选择公交方案（价格更低：{transit_cost_text} vs {taxi_cost_text}）")
                else:
                    best_route = taxi_route
                    best_mode = "打车"
                    transit_cost_text = "具体费用请联系运营商" if transit_cost == 0 else f"¥{transit_cost}"
                    taxi_cost_text = f"¥{taxi_cost}"
                    print(f"[小交通专家] 💰 选择打车方案（价格更低：{taxi_cost_text} vs {transit_cost_text}）")
            elif "最快" in user_criteria or "时间" in user_criteria:
                if taxi_time <= transit_time:
                    best_route = taxi_route
                    best_mode = "打车"
                    print(f"[小交通专家] ⚡ 选择打车方案（时间更短）")
                else:
                    best_route = transit_route
                    best_mode = "公交"
                    print(f"[小交通专家] ⚡ 选择公交方案（时间更短）")
            else:  # 综合最优
                # 综合考虑时间和价格，价格权重更高
                taxi_score = (1 - min(taxi_time / 3600, 1)) * 0.3 + (1 - min(taxi_cost / 100, 1)) * 0.7
                transit_score = (1 - min(transit_time / 3600, 1)) * 0.3 + (1 - min(transit_cost / 100, 1)) * 0.7

                if taxi_score >= transit_score:
                    best_route = taxi_route
                    best_mode = "打车"
                    print(f"[小交通专家] 🎯 选择打车方案（综合评分更高）")
                else:
                    best_route = transit_route
                    best_mode = "公交"
                    print(f"[小交通专家] 🎯 选择公交方案（综合评分更高）")

        elif taxi_route and taxi_route["status"] == "success":
            best_route = taxi_route
            best_mode = "打车"
            print(f"[小交通专家] 🚗 使用打车方案（公交查询失败）")

        elif transit_route and transit_route["status"] == "success":
            best_route = transit_route
            best_mode = "公交"
            print(f"[小交通专家] 🚌 使用公交方案（打车查询失败）")

        if best_route:
            # 添加交通方式标识
            best_route["transport_mode"] = best_mode
            local_transport["arrival"] = best_route
        else:
            print(f"[小交通专家] ❌ 所有交通方式查询都失败")
            local_transport["arrival"] = {
                "status": "error",
                "message": "无法获取从车站/机场到目的地的交通路线",
                "distance": 0,
                "duration": "未知",
                "transport_mode": "未知"
            }
    
    # 保存本地交通方案到状态
    state["local_transport_options"] = local_transport
    
    # 添加地图数据
    map_data = {
        "dep": {
            "name": departure_station.get("name", "出发站"),
            "coords": [float(dep_station_location.get("lat", "35")), float(dep_station_location.get("lng", "105"))]
        },
        "arr": {
            "name": arrival_station.get("name", "到达站"),
            "coords": [float(arr_station_location.get("lat", "35")), float(arr_station_location.get("lng", "105"))]
        }
    }
    
    # 添加精确的出发地和目的地点（如果有）
    if is_valid_coordinates(departure_coordinates.get("lng"), departure_coordinates.get("lat")):
        map_data["dep_point"] = {
            "name": departure_place,
            "coords": [float(departure_coordinates.get("lat")), float(departure_coordinates.get("lng"))]
        }
    
    if is_valid_coordinates(arrival_coordinates.get("lng"), arrival_coordinates.get("lat")):
        map_data["arr_point"] = {
            "name": arrival_place,
            "coords": [float(arrival_coordinates.get("lat")), float(arrival_coordinates.get("lng"))]
        }
    
    # 添加车站信息
    map_data["dep_station"] = {
        "name": departure_station.get("name", "出发站"),
        "coords": [float(dep_station_location.get("lat", "35")), float(dep_station_location.get("lng", "105"))]
    }
    
    map_data["arr_station"] = {
        "name": arrival_station.get("name", "到达站"),
        "coords": [float(arr_station_location.get("lat", "35")), float(arr_station_location.get("lng", "105"))]
    }
    
    # 更新状态中的地图数据
    state["map_data"] = map_data
    
    # 记录思考过程
    thinking = (
        f"已规划从出发地({departure_coordinates.get('lng', '?')},{departure_coordinates.get('lat', '?')})到" +
        f"{departure_station.get('name', '火车站')}的驾车路线，距离{local_transport['departure'].get('distance', '未知')}，" +
        f"时间{local_transport['departure'].get('duration', '未知')}。\n" +
        f"已规划从{arrival_station.get('name', '火车站')}到目的地" +
        f"({arrival_coordinates.get('lng', '?')},{arrival_coordinates.get('lat', '?')})的驾车路线，" +
        f"距离{local_transport['arrival'].get('distance', '未知')}，时间{local_transport['arrival'].get('duration', '未知')}。"
    )
    
    if "thinking_process" not in state:
        state["thinking_process"] = []
    state["thinking_process"].append(thinking)
    
    print("[小交通专家] ✅ 本地交通规划完成，返回规划师节点")
    state["next_step"] = "planner"
    return state


def web_search_node(state: ItineraryState) -> ItineraryState:
    """
    网络搜索专家节点：负责获取城市、景点、天气等额外信息。
    作为备用和增强信息的来源。
    """
    print("\n[网络搜索专家] 🔍 正在搜索相关信息...")
    
    # 追踪执行记录
    state["execution_history"].append("web_search")
    state["current_step"] = "web_search"
    
    # 确保state中有next_step字段
    if "next_step" not in state:
        state["next_step"] = "planner"
    
    # 导入天气查询工具
    from src.tools.search_tool import get_weather
    
    # 检查要查询的城市
    departure_city = state.get("departure_city")
    arrival_city = state.get("arrival_city")
    
    if not departure_city and not arrival_city:
        print("[网络搜索专家] ⚠️ 缺少城市信息，无法进行网络搜索")
        state["next_step"] = "planner"
        return state
    
    # 初始化附加信息字典
    if "additional_info" not in state:
        state["additional_info"] = {
            "weather": {},
            "attractions": {},
            "transportation_tips": []
        }
    
    try:
        # 调用天气查询工具
        print(f"[网络搜索] 🔍 正在查询 {departure_city} 的天气信息...")
        departure_weather_result = get_weather.invoke({"city": departure_city})
        
        # 处理查询结果
        if departure_weather_result["status"] == "success":
            departure_weather = departure_weather_result["data"]
            print(f"[网络搜索] ✅ 已获取 {departure_city} 的天气: {departure_weather['condition']}, {departure_weather['temperature']}")
        else:
            error_msg = departure_weather_result["message"]
            print(f"[网络搜索] ⚠️ 获取 {departure_city} 天气失败: {error_msg}")
            departure_weather = {"city": departure_city, "condition": "未知", "temperature": "未知", "note": error_msg}
        
        # 查询目的地天气
        print(f"[网络搜索] 🔍 正在查询 {arrival_city} 的天气信息...")
        arrival_weather_result = get_weather.invoke({"city": arrival_city})
        
        # 处理查询结果
        if arrival_weather_result["status"] == "success":
            arrival_weather = arrival_weather_result["data"]
            print(f"[网络搜索] ✅ 已获取 {arrival_city} 的天气: {arrival_weather['condition']}, {arrival_weather['temperature']}")
        else:
            error_msg = arrival_weather_result["message"]
            print(f"[网络搜索] ⚠️ 获取 {arrival_city} 天气失败: {error_msg}")
            arrival_weather = {"city": arrival_city, "condition": "未知", "temperature": "未知", "note": error_msg}
    
    except Exception as e:
        print(f"[网络搜索专家] ❌ 调用天气查询工具时发生错误: {str(e)}")
        print(traceback.format_exc())
        # 使用默认天气信息
        departure_weather = {"city": departure_city, "condition": "未知", "temperature": "未知", "note": "天气查询失败"}
        arrival_weather = {"city": arrival_city, "condition": "未知", "temperature": "未知", "note": "天气查询失败"}
    
    # 保存天气信息到状态
    if "additional_info" not in state:
        state["additional_info"] = {}
    if "weather" not in state["additional_info"]:
        state["additional_info"]["weather"] = {}
    
    state["additional_info"]["weather"][departure_city] = departure_weather
    state["additional_info"]["weather"][arrival_city] = arrival_weather
    
    # 记录思考过程
    if "thinking_process" not in state:
        state["thinking_process"] = []
    
    departure_weather_thought = (f"{departure_city}天气：{departure_weather['condition']}，" +
                               f"温度{departure_weather['temperature']}，" +
                               f"适合行程规划。")
    
    arrival_weather_thought = (f"{arrival_city}天气：{arrival_weather['condition']}，" +
                             f"温度{arrival_weather['temperature']}，" +
                             f"适合行程规划。")
    
    state["thinking_process"].append(departure_weather_thought)
    state["thinking_process"].append(arrival_weather_thought)
    
    # 生成目的地交通提示
    if arrival_city and not state["additional_info"]["transportation_tips"]:
        try:
            print(f"[网络搜索专家] 🚕 正在生成{arrival_city}的交通提示...")
            
            system_prompt = f"""
            你是一个旅游专家，对{arrival_city}非常熟悉。
            请提供3-5条在{arrival_city}出行的实用交通提示，包括公共交通、打车、共享单车等方面的建议。
            以JSON数组格式返回，仅返回JSON数组，不要有其他文本。
            每条提示应该简洁实用，不超过50个字。
            """
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"请提供{arrival_city}的交通提示")
            ]
            
            response = llm.invoke(messages)
            tips = json.loads(response.content)
            
            print(f"[网络搜索专家] ✅ 生成了{len(tips)}条{arrival_city}交通提示")
            state["additional_info"]["transportation_tips"] = tips
        except Exception as e:
            print(f"[网络搜索专家] ❌ 生成{arrival_city}交通提示时出错: {str(e)}")
            # 使用默认提示
            state["additional_info"]["transportation_tips"] = [
                f"{arrival_city}公共交通系统发达，建议使用公交卡或移动支付",
                "打车时建议使用滴滴等APP，避免被外地游客溢价",
                "高峰时段地铁是避开交通拥堵的最佳选择"
            ]
    
    # 添加搜索结果到最终规划建议
    if "final_plan" in state:
        # 如果已经有最终规划，添加额外信息
        final_plan = state["final_plan"]
        
        # 添加天气提示
        weather_tips = []
        for city, weather in state["additional_info"]["weather"].items():
            weather_tips.append(f"{city}天气: {weather['condition']}，温度{weather['temperature']}，适当准备衣物")
        
        # 添加交通提示
        transportation_tips = state["additional_info"]["transportation_tips"]
        
        # 合并所有提示
        if "tips" not in final_plan:
            final_plan["tips"] = []
        
        final_plan["tips"].extend(weather_tips)
        final_plan["tips"].extend(transportation_tips)
        
        # 更新最终规划
        state["final_plan"] = final_plan
    
    print("[网络搜索专家] ✅ 信息搜索完成，返回规划师节点")
    state["next_step"] = "planner"
    return state


def synthesis_node(state: ItineraryState) -> ItineraryState:
    """
    整合报告智能体：负责将所有信息整合成最终的行程规划。
    生成完整的行程报告，包括时间表、地图和建议。
    """
    print("\n[整合报告专家] 📊 正在生成最终行程规划...")
    
    # 追踪执行记录
    state["execution_history"].append("synthesis")
    state["current_step"] = "synthesis"
    
    # 确保state中有next_step字段
    if "next_step" not in state:
        state["next_step"] = "end"
    
    # 检查是否有必要的信息
    major_transport = state.get("selected_major_transport")
    local_transport = state.get("local_transport_options", {})
    
    if not major_transport:
        print("[整合报告专家] ⚠️ 缺少大交通信息，无法生成完整的行程规划")
        state["is_clarification_needed"] = True
        state["clarification_question"] = "请先提供高铁/火车的出行信息，以便我为您生成完整的行程规划。"
        state["next_step"] = "ask_user"
        return state
    
    # 提取关键信息
    departure_city = state.get("departure_city", "出发城市")
    arrival_city = state.get("arrival_city", "目的城市")
    departure_place = state.get("departure_place", departure_city)
    arrival_place = state.get("arrival_place", arrival_city)
    travel_date = state.get("travel_date", "未指定日期")
    
    # 从大交通中提取交通信息（火车或航班）
    train = major_transport.get("train")
    flight = major_transport.get("flight")

    # 判断是火车还是航班
    is_flight = flight is not None
    transport_code = ""
    transport_type = ""

    if is_flight:
        transport_code = flight.get("code", "未知航班")
        transport_type = "flight"
    else:
        transport_code = train.get("code", "未知车次") if train else "未知车次"
        transport_type = "train"
    # 根据交通类型提取时间和站点信息
    if is_flight:
        transport_departure_time = flight.get("departure", {}).get("time", "00:00")
        transport_arrival_time = flight.get("arrival", {}).get("time", "00:00")
        transport_duration = flight.get("duration", "未知")
        transport_dep_station = flight.get("departure", {}).get("airport", "出发机场")
        transport_arr_station = flight.get("arrival", {}).get("airport", "到达机场")
    else:
        transport_departure_time = train.get("departure", {}).get("time", "00:00") if train else "00:00"
        transport_arrival_time = train.get("arrival", {}).get("time", "00:00") if train else "00:00"
        transport_duration = train.get("duration", "未知") if train else "未知"
        transport_dep_station = train.get("departure", {}).get("station", "出发站") if train else "出发站"
        transport_arr_station = train.get("arrival", {}).get("station", "到达站") if train else "到达站"
    
    # 从小交通中提取驾车路线信息
    dep_driving = local_transport.get("departure", {})
    arr_driving = local_transport.get("arrival", {})
    
    # 计算时间轴
    from datetime import datetime, timedelta
    from src.tools.gaode_api_tool import parse_duration_to_timedelta
    
    timeline = {}
    
    try:
        # 解析交通工具出发和到达时间
        transport_departure_dt = None
        transport_arrival_dt = None

        if travel_date and transport_departure_time:
            # 将日期和时间组合
            date_str = travel_date
            departure_time_str = transport_departure_time

            # 解析为datetime对象
            transport_departure_dt = datetime.strptime(f"{date_str} {departure_time_str}", "%Y-%m-%d %H:%M")

            # 如果有到达时间，计算到达datetime
            if transport_arrival_time:
                arrival_hours, arrival_minutes = map(int, transport_arrival_time.split(':'))
                departure_hours, departure_minutes = map(int, transport_departure_time.split(':'))

                # 创建基本的到达时间（与出发日期相同）
                transport_arrival_dt = transport_departure_dt.replace(hour=arrival_hours, minute=arrival_minutes)

                # 如果到达时间早于出发时间，说明是第二天到达
                if arrival_hours < departure_hours or (arrival_hours == departure_hours and arrival_minutes < departure_minutes):
                    transport_arrival_dt += timedelta(days=1)
        
        # 计算驾车时间
        dep_drive_duration = None
        arr_drive_duration = None
        
        # 从出发地到车站/机场的驾车时间
        if dep_driving.get("status") == "success" and "raw_duration_seconds" in dep_driving:
            dep_drive_duration = timedelta(seconds=dep_driving["raw_duration_seconds"])
        elif dep_driving.get("duration"):
            # 尝试从格式化的时间字符串解析
            dep_drive_duration = parse_duration_to_timedelta(dep_driving["duration"])

        # 从车站/机场到目的地的驾车时间
        if arr_driving.get("status") == "success" and "raw_duration_seconds" in arr_driving:
            arr_drive_duration = timedelta(seconds=arr_driving["raw_duration_seconds"])
        elif arr_driving.get("duration"):
            # 尝试从格式化的时间字符串解析
            arr_drive_duration = parse_duration_to_timedelta(arr_driving["duration"])
        
        # 如果有足够信息，计算完整时间轴
        if transport_departure_dt and dep_drive_duration:
            # 到达车站/机场的时间（火车建议提前1小时，飞机建议提前2小时）
            advance_time = timedelta(hours=2) if is_flight else timedelta(hours=1)
            arrival_at_station_dt = transport_departure_dt - advance_time
            # 从出发地出发的最晚时间
            latest_departure_from_origin_dt = arrival_at_station_dt - dep_drive_duration

            # 格式化时间
            timeline["departure_from_origin"] = latest_departure_from_origin_dt.strftime("%H:%M")
            timeline["arrival_at_station"] = arrival_at_station_dt.strftime("%H:%M")
            timeline["transport_departure"] = transport_departure_dt.strftime("%H:%M")

            # 记录日期
            timeline["departure_date"] = latest_departure_from_origin_dt.strftime("%Y-%m-%d")
            timeline["departure_date_str"] = latest_departure_from_origin_dt.strftime("%m月%d日")

        # 计算到达时间
        if transport_arrival_dt and arr_drive_duration:
            # 最终到达目的地的时间
            final_arrival_at_destination_dt = transport_arrival_dt + arr_drive_duration

            # 格式化时间
            timeline["transport_arrival"] = transport_arrival_dt.strftime("%H:%M")
            timeline["arrival_at_destination"] = final_arrival_at_destination_dt.strftime("%H:%M")

            # 记录日期
            timeline["arrival_date"] = final_arrival_at_destination_dt.strftime("%Y-%m-%d")
            timeline["arrival_date_str"] = final_arrival_at_destination_dt.strftime("%m月%d日")
            
            # 判断是否跨天
            is_multi_day = timeline.get("departure_date") != timeline.get("arrival_date")
            timeline["is_multi_day"] = is_multi_day
        
        print(f"[整合报告专家] ✅ 时间轴计算完成: {timeline}")
    except Exception as e:
        print(f"[整合报告专家] ⚠️ 计算时间轴时出错: {str(e)}")
        print(traceback.format_exc())
        # 使用占位时间轴
        timeline = {
            "departure_from_origin": "09:00",
            "arrival_at_station": "09:30",
            "train_departure": "10:30",
            "train_arrival": "13:00",
            "arrival_at_destination": "13:30",
            "departure_date_str": "今天",
            "arrival_date_str": "今天",
            "is_multi_day": False
        }
    
    # 生成最终行程规划
    final_plan = {
        "title": f"从{departure_place}到{arrival_place}的行程规划",
        "overview": f"这是一份从{departure_place}到{arrival_place}的行程规划，包含{'航班' if is_flight else '高铁/火车'}和本地交通方案。",
        "departure_city": departure_city,
        "arrival_city": arrival_city,
        "departure_place": departure_place,
        "arrival_place": arrival_place,
        "travel_date": travel_date,
        "timeline": timeline,
        "transportation": {
            "major": {
                "type": transport_type,
                "code": transport_code,
                "departure": {
                    "station": transport_dep_station,
                    "time": transport_departure_time
                },
                "arrival": {
                    "station": transport_arr_station,
                    "time": transport_arrival_time
                },
                "duration": transport_duration,
                "seat": flight.get("seat", {}) if is_flight else train.get("seat", {}) if train else {},
                # 添加中转信息
                "isTransfer": train.get("isTransfer", False) if train else False,
                "transferInfo": train.get("transferInfo", {}) if train and train.get("isTransfer") else {}
            },
            "local": {
                "departure": {
                    "mode": dep_driving.get("transport_mode", "打车"),
                    "transport_mode": dep_driving.get("transport_mode", "打车"),
                    "distance": dep_driving.get("distance", "未知"),
                    "duration": dep_driving.get("duration", "未知"),
                    "steps": dep_driving.get("steps", []),
                    "traffic_lights": dep_driving.get("traffic_lights", 0),
                    "taxi_cost": dep_driving.get("taxi_cost", 0),
                    "cost": dep_driving.get("cost", 0),
                    "walking_distance": dep_driving.get("walking_distance", 0),
                    "transfer_count": dep_driving.get("transfer_count", 0)
                },
                "arrival": {
                    "mode": arr_driving.get("transport_mode", "打车"),
                    "transport_mode": arr_driving.get("transport_mode", "打车"),
                    "distance": arr_driving.get("distance", "未知"),
                    "duration": arr_driving.get("duration", "未知"),
                    "steps": arr_driving.get("steps", []),
                    "traffic_lights": arr_driving.get("traffic_lights", 0),
                    "taxi_cost": arr_driving.get("taxi_cost", 0),
                    "cost": arr_driving.get("cost", 0),
                    "walking_distance": arr_driving.get("walking_distance", 0),
                    "transfer_count": arr_driving.get("transfer_count", 0)
                }
            }
        },
        "map_data": state.get("map_data", {}),
        "tips": []  # 将在下面通过AI生成
    }

    # 使用AI生成个性化旅行建议
    try:
        print("[整合报告专家] 🤖 正在生成AI旅行建议...")
        advice_generator = TravelAdviceGenerator()

        # 构建规划数据用于AI生成建议
        plan_data = {
            'departure_place': departure_place,
            'arrival_place': arrival_place,
            'date_str': state.get('travel_date', '未知日期'),
            'transportation': {
                'major': {
                    'type': 'flight' if is_flight else 'train',
                    'code': transport_code
                }
            }
        }

        # 生成AI建议
        ai_advice = advice_generator.generate_travel_advice(plan_data)

        if ai_advice.get('success') and ai_advice.get('sections'):
            # 将AI生成的建议转换为简洁的提示列表
            sections = ai_advice['sections']
            ai_tips = []

            # 从各个部分提取关键提示
            if sections.get('timing_and_risks'):
                timing_content = sections['timing_and_risks']
                # 提取最佳出发时间信息
                lines = timing_content.split('\n')
                for line in lines:
                    if '最佳出发时间' in line or '建议' in line:
                        clean_line = line.strip().replace('最佳出发时间:', '').replace('建议', '').strip()
                        if clean_line and len(clean_line) > 10:
                            ai_tips.append("⏰ " + clean_line[:80] + ("..." if len(clean_line) > 80 else ""))
                            break
                if not ai_tips:  # 如果没有找到合适的内容，使用默认
                    ai_tips.append("⏰ 请提前充足时间出发，避免误车误机")

            if sections.get('packing_list'):
                ai_tips.append("🎒 请根据目的地天气和行程天数准备合适的衣物和必需品")

            if sections.get('destination_tips'):
                tips_content = sections['destination_tips']
                # 提取交通衔接信息
                lines = tips_content.split('\n')
                for line in lines:
                    if '交通' in line and ('地铁' in line or '出租车' in line or '公交' in line):
                        clean_line = line.strip().replace('交通衔接:', '').strip()
                        if clean_line and len(clean_line) > 10:
                            ai_tips.append("🚇 " + clean_line[:80] + ("..." if len(clean_line) > 80 else ""))
                            break
                if len(ai_tips) < 3:  # 如果没有找到交通信息，使用默认
                    ai_tips.append("🚇 到达后建议选择地铁、出租车或网约车前往市区")

            # 保存完整的AI建议到额外信息中
            final_plan["ai_travel_advice"] = ai_advice
            final_plan["tips"] = ai_tips

            print(f"[整合报告专家] ✅ AI旅行建议生成成功，共{len(ai_tips)}条提示")
        else:
            # AI生成失败，使用默认提示
            print("[整合报告专家] ⚠️ AI建议生成失败，使用默认提示")
            final_plan["tips"] = [
                f"请提前至少{'2小时' if is_flight else '1小时'}到达{transport_dep_station}，以免{'误机' if is_flight else '误车'}",
                f"请携带有效身份证件，{'机票' if is_flight else '高铁/火车票'}需要实名制购票",
                f"行李超过{'20公斤' if is_flight else '20公斤'}可能需要额外费用"
            ]

    except Exception as e:
        print(f"[整合报告专家] ❌ 生成AI旅行建议时出错: {str(e)}")
        # 使用默认提示
        final_plan["tips"] = [
            f"请提前至少{'2小时' if is_flight else '1小时'}到达{transport_dep_station}，以免{'误机' if is_flight else '误车'}",
            f"请携带有效身份证件，{'机票' if is_flight else '高铁/火车票'}需要实名制购票",
            f"行李超过{'20公斤' if is_flight else '20公斤'}可能需要额外费用"
        ]

    # 保存最终规划到状态
    state["final_plan"] = final_plan
    
    # 记录思考过程
    thinking = (
        f"已生成从{departure_place}到{arrival_place}的完整行程规划。\n" +
        f"大交通: {transport_code}次{'航班' if is_flight else '列车'}，{transport_dep_station}{transport_departure_time}{'起飞' if is_flight else '出发'}，" +
        f"{transport_arr_station}{transport_arrival_time}{'降落' if is_flight else '到达'}，历时{transport_duration}。\n" +
        f"小交通: 从出发地{timeline.get('departure_from_origin', '未知')}出发，" +
        f"{timeline.get('arrival_at_station', '未知')}到达{transport_dep_station}；" +
        f"{'航班降落' if is_flight else '列车到达'}后，从{transport_arr_station}出发，预计{timeline.get('arrival_at_destination', '未知')}到达最终目的地。"
    )
    
    if "thinking_process" not in state:
        state["thinking_process"] = []
    state["thinking_process"].append(thinking)
    
    print("[整合报告专家] ✅ 行程规划生成完毕")
    
    # 最终节点，标记流程结束
    state["next_step"] = "end"
    return state


def ask_user_node(state: ItineraryState) -> ItineraryState:
    """
    用户交互节点：当需要向用户请求澄清或额外信息时使用。
    这个节点会暂停工作流，等待用户回应，然后根据回应更新状态。
    """
    print("\n[用户交互] ❓ 需要用户输入...")
    
    # 追踪执行记录
    state["execution_history"].append("ask_user")
    state["current_step"] = "ask_user"
    
    # 确保state中有next_step字段
    if "next_step" not in state:
        state["next_step"] = "planner"
    
    # 检查是否有用户响应
    if state.get("user_clarification_response"):
        print(f"[用户交互] ✅ 收到用户回应: {state['user_clarification_response']}")
        
        # 根据用户响应更新状态
        user_response = state["user_clarification_response"]
        clarification_question = state.get("clarification_question", "")
        
        # 基于提问内容处理用户响应
        if "出发城市" in clarification_question or "目的地城市" in clarification_question:
            # 尝试从用户响应中提取城市信息
            system_prompt = """
            你是一个专业的旅行数据分析师。从用户回答中提取出发城市和目的地城市。
            以JSON格式返回，仅返回JSON，不要有其他文本。格式为：
            {
                "departure_city": "提取的出发城市",
                "arrival_city": "提取的目的地城市"
            }
            如果无法提取某项，将其值设为null。
            """
            
            parse_messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"问题：{clarification_question}\n用户回答：{user_response}")
            ]
            
            try:
                response = llm.invoke(parse_messages)
                parsed = json.loads(response.content)
                
                # 更新状态
                if parsed.get("departure_city"):
                    state["departure_city"] = parsed["departure_city"]
                    print(f"[用户交互] ✅ 已更新出发城市: {parsed['departure_city']}")
                
                if parsed.get("arrival_city"):
                    state["arrival_city"] = parsed["arrival_city"]
                    print(f"[用户交互] ✅ 已更新目的地城市: {parsed['arrival_city']}")
                
            except Exception as e:
                print(f"[用户交互] ⚠️ 解析用户响应时出错: {str(e)}")
        
        elif "出行日期" in clarification_question:
            # 尝试从用户响应中提取日期信息
            system_prompt = """
            你是一个专业的日期分析师。从用户回答中提取出行日期，并标准化为YYYY-MM-DD格式。
            以JSON格式返回，仅返回JSON，不要有其他文本。格式为：
            {
                "travel_date": "YYYY-MM-DD格式的日期"
            }
            如果无法提取日期，将其值设为null。
            """
            
            parse_messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"问题：{clarification_question}\n用户回答：{user_response}")
            ]
            
            try:
                response = llm.invoke(parse_messages)
                parsed = json.loads(response.content)
                
                # 更新状态
                if parsed.get("travel_date"):
                    state["travel_date"] = parsed["travel_date"]
                    print(f"[用户交互] ✅ 已更新出行日期: {parsed['travel_date']}")
                
            except Exception as e:
                print(f"[用户交互] ⚠️ 解析用户响应时出错: {str(e)}")
        
        elif "火车" in clarification_question.lower() or "高铁" in clarification_question.lower() or "交通方式" in clarification_question.lower() or "备选" in clarification_question.lower():
            # 处理交通方式相关的澄清
            # 检查用户回复是否包含关键信息，可能需要重新设置某些标志
            print(f"[用户交互] ℹ️ 处理交通方式相关回复: {user_response}")
            
            # 重置失败状态，让系统可以尝试新的查询
            if "failed_searches" in state:
                print("[用户交互] 🔄 重置失败搜索记录，允许尝试新的交通查询")
                state["failed_searches"] = {}
                
            # 尝试从回复中提取备选方案
            system_prompt = """
            你是一个专业的旅行交通分析师。从用户回答中提取备选交通方式或城市信息。
            以JSON格式返回，仅返回JSON，不要有其他文本。格式为：
            {
                "alternative_transport": "用户提到的备选交通方式，如飞机、汽车等",
                "alternative_departure_city": "用户提到的可能备选出发城市",
                "alternative_arrival_city": "用户提到的可能备选目的地城市"
            }
            如果无法提取某项，将其值设为null。
            """
            
            try:
                parse_messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"问题：{clarification_question}\n用户回答：{user_response}")
                ]
                
                response = llm.invoke(parse_messages)
                parsed = json.loads(response.content)
                
                # 更新状态
                if parsed.get("alternative_transport"):
                    state["alternative_transport"] = parsed["alternative_transport"]
                    print(f"[用户交互] ✅ 用户提供了备选交通方式: {parsed['alternative_transport']}")
                    
                if parsed.get("alternative_departure_city"):
                    state["departure_city"] = parsed["alternative_departure_city"]
                    print(f"[用户交互] ✅ 更新出发城市为: {parsed['alternative_departure_city']}")
                
                if parsed.get("alternative_arrival_city"):
                    state["arrival_city"] = parsed["alternative_arrival_city"]
                    print(f"[用户交互] ✅ 更新目的地城市为: {parsed['alternative_arrival_city']}")
                    
                # 记录思考过程
                if "thinking_process" not in state:
                    state["thinking_process"] = []
                state["thinking_process"].append(f"根据用户提供的信息，我们将尝试查找新的交通方案。" +
                   (f"备选交通方式: {parsed.get('alternative_transport')}" if parsed.get('alternative_transport') else "") +
                   (f", 出发城市: {parsed.get('alternative_departure_city')}" if parsed.get('alternative_departure_city') else "") +
                   (f", 目的地城市: {parsed.get('alternative_arrival_city')}" if parsed.get('alternative_arrival_city') else ""))
                
            except Exception as e:
                print(f"[用户交互] ⚠️ 解析交通方式回复时出错: {str(e)}")
                # 简单处理，不中断流程
                if "thinking_process" not in state:
                    state["thinking_process"] = []
                state["thinking_process"].append(f"用户提供了新的出行信息，但我无法完全理解。我们将尝试其他可能的方案。")
                
            # 即使解析失败，也确保重置失败计数，允许系统继续尝试
            state["major_transport_failure_count"] = 0
            
        elif "备选的交通方式" in clarification_question:
            # 用户提供了替代的交通方式，可能需要重新规划
            # 在这个简化版本中，我们只是记录用户的响应
            print(f"[用户交互] ℹ️ 用户提供了替代的交通方式: {user_response}")
            
            # 重置失败状态
            if "failed_searches" in state:
                state["failed_searches"] = {}
            state["major_transport_failure_count"] = 0
            
            # 添加到思考过程
            if "thinking_process" not in state:
                state["thinking_process"] = []
            state["thinking_process"].append(f"用户建议的替代交通方式: {user_response}")
        
        else:
            # 通用处理：尝试从用户响应中提取有用信息
            print(f"[用户交互] ℹ️ 通用处理用户响应: {user_response}")
            
            # 添加到思考过程
            if "thinking_process" not in state:
                state["thinking_process"] = []
            state["thinking_process"].append(f"用户回应: {user_response}")
        
        # 清除交互标记和响应，准备继续流程
        state["is_clarification_needed"] = False
        state["user_clarification_response"] = None
    else:
        # 没有用户响应，说明这是暂停点
        # 实际暂停逻辑在外部处理（FastAPI路由）
        print(f"[用户交互] ⏸️ 工作流暂停，等待用户响应: {state.get('clarification_question', '需要更多信息')}")
        
        # 保持标记为需要澄清，以便外部处理程序识别
        state["is_clarification_needed"] = True
    
    # 默认返回规划师节点
    if not state.get("next_step") or state.get("next_step") == "ask_user":
        state["next_step"] = "planner"
    
    return state


def intelligent_ask_user_node(state: ItineraryState) -> ItineraryState:
    """
    智能用户交互节点：使用LLM分析用户回复并智能处理
    """
    print("\n[智能交互] 🤖 正在智能处理用户交互...")
    print(f"[智能交互] 🔍 DEBUG - 函数开始执行，state类型: {type(state)}")
    print(f"[智能交互] 🔍 DEBUG - state内容: {dict(state) if hasattr(state, 'keys') else 'state不是字典类型'}")

    # 追踪执行记录
    if "execution_history" not in state:
        state["execution_history"] = []
    state["execution_history"].append("intelligent_ask_user")
    state["current_step"] = "intelligent_ask_user"

    # 获取用户回复
    user_response = state.get("user_response", "")
    clarification_question = state.get("clarification_question", "")

    print(f"[智能交互] 📝 用户回复: '{user_response}'")
    print(f"[智能交互] ❓ 澄清问题: '{clarification_question}'")

    if not user_response:
        print("[智能交互] ⚠️ 等待用户回复...")
        state["next_step"] = "ask_user"
        return state

    # 使用LLM智能分析用户回复
    print("[智能交互] 🤖 使用AI分析用户回复...")

    analysis_prompt = f"""
    你是一位专业的旅行规划助手，需要分析用户的回复并提取有用信息。

    原始问题：{clarification_question}
    用户回复：{user_response}

    当前状态：
    - 出发地：{state.get('departure_place', '未知')}
    - 目的地：{state.get('arrival_place', '未知')}
    - 出发城市：{state.get('departure_city', '未知')}
    - 目的城市：{state.get('arrival_city', '未知')}
    - 日期：{state.get('travel_date', '未知')}

    特别注意：
    - 如果用户说"全部你来定"、"无需求"、"没有特殊要求"、"随便"等，表示用户没有特殊偏好，可以继续规划
    - 如果用户提供了具体的交通方式、时间、地点等信息，需要提取这些信息
    - 如果用户的回复模糊不清，可能需要进一步澄清

    请分析用户回复并返回JSON格式的结果：
    {{
        "理解结果": "用户想表达什么",
        "提取信息": {{
            "出发地": "如果用户提供了新的出发地",
            "目的地": "如果用户提供了新的目的地",
            "出发城市": "如果用户提供了新的出发城市",
            "目的城市": "如果用户提供了新的目的城市",
            "日期": "如果用户提供了新的日期",
            "交通偏好": "如果用户表达了交通方式偏好，如果用户说无偏好则填'无偏好'",
            "其他要求": "其他特殊要求，如果用户说无要求则填'无特殊要求'"
        }},
        "下一步建议": "major_transport",
        "需要进一步澄清": false,
        "澄清问题": ""
    }}

    注意：如果用户表示无特殊偏好或要求，应该设置"需要进一步澄清"为false，"下一步建议"为"major_transport"，让系统继续规划流程。
    """

    try:
        analysis_messages = [
            SystemMessage(content="你是专业的用户交互分析AI，擅长理解用户意图并提取关键信息。"),
            HumanMessage(content=analysis_prompt)
        ]

        response = llm.invoke(analysis_messages)
        response_content = response.content.strip()

        # 解析LLM响应
        if response_content.startswith('```json'):
            response_content = response_content[7:-3]
        elif response_content.startswith('```'):
            response_content = response_content[3:-3]

        json_start = response_content.find('{')
        json_end = response_content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            response_content = response_content[json_start:json_end]

        analysis_result = json.loads(response_content)

        # 更新状态信息
        extracted_info = analysis_result.get("提取信息", {})

        for key, value in extracted_info.items():
            if value and value != "未知" and value.strip():
                if key == "出发地":
                    state["departure_place"] = value
                elif key == "目的地":
                    state["arrival_place"] = value
                elif key == "出发城市":
                    state["departure_city"] = value
                elif key == "目的城市":
                    state["arrival_city"] = value
                elif key == "日期":
                    state["travel_date"] = value
                elif key == "交通偏好":
                    state["transport_preference"] = value

                print(f"[智能交互] ✅ 更新{key}: {value}")

        # 记录AI分析过程
        if "thinking_process" not in state:
            state["thinking_process"] = []

        state["thinking_process"].append(f"🤖 用户回复分析：{analysis_result.get('理解结果', '未知')}")

        # 检查是否需要进一步澄清
        if analysis_result.get("需要进一步澄清", False):
            state["is_clarification_needed"] = True
            state["clarification_question"] = analysis_result.get("澄清问题", "请提供更多信息。")
            state["next_step"] = "ask_user"
            print(f"[智能交互] ❓ 需要进一步澄清：{state['clarification_question']}")
        else:
            # 清除澄清状态
            state["is_clarification_needed"] = False
            state["user_response"] = ""  # 清除已处理的用户回复

            # 根据AI建议决定下一步
            next_step = analysis_result.get("下一步建议", "planner")
            state["next_step"] = next_step

            print(f"[智能交互] ✅ 信息收集完成，下一步：{next_step}")

        return state

    except Exception as e:
        print(f"[智能交互] ⚠️ AI分析失败，使用简单处理: {str(e)}")

        # 回退到简单处理
        state["is_clarification_needed"] = False
        state["user_response"] = ""
        state["next_step"] = "planner"

        # 记录用户回复
        if "thinking_process" not in state:
            state["thinking_process"] = []
        state["thinking_process"].append(f"用户回复：{user_response}")

        return state
