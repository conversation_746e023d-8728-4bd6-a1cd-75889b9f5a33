<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>您的专属行程规划 - AI Trip Plan</title>

    <!-- 引入外部资源 -->
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- 自定义样式与动画 -->
    <style>
        body {
            font-family: 'Noto Sans SC', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e1e8f0 100%);
            opacity: 0; /* 初始不可见 */
            animation: page-fade-in 0.6s ease-out forwards;
            animation-delay: 0.2s; /* 延迟一点以获得更好的过渡感 */
        }
        @keyframes page-fade-in {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .font-serif { font-family: 'Noto Serif SC', serif; }
        .card-shadow { box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        #map {
            height: 500px;
            border-radius: 0.75rem;
            border: 1px solid #e5e7eb;
            z-index: 1; /* Ensure map is interactable */
            margin-bottom: 20px;
        }
        /* 自定义地图标记动画 (已修复定位问题) */
        .pulse-marker {
            border-radius: 50%;
            border: 3px solid rgba(29, 78, 216, 0.8);
            box-shadow: 0 0 0 0 rgba(29, 78, 216, 1);
            animation: pulse-shadow 2s infinite;
        }
        @keyframes pulse-shadow {
            0% { box-shadow: 0 0 0 0 rgba(29, 78, 216, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(29, 78, 216, 0); }
            100% { box-shadow: 0 0 0 0 rgba(29, 78, 216, 0); }
        }
        
        /* 展开/折叠驾车路线指引 */
        .steps-container {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
            opacity: 0;
        }
        .steps-container.open {
            max-height: 500px; /* 增加最大高度，确保能显示全部内容 */
            overflow-y: auto;  /* 添加滚动条以防内容过多 */
            opacity: 1;
        }
        
        /* 导航步骤样式 */
        .nav-step {
            position: relative;
            padding-left: 28px;
            margin-bottom: 12px;
            border-bottom: 1px dashed #e2e8f0;
            padding-bottom: 8px;
        }
        .nav-step:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .nav-step:before {
            content: '';
            position: absolute;
            left: 0;
            top: 5px;
            width: 18px;
            height: 18px;
            background-color: #edf2f7;
            border-radius: 50%;
            border: 2px solid #a0aec0;
        }
        .nav-step:after {
            content: '';
            position: absolute;
            left: 9px;
            top: 23px;
            bottom: -13px;
            width: 2px;
            background-color: #e2e8f0;
        }
        .nav-step:last-child:after {
            display: none;
        }

        /* AI建议格式化样式 */
        .formatted-content {
            line-height: 1.7;
        }

        .formatted-content h4 {
            font-weight: 600;
            margin: 1rem 0 0.5rem 0;
            color: #374151;
            font-size: 1.1em;
        }

        .formatted-content ul {
            margin: 0.75rem 0;
            padding-left: 1.5rem;
            list-style-type: disc;
        }

        .formatted-content li {
            margin: 0.5rem 0;
            padding-left: 0.25rem;
        }

        .formatted-content p {
            margin: 0.75rem 0;
        }

        .formatted-content strong {
            font-weight: 600;
            color: #1f2937;
        }

        /* 响应式字体大小 */
        @media (max-width: 640px) {
            .formatted-content {
                font-size: 0.875rem;
                line-height: 1.6;
            }

            .formatted-content h4 {
                font-size: 1rem;
                margin: 0.75rem 0 0.375rem 0;
            }

            .formatted-content ul {
                padding-left: 1.25rem;
                margin: 0.5rem 0;
            }

            .formatted-content li {
                margin: 0.375rem 0;
            }

            .formatted-content p {
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 主容器 -->
    <div class="container mx-auto p-4 md:p-8">

        <!-- 页面标题 -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold font-serif mb-3 text-gray-900">您的专属行程规划</h1>
            <p class="text-lg text-gray-600">
                从 <span class="font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-indigo-600">{{ plan.departure_place }}</span> 
                到 <span class="font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-indigo-600">{{ plan.arrival_place }}</span>
            </p>
        </header>

        <div class="grid grid-cols-1 gap-8">
            <!-- 行程规划详情 -->
            <main>
                
                <!-- 行程总览地图 -->
                <section class="bg-white rounded-xl card-shadow p-6 mb-8">
                    <h2 class="text-2xl font-bold mb-4 font-serif text-gray-800">行程总览</h2>
                    <div id="map"></div>
                </section>
                
                {% if plan.timeline %}
                <!-- 全新时间轴模块 -->
                <section class="bg-white rounded-xl card-shadow p-6 mb-8">
                    <h2 class="text-2xl font-bold mb-6 font-serif text-gray-800">行程时间轴</h2>
                    <div class="flex items-center justify-between text-center text-sm text-gray-600">
                        <div class="flex-1">
                            <div class="text-xs text-gray-500">{{ plan.timeline.departure_date_str }}</div>
                            <div class="font-bold text-lg text-blue-600">{{ plan.timeline.departure_from_origin }}</div>
                            <div class="mt-1">从家出发</div>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute w-full h-0.5 bg-gray-200 top-1/2 left-0 transform -translate-y-1/2"></div>
                            <div class="absolute w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 top-1/2 left-0 transform -translate-y-1/2" 
                                 style="clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);"></div>
                            <i class="fas fa-car text-blue-500 bg-white p-1 rounded-full border-2 border-blue-200 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500">&nbsp;</div>
                            <div class="font-bold text-lg">{{ plan.timeline.arrival_at_dep_station }}</div>
                            <div class="mt-1">到达车站</div>
                        </div>
                         <div class="flex-1 relative">
                            <div class="absolute w-full h-0.5 bg-gray-200 top-1/2 left-0 transform -translate-y-1/2"></div>
                            <div class="absolute w-full h-0.5 bg-gradient-to-r from-purple-500 to-green-500 top-1/2 left-0 transform -translate-y-1/2"
                                 style="clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);"></div>
                            <i class="fas fa-train text-purple-500 bg-white p-1 rounded-full border-2 border-purple-200 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10"></i>
                        </div>
                        <div class="flex-1">
                             <div class="text-xs text-gray-500">&nbsp;</div>
                            <div class="font-bold text-lg">{{ plan.timeline.train_departure }}</div>
                            <div class="mt-1">火车出发</div>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute w-full h-0.5 bg-gray-200 top-1/2 left-0 transform -translate-y-1/2"></div>
                            <div class="absolute w-full h-0.5 bg-gradient-to-r from-green-500 to-yellow-500 top-1/2 left-0 transform -translate-y-1/2"
                                 style="clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);"></div>
                            <i class="fas fa-train text-green-500 bg-white p-1 rounded-full border-2 border-green-200 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500">{% if plan.timeline.is_multi_day %}{{ plan.timeline.arrival_date_str }}{% else %}&nbsp;{% endif %}</div>
                            <div class="font-bold text-lg">{{ plan.timeline.train_arrival }}</div>
                            <div class="mt-1">火车到达</div>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute w-full h-0.5 bg-gray-200 top-1/2 left-0 transform -translate-y-1/2"></div>
                            <div class="absolute w-full h-0.5 bg-gradient-to-r from-yellow-500 to-red-500 top-1/2 left-0 transform -translate-y-1/2"
                                 style="clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);"></div>
                            <i class="fas fa-car text-yellow-500 bg-white p-1 rounded-full border-2 border-yellow-200 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500">&nbsp;</div>
                            <div class="font-bold text-lg text-red-600">{{ plan.timeline.arrival_at_destination }}</div>
                            <div class="mt-1">到达目的地</div>
                        </div>
                    </div>
                </section>
                {% endif %}
                
                <!-- 详细行程卡片 -->
                <section class="bg-white rounded-xl card-shadow p-6">
                    <h2 class="text-2xl font-bold mb-2 font-serif text-gray-800">详细行程</h2>
                    <p class="text-gray-500 mb-6">{{ plan.date_str }}</p>

                    <div class="relative">
                        <!-- 垂直连接线 -->
                        <div class="absolute left-6 top-6 bottom-6 w-0.5 bg-gradient-to-b from-blue-300 via-purple-300 to-green-300"></div>

                        {% if plan.transportation.local.departure and plan.timeline %}
                        <!-- 出发地到高铁站 -->
                        <div class="flex items-start mb-8 z-10 relative">
                            <div class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mr-6 shadow-lg">
                                {% if plan.transportation.local.departure.transport_mode == "公交" %}
                                <i class="fas fa-bus text-xl text-white"></i>
                                {% else %}
                                <i class="fas fa-taxi text-xl text-white"></i>
                                {% endif %}
                            </div>
                            <div class="bg-gray-50 p-5 rounded-lg flex-1 shadow-sm">
                                <div class="flex justify-between items-baseline mb-2">
                                    <h3 class="font-bold text-lg text-blue-800">
                                        小交通({{ plan.transportation.local.departure.transport_mode or "打车" }})
                                    </h3>
                                    <span class="text-2xl font-bold text-gray-800">{{ plan.timeline.departure_from_origin }}</span>
                                </div>
                                <p class="text-gray-600 font-medium text-md mb-3 pb-3 border-b border-gray-200">
                                    {{ plan.departure_place }} → {{ plan.transportation.major.departure.station }}
                                </p>

                                <div class="text-sm space-y-2">
                                    {% if plan.transportation.local.departure.transport_mode == "公交" %}
                                    <p><i class="fas fa-clock w-4 mr-1 text-gray-500"></i><strong>预计用时:</strong> {{ plan.transportation.local.departure.duration }}</p>
                                    <p><i class="fas fa-money-bill w-4 mr-1 text-gray-500"></i><strong>费用:</strong> {{ plan.transportation.local.departure.cost_text or ('¥' + (plan.transportation.local.departure.cost|string) if plan.transportation.local.departure.cost and plan.transportation.local.departure.cost > 0 else '具体费用请联系运营商') }}</p>
                                    {% if plan.transportation.local.departure.walking_distance %}
                                    <p><i class="fas fa-walking w-4 mr-1 text-gray-500"></i><strong>步行距离:</strong> {{ plan.transportation.local.departure.walking_distance }}米</p>
                                    {% endif %}
                                    {% if plan.transportation.local.departure.transfer_count is defined %}
                                    <p><i class="fas fa-exchange-alt w-4 mr-1 text-gray-500"></i><strong>换乘次数:</strong> {{ plan.transportation.local.departure.transfer_count }}次</p>
                                    {% endif %}
                                    {% else %}
                                    <p><i class="fas fa-route w-4 mr-1 text-gray-500"></i><strong>距离:</strong> {{ plan.transportation.local.departure.distance|int//1000 }}公里{{ plan.transportation.local.departure.distance|int%1000 }}米</p>
                                    <p><i class="fas fa-clock w-4 mr-1 text-gray-500"></i><strong>预计用时:</strong> {{ plan.transportation.local.departure.duration }}</p>
                                    <p><i class="fas fa-money-bill w-4 mr-1 text-gray-500"></i><strong>打车费用:</strong> ¥{{ plan.transportation.local.departure.taxi_cost or 0 }}</p>
                                    {% if plan.transportation.local.departure.traffic_lights is defined %}
                                    <p><i class="fas fa-traffic-light w-4 mr-1 text-gray-500"></i><strong>红绿灯:</strong> {{ plan.transportation.local.departure.traffic_lights }}个</p>
                                    {% endif %}
                                    {% endif %}
                                    
                                    <!-- 驾车导航指引 -->
                                    <div class="pt-2">
                                        <button id="dep-steps-btn" class="text-sm text-indigo-600 hover:text-indigo-800 focus:outline-none">
                                            <i class="fas fa-angle-down mr-1"></i> 查看{% if plan.transportation.local.departure.transport_mode == "公交" %}公交路线{% else %}驾车路线{% endif %}指引
                                        </button>
                                        <div id="dep-steps-container" class="steps-container mt-3 bg-gray-100 p-3 rounded-lg text-sm">
                                            {% for step in plan.transportation.local.departure.steps %}
                                            <div class="nav-step mb-2 p-2 bg-white rounded border-l-4 {% if step.type == 'transit' %}border-blue-500{% else %}border-green-500{% endif %}">
                                                {% if step.type == 'transit' %}
                                                    <div class="flex items-center mb-1">
                                                        <i class="fas fa-bus text-blue-500 mr-2"></i>
                                                        <p class="font-medium">{{ step.instruction or step.name }}</p>
                                                    </div>
                                                    {% if step.departure_stop and step.arrival_stop %}
                                                    <p class="text-xs text-gray-600 ml-6">{{ step.departure_stop }} → {{ step.arrival_stop }}</p>
                                                    {% endif %}
                                                    {% if step.distance %}
                                                    <p class="text-xs text-gray-500 ml-6">距离: {{ step.distance }}米</p>
                                                    {% endif %}
                                                {% else %}
                                                    <div class="flex items-center">
                                                        
                                                        <p>{{ step.instruction or "步行" }}</p>
                                                    </div>
                                                    {% if step.distance %}
                                                    <p class="text-xs text-gray-500 ml-6">{{ step.distance }}米</p>
                                                    {% endif %}
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 大交通（全新整合设计） -->
                        <div class="flex items-start mb-8 z-10 relative">
                            <!-- 左侧图标 -->
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center mr-6 shadow-lg flex-shrink-0">
                                {% if plan.transportation.major.type == 'train' %}
                                <i class="fas fa-train text-xl text-white"></i>
                                {% else %}
                                <i class="fas fa-plane text-xl text-white"></i>
                                {% endif %}
                            </div>

                            <!-- 右侧内容 -->
                            <div class="flex-1">
                                <!-- 标题和收藏按钮 -->
                                <div class="flex justify-between items-start mb-1">
                                    <div>
                                        <h3 class="font-bold text-lg text-gray-800">交通</h3>
                                        <p class="text-sm text-gray-500 mt-1">
                                            乘坐{{ '火车' if plan.transportation.major.type == 'train' else '飞机' }} {{ plan.date_str }}
                                        </p>
                                    </div>
                                    <button class="text-gray-400 hover:text-yellow-500 transition-colors duration-200" title="收藏">
                                        <i class="far fa-star text-xl"></i>
                                    </button>
                                </div>
                                
                                <!-- 航班/车次信息卡片 -->
                                {% if plan.transportation.major.isTransfer %}
                                <!-- 中转票卡片 -->
                                <div class="bg-white p-5 mt-4 rounded-xl shadow-md border border-orange-200 transform hover:shadow-xl transition-shadow duration-300">
                                    <!-- 中转标识 -->
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-route text-orange-500 mr-2"></i>
                                            <span class="text-orange-600 font-semibold">中转行程</span>
                                        </div>
                                        <div class="text-right">
                                            {% if plan.transportation.major.seat and plan.transportation.major.seat.price %}
                                            <p class="text-xl font-bold text-red-600">
                                                <span class="text-sm font-normal">¥</span>{{ plan.transportation.major.seat.price }}
                                            </p>
                                            {% endif %}
                                            <button class="bg-indigo-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 mt-2">
                                                预订
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 中转行程详情 -->
                                    {% if plan.transportation.major.transferInfo and plan.transportation.major.transferInfo.legs %}
                                    <div class="space-y-4">
                                        {% for leg in plan.transportation.major.transferInfo.legs %}
                                        <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-orange-400">
                                            <div class="flex items-center justify-between">
                                                <!-- 第一段/第二段标识 -->
                                                <div class="flex items-center">
                                                    <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full mr-3">第{{ loop.index }}段</span>
                                                    <i class="fas fa-train text-gray-500 mr-2"></i>
                                                    <span class="font-medium text-gray-700">{{ leg.trainNo or leg.trainNumber or '车次' }}</span>
                                                </div>
                                                {% if leg.price %}
                                                <span class="text-sm text-gray-500">参考票价: ¥{{ leg.price }}</span>
                                                {% endif %}
                                            </div>

                                            <!-- 行程信息 -->
                                            <div class="flex items-center mt-3">
                                                <!-- 出发信息 -->
                                                <div class="text-center w-24">
                                                    <p class="text-lg font-bold text-gray-900">{{ leg.fromTime }}</p>
                                                    <p class="text-xs text-gray-500 mt-1 truncate">{{ leg.fromStation }}</p>
                                                </div>

                                                <!-- 行程线 -->
                                                <div class="flex-1 text-center px-3">
                                                    {% if leg.fromTime and leg.toTime %}
                                                        {% set from_hour = leg.fromTime.split(':')[0]|int %}
                                                        {% set from_min = leg.fromTime.split(':')[1]|int %}
                                                        {% set to_hour = leg.toTime.split(':')[0]|int %}
                                                        {% set to_min = leg.toTime.split(':')[1]|int %}
                                                        {% set total_minutes = (to_hour * 60 + to_min) - (from_hour * 60 + from_min) %}
                                                        {% if total_minutes < 0 %}
                                                            {% set total_minutes = total_minutes + 24 * 60 %}
                                                        {% endif %}
                                                        {% set hours = total_minutes // 60 %}
                                                        {% set minutes = total_minutes % 60 %}
                                                        <p class="text-xs text-gray-500">
                                                            {% if hours > 0 %}{{ hours }}小时{% endif %}
                                                            {% if minutes > 0 %}{{ minutes }}分钟{% endif %}
                                                        </p>
                                                    {% else %}
                                                        <p class="text-xs text-gray-500">行程时间</p>
                                                    {% endif %}
                                                    <div class="w-full h-px bg-orange-300 relative my-1">
                                                        <i class="fas fa-circle text-orange-400 text-xs absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1/2"></i>
                                                        <i class="fas fa-circle text-orange-400 text-xs absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2"></i>
                                                    </div>
                                                </div>

                                                <!-- 到达信息 -->
                                                <div class="text-center w-24">
                                                    <p class="text-lg font-bold text-gray-900">{{ leg.toTime }}</p>
                                                    <p class="text-xs text-gray-500 mt-1 truncate">{{ leg.toStation }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 中转等待时间 -->
                                        {% if not loop.last %}
                                        <div class="text-center py-2">
                                            <div class="inline-flex items-center bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                                                <i class="fas fa-clock mr-2"></i>
                                                {% set wait_time = plan.transportation.major.transferInfo.waitTime %}
                                                {% if wait_time and wait_time|int > 0 %}
                                                    {% set hours = (wait_time|int // 60) %}
                                                    {% set minutes = (wait_time|int % 60) %}
                                                    中转等待:
                                                    {% if hours > 0 %}{{ hours }}小时{% endif %}
                                                    {% if minutes > 0 %}{{ minutes }}分钟{% endif %}
                                                {% else %}
                                                    中转等待: 约30分钟
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <!-- 简化的中转显示（如果没有详细信息） -->
                                    <div class="flex items-center">
                                        <!-- 出发信息 -->
                                        <div class="text-center w-28">
                                            <p class="text-2xl font-bold text-gray-900">{{ plan.transportation.major.departure.time }}</p>
                                            <p class="text-sm text-gray-500 mt-1 truncate">{{ plan.transportation.major.departure.station }}</p>
                                        </div>

                                        <!-- 中转路线 -->
                                        <div class="flex-1 text-center px-4">
                                            <p class="text-xs text-gray-500">{{ plan.transportation.major.duration }}</p>
                                            <div class="w-full h-px bg-orange-300 relative my-1">
                                                <i class="fas fa-circle text-orange-400 text-xs absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1/2"></i>
                                                <i class="fas fa-exchange-alt text-orange-500 text-sm absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-1"></i>
                                                <i class="fas fa-circle text-orange-400 text-xs absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2"></i>
                                            </div>
                                            <p class="text-xs text-orange-600 font-medium">中转</p>
                                        </div>

                                        <!-- 到达信息 -->
                                        <div class="text-center w-28">
                                            <p class="text-2xl font-bold text-gray-900">{{ plan.transportation.major.arrival.time }}</p>
                                            <p class="text-sm text-gray-500 mt-1 truncate">{{ plan.transportation.major.arrival.station }}</p>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- 底部信息 -->
                                    <div class="border-t border-gray-100 mt-4 pt-3 text-sm text-gray-600 flex items-center">
                                        <i class="fas fa-train w-4 mr-2 text-gray-400"></i>
                                        <span>{{ plan.transportation.major.code }}</span>
                                        {% if plan.transportation.major.seat %}
                                        <span class="mx-2 text-gray-300">|</span>
                                        <span>{{ plan.transportation.major.seat.type }}</span>
                                        {% endif %}
                                        <span class="mx-2 text-gray-300">|</span>
                                        <span class="text-orange-600">需要中转</span>
                                    </div>
                                </div>
                                {% else %}
                                <!-- 直达票卡片 -->
                                <div class="bg-white p-5 mt-4 rounded-xl shadow-md border border-gray-100 transform hover:shadow-xl transition-shadow duration-300">
                                    <div class="flex items-center">
                                        <!-- 出发信息 -->
                                        <div class="text-center w-28">
                                            <p class="text-2xl font-bold text-gray-900">{{ plan.transportation.major.departure.time }}</p>
                                            <p class="text-sm text-gray-500 mt-1 truncate">{{ plan.transportation.major.departure.station }}</p>
                                        </div>

                                        <!-- 行程时长和路线 -->
                                        <div class="flex-1 text-center px-4">
                                            <p class="text-xs text-gray-500">{{ plan.transportation.major.duration }}</p>
                                            <div class="w-full h-px bg-gray-200 relative my-1">
                                                <i class="fas fa-circle text-gray-300 text-xs absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1/2"></i>
                                                <i class="fas fa-circle text-gray-300 text-xs absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2"></i>
                                            </div>
                                            <p class="text-xs text-gray-400">直达</p>
                                        </div>

                                        <!-- 到达信息 -->
                                        <div class="text-center w-28">
                                            <p class="text-2xl font-bold text-gray-900">{{ plan.transportation.major.arrival.time }}</p>
                                            <p class="text-sm text-gray-500 mt-1 truncate">{{ plan.transportation.major.arrival.station }}</p>
                                        </div>

                                        <!-- 分隔线 -->
                                        <div class="border-l border-gray-200 h-16 mx-6"></div>

                                        <!-- 价格和预订按钮 -->
                                        <div class="text-right flex-shrink-0">
                                            {% if plan.transportation.major.seat and plan.transportation.major.seat.price %}
                                            <p class="text-xl font-bold text-red-600 mb-2">
                                                <span class="text-sm font-normal">¥</span>{{ plan.transportation.major.seat.price }}
                                            </p>
                                            {% endif %}
                                            <button class="bg-indigo-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105">
                                                预订
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 底部信息 -->
                                    <div class="border-t border-gray-100 mt-4 pt-3 text-sm text-gray-600 flex items-center">
                                        {% if plan.transportation.major.type == 'train' %}
                                        <i class="fas fa-train w-4 mr-2 text-gray-400"></i>
                                        {% else %}
                                        <i class="fas fa-plane w-4 mr-2 text-gray-400"></i>
                                        {% endif %}
                                        <span>{{ plan.transportation.major.code }}</span>
                                        {% if plan.transportation.major.seat %}
                                        <span class="mx-2 text-gray-300">|</span>
                                        <span>{{ plan.transportation.major.seat.type }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}

                                <!-- 推荐理由 -->
                                <div class="mt-4 bg-gray-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                                    <p class="text-sm text-gray-700">
                                        <strong class="font-semibold text-gray-800">推荐理由:</strong>
                                        {% if plan.transportation.major.recommendation_reason %}
                                            {{ plan.transportation.major.recommendation_reason }}
                                        {% else %}
                                            该方案为综合推荐，性价比高，时间合适。
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {% if plan.transportation.local.arrival and plan.timeline %}
                        <!-- 火车站到目的地 -->
                         <div class="flex items-start z-10 relative">
                            <div class="w-12 h-12 rounded-full bg-green-500 flex items-center justify-center mr-6 shadow-lg">
                                {% if plan.transportation.local.arrival.transport_mode == "公交" %}
                                <i class="fas fa-bus text-xl text-white"></i>
                                {% else %}
                                <i class="fas fa-taxi text-xl text-white"></i>
                                {% endif %}
                            </div>
                            <div class="bg-gray-50 p-5 rounded-lg flex-1 shadow-sm">
                                <div class="flex justify-between items-baseline mb-2">
                                    <h3 class="font-bold text-lg text-green-800">
                                        小交通({{ plan.transportation.local.arrival.transport_mode or "打车" }})
                                    </h3>
                                    <span class="text-2xl font-bold text-gray-800">{{ plan.timeline.arrival_at_destination }}</span>
                                </div>
                                <p class="text-gray-600 font-medium text-md mb-3 pb-3 border-b border-gray-200">
                                    {{ plan.transportation.major.arrival.station }} → {{ plan.arrival_place }}
                                </p>

                                <div class="text-sm space-y-2">
                                    {% if plan.transportation.local.arrival.transport_mode == "公交" %}
                                    <p><i class="fas fa-clock w-4 mr-1 text-gray-500"></i><strong>预计用时:</strong> {{ plan.transportation.local.arrival.duration }}</p>
                                    <p><i class="fas fa-money-bill w-4 mr-1 text-gray-500"></i><strong>费用:</strong> {{ plan.transportation.local.arrival.cost_text or ('¥' + (plan.transportation.local.arrival.cost|string) if plan.transportation.local.arrival.cost and plan.transportation.local.arrival.cost > 0 else '具体费用请联系运营商') }}</p>
                                    {% if plan.transportation.local.arrival.walking_distance %}
                                    <p><i class="fas fa-walking w-4 mr-1 text-gray-500"></i><strong>步行距离:</strong> {{ plan.transportation.local.arrival.walking_distance }}米</p>
                                    {% endif %}
                                    {% if plan.transportation.local.arrival.transfer_count is defined %}
                                    <p><i class="fas fa-exchange-alt w-4 mr-1 text-gray-500"></i><strong>换乘次数:</strong> {{ plan.transportation.local.arrival.transfer_count }}次</p>
                                    {% endif %}
                                    {% else %}
                                    <p><i class="fas fa-route w-4 mr-1 text-gray-500"></i><strong>距离:</strong> {{ plan.transportation.local.arrival.distance|int//1000 }}公里{{ plan.transportation.local.arrival.distance|int%1000 }}米</p>
                                    <p><i class="fas fa-clock w-4 mr-1 text-gray-500"></i><strong>预计用时:</strong> {{ plan.transportation.local.arrival.duration }}</p>
                                    <p><i class="fas fa-money-bill w-4 mr-1 text-gray-500"></i><strong>打车费用:</strong> ¥{{ plan.transportation.local.arrival.taxi_cost or 0 }}</p>
                                    {% if plan.transportation.local.arrival.traffic_lights is defined %}
                                    <p><i class="fas fa-traffic-light w-4 mr-1 text-gray-500"></i><strong>红绿灯:</strong> {{ plan.transportation.local.arrival.traffic_lights }}个</p>
                                    {% endif %}
                                    {% endif %}
                                    
                                    <!-- 驾车导航指引 -->
                                    <div class="pt-2">
                                        <button id="arr-steps-btn" class="text-sm text-indigo-600 hover:text-indigo-800 focus:outline-none">
                                            <i class="fas fa-angle-down mr-1"></i> 查看{% if plan.transportation.local.arrival.transport_mode == "公交" %}公交路线{% else %}驾车路线{% endif %}指引
                                        </button>
                                        <div id="arr-steps-container" class="steps-container mt-3 bg-gray-100 p-3 rounded-lg text-sm">
                                            {% for step in plan.transportation.local.arrival.steps %}
                                            <div class="nav-step mb-2 p-2 bg-white rounded border-l-4 {% if step.type == 'transit' %}border-blue-500{% else %}border-green-500{% endif %}">
                                                {% if step.type == 'transit' %}
                                                    <div class="flex items-center mb-1">
                                                        <i class="fas fa-bus text-blue-500 mr-2"></i>
                                                        <p class="font-medium">{{ step.instruction or step.name }}</p>
                                                    </div>
                                                    {% if step.departure_stop and step.arrival_stop %}
                                                    <p class="text-xs text-gray-600 ml-6">{{ step.departure_stop }} → {{ step.arrival_stop }}</p>
                                                    {% endif %}
                                                    {% if step.distance %}
                                                    <p class="text-xs text-gray-500 ml-6">距离: {{ step.distance }}米</p>
                                                    {% endif %}
                                                {% else %}
                                                    <div class="flex items-center">
                                                        
                                                        <p>{{ step.instruction or "步行" }}</p>
                                                    </div>
                                                    {% if step.distance %}
                                                    <p class="text-xs text-gray-500 ml-6">{{ step.distance }}米</p>
                                                    {% endif %}
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </section>
                
                <!-- AI智能思考过程已移除 -->
                
                <!-- 旅行贴士 -->
                {% if plan.tips and plan.tips|length > 0 %}
                <section class="bg-white rounded-xl card-shadow p-6 mt-8">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-2xl font-bold font-serif text-gray-800">
                            <i class="fas fa-lightbulb text-yellow-500 mr-3"></i>
                            旅行贴士
                        </h2>
                        {% if plan.ai_travel_advice %}
                        <button id="show-detailed-advice" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition-colors">
                            <i class="fas fa-expand-alt mr-1"></i>查看详细建议
                        </button>
                        {% endif %}
                    </div>
                    <ul class="space-y-3">
                        {% for tip in plan.tips %}
                        <li class="flex items-start">
                            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-800 mr-3 flex-shrink-0">
                                <i class="fas fa-check text-xs"></i>
                            </span>
                            <span>{{ tip }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </section>
                {% endif %}

                <!-- AI详细旅行建议弹窗 -->
                {% if plan.ai_travel_advice %}
                <div id="detailed-advice-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-2 sm:p-4">
                    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
                        <!-- 弹窗头部 -->
                        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-3 sm:p-4 md:p-6 rounded-t-2xl flex-shrink-0">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <h2 class="text-lg sm:text-xl md:text-2xl font-bold mb-1 sm:mb-2 truncate">🎯 AI专属旅行建议</h2>
                                    <p class="text-indigo-100 text-xs sm:text-sm md:text-base">为您量身定制的详细出行指南</p>
                                </div>
                                <button id="close-advice-modal" class="text-white hover:text-gray-200 transition-colors ml-4 flex-shrink-0">
                                    <i class="fas fa-times text-lg sm:text-xl md:text-2xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 弹窗内容 -->
                        <div class="flex-1 overflow-y-auto p-3 sm:p-4 md:p-6">
                            <div class="space-y-4 sm:space-y-6 md:space-y-8">
                                {% if plan.ai_travel_advice.sections.timing_and_risks %}
                                <div class="bg-red-50 border-l-4 border-red-400 p-3 sm:p-4 md:p-6 rounded-r-lg">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-clock text-red-400 text-lg sm:text-xl"></i>
                                        </div>
                                        <div class="ml-3 sm:ml-4 w-full min-w-0">
                                            <h3 class="text-base sm:text-lg font-semibold text-red-800 mb-2 sm:mb-3">出发时间与风险提示</h3>
                                            <div class="formatted-content text-gray-700 leading-relaxed text-sm sm:text-base">
                                                {{ plan.ai_travel_advice.sections.timing_and_risks|replace('【出发时间与风险提示】', '')|safe }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if plan.ai_travel_advice.sections.packing_list %}
                                <div class="bg-blue-50 border-l-4 border-blue-400 p-3 sm:p-4 md:p-6 rounded-r-lg">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-suitcase text-blue-400 text-lg sm:text-xl"></i>
                                        </div>
                                        <div class="ml-3 sm:ml-4 w-full min-w-0">
                                            <h3 class="text-base sm:text-lg font-semibold text-blue-800 mb-2 sm:mb-3">"万无一失"物品清单</h3>
                                            <div class="formatted-content text-gray-700 leading-relaxed text-sm sm:text-base">
                                                {{ plan.ai_travel_advice.sections.packing_list|replace('【"万无一失"物品清单】', '')|safe }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if plan.ai_travel_advice.sections.destination_tips %}
                                <div class="bg-green-50 border-l-4 border-green-400 p-3 sm:p-4 md:p-6 rounded-r-lg">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-map-marker-alt text-green-400 text-lg sm:text-xl"></i>
                                        </div>
                                        <div class="ml-3 sm:ml-4 w-full min-w-0">
                                            <h3 class="text-base sm:text-lg font-semibold text-green-800 mb-2 sm:mb-3">目的地小贴士</h3>
                                            <div class="formatted-content text-gray-700 leading-relaxed text-sm sm:text-base">
                                                {{ plan.ai_travel_advice.sections.destination_tips|replace('【目的地小贴士】', '')|safe }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 弹窗底部 -->
                        <div class="bg-gray-50 px-3 sm:px-4 md:px-6 py-3 sm:py-4 rounded-b-2xl flex-shrink-0">
                            <div class="flex justify-center sm:justify-end">
                                <button id="close-advice-modal-btn" class="w-full sm:w-auto bg-indigo-600 text-white px-6 py-2.5 rounded-lg hover:bg-indigo-700 transition-colors font-medium">
                                    <i class="fas fa-check mr-2"></i>知道了
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

            </main>
        </div>
        
        <!-- 返回按钮 -->
        <div class="text-center mt-12">
            <a href="/" class="text-indigo-600 hover:text-indigo-800 font-semibold transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>返回重新规划
            </a>
        </div>
    </div>

    <!-- Leaflet.js & AntPath Plugin -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://unpkg.com/leaflet-ant-path@1.3.0/dist/leaflet-ant-path.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            let mapData = {};
            let mapInitialized = false;

            // 检查地图容器是否存在
            const mapContainer = document.getElementById('map');
            if (!mapContainer) {
                console.error('地图容器不存在！');
                return;
            }
            console.log('地图容器已找到:', mapContainer);
            
            try {
                // 安全解析地图数据，添加更好的错误处理
                try {
                    const mapDataJson = '{{ plan.map_data | tojson | safe }}';
                    mapData = JSON.parse(mapDataJson);
                    console.log('成功解析地图数据:', mapData);
                } catch (parseError) {
                    console.error('地图数据解析错误:', parseError);
                    mapData = {}; // 重置为空对象以避免后续错误
                    document.getElementById('map').innerHTML = '<div class="p-4 bg-yellow-100 text-yellow-700 rounded">地图数据解析失败</div>';
                    return; // 停止执行
                }
                
                if (!mapData || Object.keys(mapData).length === 0) {
                    console.warn('地图数据为空或无效');
                    document.getElementById('map').innerHTML = '<div class="p-4 bg-yellow-100 text-yellow-700 rounded">暂无地图数据可显示</div>';
                    return;
                }
            
            // 1. 初始化地图
            console.log('正在初始化地图...');
            const map = L.map('map').setView([35, 110], 4); // 默认中国中心
            mapInitialized = true;
            console.log('地图初始化成功');

            // 2. 添加地图瓦片图层 (使用OpenStreetMap)
            console.log('正在加载地图瓦片...');
            const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            });

            tileLayer.on('load', function() {
                console.log('地图瓦片加载成功');
            });

            tileLayer.on('tileerror', function(e) {
                console.error('地图瓦片加载失败:', e);
            });

            tileLayer.addTo(map);

                // 3. 准备图标
                const mainStationIcon = L.divIcon({
                className: 'pulse-marker',
                html: '<div style="background-color: #3b82f6; width: 100%; height: 100%; border-radius: 50%;"></div>',
                iconSize: [12, 12],
                iconAnchor: [6, 6]
            });
                
                const secondaryStationIcon = L.divIcon({
                className: '', // 无动画
                html: '<div style="background-color: #10b981; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>',
                iconSize: [12, 12],
                iconAnchor: [6, 6]
            });
                
                const locationIcon = L.divIcon({
                    className: '',
                    html: '<div style="background-color: #f97316; width: 10px; height: 10px; border-radius: 50%; border: 2px solid white;"></div>',
                    iconSize: [14, 14],
                    iconAnchor: [7, 7]
                });
                
                // 4. 验证坐标有效性的函数
                function isValidCoords(coords) {
                    if (!coords || !Array.isArray(coords) || coords.length !== 2) {
                        console.error('无效的坐标格式:', coords);
                        return false;
                    }
                    
                    const [lat, lng] = coords;
                    // 中国大陆范围检查
                    if (isNaN(lat) || isNaN(lng) || 
                        lat < 18 || lat > 53 || 
                        lng < 73 || lng > 135) {
                        console.error('坐标超出中国大陆范围:', coords);
                        return false;
                    }
                    
                    return true;
                }
                
                // 5. 绘制点和线路
                const allPoints = [];
                const paths = [];
                
                // 如果有精确坐标（小交通方案）
                if (mapData.dep_point && mapData.arr_point) {
                    console.log('渲染完整行程路线（包含小交通）');
                    
                    try {
                        // 添加出发地标记
                        const depPoint = mapData.dep_point.coords;
                        if (isValidCoords(depPoint)) {
                            L.marker(depPoint, { icon: locationIcon }).addTo(map)
                                .bindPopup(`<b>出发地:</b> ${mapData.dep_point.name}`);
                            allPoints.push(depPoint);
                            console.log('添加出发地点:', depPoint);
                        }
                        
                        // 添加出发站标记
                        const depStation = mapData.dep_station.coords;
                        if (isValidCoords(depStation)) {
                            L.marker(depStation, { icon: mainStationIcon }).addTo(map)
                                .bindPopup(`<b>出发站:</b> ${mapData.dep_station.name}`);
                            allPoints.push(depStation);
                            console.log('添加出发站点:', depStation);
                        }
                        
                        // 添加到达站标记
                        const arrStation = mapData.arr_station.coords;
                        if (isValidCoords(arrStation)) {
                            L.marker(arrStation, { icon: secondaryStationIcon }).addTo(map)
                                .bindPopup(`<b>到达站:</b> ${mapData.arr_station.name}`);
                            allPoints.push(arrStation);
                            console.log('添加到达站点:', arrStation);
                        }
                        
                        // 添加目的地标记
                        const arrPoint = mapData.arr_point.coords;
                        if (isValidCoords(arrPoint)) {
                            L.marker(arrPoint, { icon: locationIcon }).addTo(map)
                                .bindPopup(`<b>目的地:</b> ${mapData.arr_point.name}`);
                            allPoints.push(arrPoint);
                            console.log('添加目的地点:', arrPoint);
                        }
                        
                        // 出发地到出发站的路线
                        if (isValidCoords(depPoint) && isValidCoords(depStation)) {
                            const depLocalPath = L.polyline.antPath([depPoint, depStation], {
                                delay: 800,
                                dashArray: [10, 20],
                                weight: 3,
                                color: '#f97316', // 橙色
                                pulseColor: '#e5e7eb',
                                paused: false,
                                reverse: false,
                                hardwareAccelerated: true
                            }).addTo(map);
                            console.log('添加出发地到出发站路线');
                        }
                        
                        // 高铁路线
                        if (isValidCoords(depStation) && isValidCoords(arrStation)) {
                            const trainPath = L.polyline.antPath([depStation, arrStation], {
                                delay: 800,
                                dashArray: [10, 20],
                                weight: 5,
                                color: '#4f46e5', // 蓝紫色
                                pulseColor: '#e5e7eb',
                                paused: false,
                                reverse: false,
                                hardwareAccelerated: true
                            }).addTo(map);
                            console.log('添加高铁路线');
                        }
                        
                        // 到达站到目的地的路线
                        if (isValidCoords(arrStation) && isValidCoords(arrPoint)) {
                            const arrLocalPath = L.polyline.antPath([arrStation, arrPoint], {
                                delay: 800,
                                dashArray: [10, 20],
                                weight: 3,
                                color: '#10b981', // 绿色
                                pulseColor: '#e5e7eb',
                                paused: false,
                                reverse: false,
                                hardwareAccelerated: true
                            }).addTo(map);
                            console.log('添加到达站到目的地路线');
                        }
                    } catch (error) {
                        console.error('渲染完整行程路线出错:', error);
                    }
                } else {
                    // 仅显示大交通路线（出发站到到达站）
                    console.log('仅渲染大交通路线');
                    try {
                        const depCoords = mapData.dep.coords;
                        const arrCoords = mapData.arr.coords;
                        
                        console.log('出发站坐标:', depCoords);
                        console.log('到达站坐标:', arrCoords);

            // 添加标记
                        if (isValidCoords(depCoords)) {
                            L.marker(depCoords, { icon: mainStationIcon }).addTo(map)
                                .bindPopup(`<b>出发:</b> ${mapData.dep.name}`);
                            allPoints.push(depCoords);
                        }
                        
                        if (isValidCoords(arrCoords)) {
                            L.marker(arrCoords, { icon: secondaryStationIcon }).addTo(map)
                                .bindPopup(`<b>到达:</b> ${mapData.arr.name}`);
                            allPoints.push(arrCoords);
                        }
            
                        // 绘制流动轨迹线
                        if (isValidCoords(depCoords) && isValidCoords(arrCoords)) {
                            const trainPath = L.polyline.antPath([depCoords, arrCoords], {
                                delay: 800,
                                dashArray: [10, 20],
                                weight: 5,
                                color: '#4f46e5',
                                pulseColor: '#e5e7eb',
                                paused: false,
                                reverse: false,
                                hardwareAccelerated: true
            }).addTo(map);
                        }
                    } catch (error) {
                        console.error('渲染大交通路线出错:', error);
                    }
                }

                // 6. 调整地图视野
                if (allPoints.length > 0) {
                    console.log('设置地图视野范围，包含所有点:', allPoints);
                    try {
                        map.fitBounds(L.latLngBounds(allPoints), { padding: [50, 50] });
                    } catch (error) {
                        console.error('设置地图视野出错:', error);
                        // 如果出错，设置一个默认视图
                        map.setView([35, 110], 4);
                    }
                }
            
                // 7. 为地图添加箭头标记
                try {
            const svgContainer = document.createElementNS("http://www.w3.org/2000/svg", "svg");
            svgContainer.setAttribute("width", "0");
            svgContainer.setAttribute("height", "0");
            svgContainer.style.position = "absolute";
            document.body.appendChild(svgContainer);
            
            const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
            svgContainer.appendChild(defs);
            
            const marker = document.createElementNS("http://www.w3.org/2000/svg", "marker");
            marker.setAttribute("id", "arrow");
            marker.setAttribute("viewBox", "0 0 10 10");
            marker.setAttribute("refX", "5");
            marker.setAttribute("refY", "5");
            marker.setAttribute("markerWidth", "6");
            marker.setAttribute("markerHeight", "6");
            marker.setAttribute("orient", "auto-start-reverse");
            defs.appendChild(marker);
            
            const arrow = document.createElementNS("http://www.w3.org/2000/svg", "path");
            arrow.setAttribute("d", "M 0 0 L 10 5 L 0 10 z");
            arrow.setAttribute("fill", "#4f46e5");
            marker.appendChild(arrow);
                } catch (error) {
                    console.error('添加箭头标记出错:', error);
                }
                
                // 8. 处理驾车路线指引的展开/折叠
                const depStepsBtn = document.getElementById('dep-steps-btn');
                const depStepsContainer = document.getElementById('dep-steps-container');
                const arrStepsBtn = document.getElementById('arr-steps-btn');
                const arrStepsContainer = document.getElementById('arr-steps-container');
                
                console.log('路线指引按钮:', depStepsBtn, arrStepsBtn);
                console.log('路线指引容器:', depStepsContainer, arrStepsContainer);
                
                // 添加点击事件并确保元素存在
                function setupStepsToggle(btn, container) {
                    if (btn && container) {
                        btn.addEventListener('click', function() {
                            console.log('点击路线指引按钮:', btn.id);
                            container.classList.toggle('open');
                            
                            const icon = btn.querySelector('i');
                            if (icon) {
                                icon.classList.toggle('fa-angle-down');
                                icon.classList.toggle('fa-angle-up');
                            }
                            
                            // 如果展开，滚动到可见区域
                            if (container.classList.contains('open')) {
                                setTimeout(() => {
                                    btn.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                }, 300);
                            }
                        });
                    }
                }
                
                // 设置两个路线指引的点击事件
                setupStepsToggle(depStepsBtn, depStepsContainer);
                setupStepsToggle(arrStepsBtn, arrStepsContainer);
            } catch (error) {
                console.error('地图初始化失败:', error);
                
                // 只有在地图未初始化的情况下才显示错误，避免覆盖之前的错误消息
                if (!mapInitialized) {
                    document.getElementById('map').innerHTML = '<div class="p-4 bg-red-100 text-red-700 rounded">地图加载失败，请刷新页面重试。</div>';
                }
                
                // 显示详细错误信息以便调试
                console.debug('完整的错误信息:', error.message, error.stack);
            }
            
            // 在页面加载后为地图添加刷新按钮
            setTimeout(() => {
                const mapEl = document.getElementById('map');
                const refreshBtn = document.createElement('button');
                refreshBtn.className = 'absolute top-4 right-4 bg-white p-2 rounded-md shadow-md z-50';
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
                refreshBtn.title = '刷新地图';
                refreshBtn.onclick = function() {
                    window.location.reload();
                };
                mapEl.style.position = 'relative';
                mapEl.appendChild(refreshBtn);
            }, 1000);

            // AI详细建议弹窗控制
            const showAdviceBtn = document.getElementById('show-detailed-advice');
            const adviceModal = document.getElementById('detailed-advice-modal');
            const closeAdviceBtn = document.getElementById('close-advice-modal');
            const closeAdviceModalBtn = document.getElementById('close-advice-modal-btn');

            if (showAdviceBtn && adviceModal) {
                // 显示弹窗
                showAdviceBtn.addEventListener('click', function() {
                    adviceModal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden'; // 防止背景滚动

                    // 格式化AI建议内容
                    formatAdviceContent();
                });

                // 隐藏弹窗的函数
                function hideAdviceModal() {
                    adviceModal.classList.add('hidden');
                    document.body.style.overflow = 'auto'; // 恢复滚动
                }

                // 点击关闭按钮
                if (closeAdviceBtn) {
                    closeAdviceBtn.addEventListener('click', hideAdviceModal);
                }

                if (closeAdviceModalBtn) {
                    closeAdviceModalBtn.addEventListener('click', hideAdviceModal);
                }

                // 点击背景关闭弹窗
                adviceModal.addEventListener('click', function(e) {
                    if (e.target === adviceModal) {
                        hideAdviceModal();
                    }
                });

                // ESC键关闭弹窗
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !adviceModal.classList.contains('hidden')) {
                        hideAdviceModal();
                    }
                });
            }

            // 格式化AI建议内容
            function formatAdviceContent() {
                const contentElements = document.querySelectorAll('.formatted-content');
                contentElements.forEach(element => {
                    let content = element.textContent || element.innerText;

                    // 分割成行
                    let lines = content.split('\n').map(line => line.trim()).filter(line => line);
                    let formattedLines = [];
                    let inList = false;

                    lines.forEach(line => {
                        // 检查是否是列表项
                        if (line.match(/^[-•*]\s+/)) {
                            if (!inList) {
                                formattedLines.push('<ul>');
                                inList = true;
                            }
                            let listContent = line.replace(/^[-•*]\s+/, '');
                            formattedLines.push(`<li>${listContent}</li>`);
                        } else {
                            // 如果之前在列表中，现在不是列表项，关闭列表
                            if (inList) {
                                formattedLines.push('</ul>');
                                inList = false;
                            }

                            // 检查是否是标题（以冒号结尾）
                            if (line.endsWith(':') && line.length < 50) {
                                formattedLines.push(`<h4>${line}</h4>`);
                            } else if (line.length > 0) {
                                // 普通段落
                                formattedLines.push(`<p>${line}</p>`);
                            }
                        }
                    });

                    // 如果最后还在列表中，关闭列表
                    if (inList) {
                        formattedLines.push('</ul>');
                    }

                    element.innerHTML = formattedLines.join('');
                });
            }
        });
    </script>
</body>
</html> 