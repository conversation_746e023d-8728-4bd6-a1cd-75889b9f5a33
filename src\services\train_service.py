from typing import Dict, Any

def analyze_train_data(data: Dict[str, Any], criteria: str) -> Dict[str, Any]:
    """
    分析火车数据，根据给定标准找到最佳选项。
    """
    print(f"[服务] 🔍 正在分析火车数据以查找 {criteria} 的高铁选项...")

    if "error" in data:
        print(f"[服务] ❌ 无法分析数据: {data['error']}")
        return {"error": data["error"]}

    if not data or not data.get('data', {}).get('trains'):
        print(f"[服务] ❌ 未找到符合条件的火车。")
        print(f"[服务] 🔍 数据结构: {data}")
        return {"error": "未找到符合条件的火车。"}

    trains = data['data']['trains']
    print(f"[服务] ℹ️ 共找到 {len(trains)} 趟火车。")

    # 筛选高铁（包括中转方案）
    high_speed_trains = []
    for train in trains:
        train_code = train.get('trainCode', '')
        # 传统高铁：G、D、C开头
        # 中转方案：包含"中转"关键字或以高铁车次开头
        if train_code and (train_code[0] in ['G', 'D', 'C'] or '中转' in train_code or train.get('isTransfer', False)):
            high_speed_trains.append(train)

    print(f"[服务] ℹ️ 找到 {len(high_speed_trains)} 趟高铁（含中转）。")

    if not high_speed_trains:
        print("[服务] ❌ 未找到符合条件的高铁。")
        return {"error": "未找到符合条件的高铁。"}

    best_train = None
    best_seat = None

    if criteria == "最便宜":
        best_price = float('inf')
        for train in high_speed_trains:
            for seat in train.get('trainAvs', []):
                price = seat.get('price')
                if price and float(price) < best_price:
                    best_price = float(price)
                    best_train = train
                    best_seat = seat
        print(f"[服务] 🔍 筛选条件: 最便宜的票价")

    elif criteria == "最贵":
        best_price = -1
        for train in high_speed_trains:
            for seat in train.get('trainAvs', []):
                price = seat.get('price')
                if price and float(price) > best_price:
                    best_price = float(price)
                    best_train = train
                    best_seat = seat
        print(f"[服务] 🔍 筛选条件: 最贵的票价")

    elif criteria == "最快":
        best_duration = float('inf')
        for train in high_speed_trains:
            duration_str = train.get('runTime', '')
            minutes = 0
            if '小时' in duration_str and '分钟' in duration_str:
                hours, minutes_part = duration_str.split('小时')
                minutes = int(hours) * 60 + int(minutes_part.replace('分钟', ''))
            elif '小时' in duration_str:
                hours = duration_str.replace('小时', '')
                minutes = int(hours) * 60
            elif '分钟' in duration_str:
                minutes = int(duration_str.replace('分钟', ''))

            if minutes < best_duration:
                best_duration = minutes
                best_train = train
                best_seat = min(train.get('trainAvs', []), key=lambda x: float(x.get('price', float('inf'))))
        print(f"[服务] 🔍 筛选条件: 最短时长")

    elif criteria == "综合最优":
        best_score = float('-inf')
        for train in high_speed_trains:
            duration_str = train.get('runTime', '')
            minutes = 0
            if '小时' in duration_str and '分钟' in duration_str:
                hours, minutes_part = duration_str.split('小时')
                minutes = int(hours) * 60 + int(minutes_part.replace('分钟', ''))
            elif '小时' in duration_str:
                hours = duration_str.replace('小时', '')
                minutes = int(hours) * 60
            elif '分钟' in duration_str:
                minutes = int(duration_str.replace('分钟', ''))

            for seat in train.get('trainAvs', []):
                price = float(seat.get('price', float('inf')))
                if price == float('inf'):
                    continue

                price_score = 1 - (price / 2000)
                time_score = 1 - (minutes / 600)

                score = price_score * 0.6 + time_score * 0.4

                if score > best_score:
                    best_score = score
                    best_train = train
                    best_seat = seat
        print(f"[服务] 🔍 筛选条件: 综合考虑价格和时间")

    else:
        best_price = float('inf')
        for train in high_speed_trains:
            for seat in train.get('trainAvs', []):
                price = seat.get('price')
                if price and float(price) < best_price:
                    best_price = float(price)
                    best_train = train
                    best_seat = seat
        print(f"[服务] 🔍 未识别的筛选条件，默认为: 最便宜的票价")

    if not best_train or not best_seat:
        print(f"[服务] ❌ 未能为 '{criteria}' 标准找到合适的选项。")
        return {"error": f"未能为 '{criteria}' 标准找到合适的选项。"}

    seat_mapping = {"9": "商务座", "M": "一等座", "O": "二等座", "4": "软卧", "3": "硬卧", "1": "硬座", "W": "无座"}
    seat_type = seat_mapping.get(best_seat.get('seatClassCode', ''), '未知座位')

    # 检查是否为中转方案
    is_transfer = best_train.get('isTransfer', False)

    result = {
        "criteria": criteria,
        "train_code": best_train.get('trainCode'),
        "departure": {
            "station": best_train.get('deptStation') or best_train.get('depStationName'),
            "time": best_train.get('deptTime') or best_train.get('depTime'),
            "date": best_train.get('deptDate') or best_train.get('depDate')
        },
        "arrival": {
            "station": best_train.get('arrStation') or best_train.get('arrStationName'),
            "time": best_train.get('arrTime')
        },
        "duration": best_train.get('runTime'),
        "seat": {
            "type": best_seat.get('seatType') or seat_type,
            "price": best_seat.get('price'),
            "available": best_seat.get('num') or best_seat.get('ticketStatus', '有票')
        },
        "other_trains_count": len(high_speed_trains) - 1,
        "isTransfer": is_transfer
    }

    # 如果是中转方案，添加中转信息
    if is_transfer and 'transferInfo' in best_train:
        result["transferInfo"] = best_train['transferInfo']
        print(f"[服务] ✅ 找到 {criteria} 中转方案: {result['train_code']}, 价格: ¥{result['seat']['price']}, 时长: {result['duration']}")
        print(f"[服务] 🔄 中转路线: {best_train['transferInfo'].get('route', '未知')}")
    else:
        print(f"[服务] ✅ 找到 {criteria} 直达火车: {result['train_code']}, 价格: ¥{result['seat']['price']}, 时长: {result['duration']}")

    return result
