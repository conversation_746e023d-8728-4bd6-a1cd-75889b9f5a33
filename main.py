import argparse
import uvicorn
import os
import multiprocessing
from dotenv import load_dotenv

# 从 .env 文件加载环境变量
load_dotenv()

def main():
    parser = argparse.ArgumentParser(description="智能旅行规划助手")
    parser.add_argument(
        "mode",
        choices=["cli", "web", "langgraph"],
        nargs="?",
        default="langgraph",
        help="应用程序的运行模式: 'cli' 表示命令行，'web' 表示网页界面，'langgraph' 表示LangGraph多智能体版本。",
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="Web模式下的工作进程数量 (默认为CPU核心数的一半)"
    )
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error", "critical"],
        default="info",
        help="日志级别 (默认为 info)"
    )
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Web服务器监听地址 (默认为 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Web服务器端口 (默认为 8000)"
    )
    args = parser.parse_args()

    if args.mode == "web":
        print("=" * 60)
        print("🧠 正在启动智能旅行规划助手网页应用")
        print("=" * 60)
        print("✓ 基于 FastAPI 的现代化网页界面")
        print("✓ 多种规划策略：最便宜、最快、最贵、综合最优")
        print("✓ 展示智能思考过程和行程规划结果")
        print("=" * 60)
        print("📋 启动信息:")
        print(f"   - 访问地址: http://{args.host if args.host != '0.0.0.0' else 'localhost'}:{args.port}")
        print("   - 按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 确定工作进程数量，默认为CPU核心数的一半（至少为1）
        if args.workers is None:
            args.workers = max(1, multiprocessing.cpu_count() // 2)
        
        # 使用uvicorn服务器启动，配置优化参数
        uvicorn.run(
            "src.web.app:app", 
            host=args.host, 
            port=args.port, 
            reload=True,
            workers=args.workers if not os.environ.get("DEBUG") else 1,  # 在DEBUG模式下仅使用1个工作进程
            log_level=args.log_level,
            proxy_headers=True,  # 支持代理头部
            forwarded_allow_ips="*",  # 允许所有转发IP
            access_log=True,  # 启用访问日志
        )
    elif args.mode == "langgraph":
        print("=" * 60)
        print("🧠 正在启动LangGraph多智能体版本的旅行规划助手")
        print("=" * 60)
        print("✓ 基于LangGraph的多智能体协作系统")
        print("✓ 专家智能体: 规划师、大交通专家、小交通专家、网络搜索专家")
        print("✓ 支持任意地点到任意地点的完整路线规划")
        print("✓ 包含完整时间线和驾车路线")
        print("=" * 60)
        # 启动LangGraph CLI版本
        from src.main_cli_langgraph import run_agent
        run_agent()
    else:
        # 为了避免循环导入并保持主脚本的整洁，
        # 我们推迟导入 CLI 运行器。
        from src.main_cli import run_agent
        run_agent()

if __name__ == "__main__":
    main()
