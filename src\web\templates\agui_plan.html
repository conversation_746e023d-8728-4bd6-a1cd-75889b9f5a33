<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AG-UI 智能行程规划 - 实时流式体验</title>
    
    <!-- 外部资源 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        .event-stream {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }
        
        .event-item {
            animation: slideIn 0.3s ease-out;
            border-left: 3px solid;
            padding-left: 12px;
            margin-bottom: 8px;
        }
        
        .event-run { border-left-color: #10b981; }
        .event-step { border-left-color: #3b82f6; }
        .event-message { border-left-color: #8b5cf6; }
        .event-tool { border-left-color: #f59e0b; }
        .event-state { border-left-color: #ef4444; }
        .event-error { border-left-color: #dc2626; }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .thinking-bubble {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
            padding: 12px;
            margin: 8px 0;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .tool-call-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #f59e0b;
            border-radius: 50%;
            animation: blink 1s infinite;
            margin-right: 8px;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #10b981; animation: pulse 1s infinite; }
        .status-completed { background: #6b7280; }
        .status-error { background: #ef4444; }
    </style>
</head>
<body>
    <div class="min-h-screen p-6">
        <div class="max-w-6xl mx-auto">
            <!-- 标题 -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-2">AG-UI 智能行程规划</h1>
                <p class="text-white opacity-80">实时流式AI规划体验</p>
            </div>
            
            <!-- 输入区域 -->
            <div class="glass-card p-6 mb-6">
                <div class="flex gap-4">
                    <input 
                        type="text" 
                        id="queryInput" 
                        placeholder="请输入您的行程需求，例如：我想从北京到上海旅行"
                        class="flex-1 px-4 py-3 rounded-lg bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 border border-white border-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                    >
                    <button 
                        id="planButton"
                        class="px-6 py-3 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200 font-medium"
                    >
                        <i class="fas fa-paper-plane mr-2"></i>开始规划
                    </button>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 事件流显示 -->
                <div class="glass-card p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">
                        <i class="fas fa-stream mr-2"></i>实时事件流
                    </h2>
                    <div id="eventStream" class="event-stream text-white text-sm">
                        <div class="text-white opacity-60 text-center py-8">
                            等待开始规划...
                        </div>
                    </div>
                </div>
                
                <!-- 智能体状态 -->
                <div class="glass-card p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">
                        <i class="fas fa-robot mr-2"></i>智能体状态
                    </h2>
                    <div id="agentStatus" class="space-y-3">
                        <div class="agent-item" data-agent="planner">
                            <span class="status-indicator status-completed"></span>
                            <span class="text-white">规划师</span>
                            <span class="text-white opacity-60 ml-2">待机</span>
                        </div>
                        <div class="agent-item" data-agent="major_transport">
                            <span class="status-indicator status-completed"></span>
                            <span class="text-white">大交通专家</span>
                            <span class="text-white opacity-60 ml-2">待机</span>
                        </div>
                        <div class="agent-item" data-agent="local_transport">
                            <span class="status-indicator status-completed"></span>
                            <span class="text-white">小交通专家</span>
                            <span class="text-white opacity-60 ml-2">待机</span>
                        </div>
                        <div class="agent-item" data-agent="synthesis">
                            <span class="status-indicator status-completed"></span>
                            <span class="text-white">综合报告专家</span>
                            <span class="text-white opacity-60 ml-2">待机</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 工具调用显示 -->
            <div class="glass-card p-6 mt-6">
                <h2 class="text-xl font-semibold text-white mb-4">
                    <i class="fas fa-tools mr-2"></i>工具调用
                </h2>
                <div id="toolCalls" class="text-white text-sm">
                    <div class="text-white opacity-60 text-center py-4">
                        暂无工具调用
                    </div>
                </div>
            </div>
            
            <!-- 最终结果 -->
            <div id="finalResult" class="glass-card p-6 mt-6" style="display: none;">
                <h2 class="text-xl font-semibold text-white mb-4">
                    <i class="fas fa-check-circle mr-2"></i>规划结果
                </h2>
                <div id="resultContent" class="text-white">
                    <!-- 结果内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        class AGUIEventHandler {
            constructor() {
                this.eventSource = null;
                this.currentMessages = new Map();
                this.currentToolCalls = new Map();
                this.agentStates = new Map();
                this.setupEventHandlers();
            }
            
            setupEventHandlers() {
                document.getElementById('planButton').addEventListener('click', () => {
                    this.startPlanning();
                });
                
                document.getElementById('queryInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.startPlanning();
                    }
                });
            }
            
            async startPlanning() {
                const query = document.getElementById('queryInput').value.trim();
                if (!query) {
                    alert('请输入行程需求');
                    return;
                }
                
                // 重置界面
                this.resetUI();
                
                // 禁用按钮
                const button = document.getElementById('planButton');
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>规划中...';
                
                try {
                    // 建立SSE连接
                    const response = await fetch('/plan_agui', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'text/event-stream'
                        },
                        body: JSON.stringify({ query: query })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    // 处理事件流
                    this.handleEventStream(response);
                    
                } catch (error) {
                    console.error('规划失败:', error);
                    this.addEvent('error', `规划失败: ${error.message}`);
                } finally {
                    // 恢复按钮
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>开始规划';
                }
            }
            
            async handleEventStream(response) {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const eventData = JSON.parse(line.slice(6));
                                    this.handleEvent(eventData);
                                } catch (e) {
                                    console.warn('解析事件失败:', e, line);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('读取事件流失败:', error);
                    this.addEvent('error', `事件流错误: ${error.message}`);
                }
            }
            
            handleEvent(event) {
                console.log('收到事件:', event);
                
                switch (event.type) {
                    case 'RUN_STARTED':
                        this.handleRunStarted(event);
                        break;
                    case 'RUN_FINISHED':
                        this.handleRunFinished(event);
                        break;
                    case 'RUN_ERROR':
                        this.handleRunError(event);
                        break;
                    case 'STEP_STARTED':
                        this.handleStepStarted(event);
                        break;
                    case 'STEP_FINISHED':
                        this.handleStepFinished(event);
                        break;
                    case 'TEXT_MESSAGE_START':
                        this.handleTextMessageStart(event);
                        break;
                    case 'TEXT_MESSAGE_CONTENT':
                        this.handleTextMessageContent(event);
                        break;
                    case 'TEXT_MESSAGE_END':
                        this.handleTextMessageEnd(event);
                        break;
                    case 'TOOL_CALL_START':
                        this.handleToolCallStart(event);
                        break;
                    case 'TOOL_CALL_ARGS':
                        this.handleToolCallArgs(event);
                        break;
                    case 'TOOL_CALL_END':
                        this.handleToolCallEnd(event);
                        break;
                    case 'TOOL_CALL_RESULT':
                        this.handleToolCallResult(event);
                        break;
                    case 'STATE_DELTA':
                        this.handleStateDelta(event);
                        break;
                    case 'MESSAGES_SNAPSHOT':
                        this.handleMessagesSnapshot(event);
                        break;
                    default:
                        console.log('未处理的事件类型:', event.type);
                }
            }
            
            handleRunStarted(event) {
                this.addEvent('run', `开始规划 (Run ID: ${event.runId})`);
            }
            
            handleRunFinished(event) {
                this.addEvent('run', '规划完成');
                document.getElementById('finalResult').style.display = 'block';
            }
            
            handleRunError(event) {
                this.addEvent('error', `规划错误: ${event.message}`);
            }
            
            handleStepStarted(event) {
                this.addEvent('step', `开始执行: ${this.getAgentDisplayName(event.stepName)}`);
                this.updateAgentStatus(event.stepName, 'running');
            }
            
            handleStepFinished(event) {
                this.addEvent('step', `完成执行: ${this.getAgentDisplayName(event.stepName)}`);
                this.updateAgentStatus(event.stepName, 'completed');
            }
            
            handleTextMessageStart(event) {
                this.currentMessages.set(event.messageId, {
                    content: '',
                    element: null
                });
            }
            
            handleTextMessageContent(event) {
                const message = this.currentMessages.get(event.messageId);
                if (message) {
                    message.content += event.delta;
                    this.updateMessageDisplay(event.messageId, message.content);
                }
            }
            
            handleTextMessageEnd(event) {
                const message = this.currentMessages.get(event.messageId);
                if (message && message.element) {
                    message.element.classList.remove('thinking-bubble');
                }
            }
            
            handleToolCallStart(event) {
                this.currentToolCalls.set(event.toolCallId, {
                    name: event.toolCallName,
                    args: '',
                    result: null,
                    element: null
                });
                this.addToolCall(event.toolCallId, event.toolCallName);
            }
            
            handleToolCallArgs(event) {
                const toolCall = this.currentToolCalls.get(event.toolCallId);
                if (toolCall) {
                    toolCall.args += event.delta;
                    this.updateToolCallArgs(event.toolCallId, toolCall.args);
                }
            }
            
            handleToolCallEnd(event) {
                // 工具调用参数传输完成
            }
            
            handleToolCallResult(event) {
                const toolCall = this.currentToolCalls.get(event.toolCallId);
                if (toolCall) {
                    toolCall.result = event.content;
                    this.updateToolCallResult(event.toolCallId, event.content);
                }
            }
            
            handleStateDelta(event) {
                this.addEvent('state', `状态更新: ${event.delta.length} 个变更`);
            }
            
            handleMessagesSnapshot(event) {
                this.addEvent('message', `消息快照: ${event.messages.length} 条消息`);
            }
            
            // UI 更新方法
            addEvent(type, content) {
                const eventStream = document.getElementById('eventStream');
                const eventItem = document.createElement('div');
                eventItem.className = `event-item event-${type}`;
                eventItem.innerHTML = `
                    <span class="opacity-60">[${new Date().toLocaleTimeString()}]</span>
                    <span class="ml-2">${content}</span>
                `;
                eventStream.appendChild(eventItem);
                eventStream.scrollTop = eventStream.scrollHeight;
            }
            
            updateMessageDisplay(messageId, content) {
                const message = this.currentMessages.get(messageId);
                if (!message.element) {
                    message.element = document.createElement('div');
                    message.element.className = 'thinking-bubble';
                    document.getElementById('eventStream').appendChild(message.element);
                }
                message.element.textContent = content;
                document.getElementById('eventStream').scrollTop = document.getElementById('eventStream').scrollHeight;
            }
            
            addToolCall(toolCallId, toolName) {
                const toolCalls = document.getElementById('toolCalls');
                if (toolCalls.children[0]?.textContent.includes('暂无工具调用')) {
                    toolCalls.innerHTML = '';
                }
                
                const toolElement = document.createElement('div');
                toolElement.id = `tool-${toolCallId}`;
                toolElement.className = 'mb-3 p-3 bg-white bg-opacity-10 rounded-lg';
                toolElement.innerHTML = `
                    <div class="flex items-center mb-2">
                        <span class="tool-call-indicator"></span>
                        <span class="font-medium">${toolName}</span>
                    </div>
                    <div class="text-sm opacity-80">
                        <div>参数: <span id="args-${toolCallId}">加载中...</span></div>
                        <div>结果: <span id="result-${toolCallId}">等待中...</span></div>
                    </div>
                `;
                toolCalls.appendChild(toolElement);
            }
            
            updateToolCallArgs(toolCallId, args) {
                const argsElement = document.getElementById(`args-${toolCallId}`);
                if (argsElement) {
                    argsElement.textContent = args.length > 100 ? args.substring(0, 100) + '...' : args;
                }
            }
            
            updateToolCallResult(toolCallId, result) {
                const resultElement = document.getElementById(`result-${toolCallId}`);
                if (resultElement) {
                    try {
                        const parsed = JSON.parse(result);
                        resultElement.textContent = typeof parsed === 'object' ? '成功' : result;
                    } catch {
                        resultElement.textContent = result.length > 50 ? result.substring(0, 50) + '...' : result;
                    }
                }
                
                // 移除加载指示器
                const toolElement = document.getElementById(`tool-${toolCallId}`);
                if (toolElement) {
                    const indicator = toolElement.querySelector('.tool-call-indicator');
                    if (indicator) {
                        indicator.style.display = 'none';
                    }
                }
            }
            
            updateAgentStatus(agentName, status) {
                const agentElement = document.querySelector(`[data-agent="${agentName}"]`);
                if (agentElement) {
                    const indicator = agentElement.querySelector('.status-indicator');
                    const statusText = agentElement.querySelector('.text-white.opacity-60');
                    
                    indicator.className = `status-indicator status-${status}`;
                    
                    const statusMap = {
                        'running': '运行中',
                        'completed': '已完成',
                        'error': '错误'
                    };
                    statusText.textContent = statusMap[status] || status;
                }
            }
            
            getAgentDisplayName(agentName) {
                const nameMap = {
                    'planner': '规划师',
                    'major_transport': '大交通专家',
                    'local_transport': '小交通专家',
                    'web_search': '网络搜索专家',
                    'synthesis': '综合报告专家'
                };
                return nameMap[agentName] || agentName;
            }
            
            resetUI() {
                document.getElementById('eventStream').innerHTML = '';
                document.getElementById('toolCalls').innerHTML = '<div class="text-white opacity-60 text-center py-4">暂无工具调用</div>';
                document.getElementById('finalResult').style.display = 'none';
                
                // 重置智能体状态
                document.querySelectorAll('.agent-item').forEach(item => {
                    const indicator = item.querySelector('.status-indicator');
                    const statusText = item.querySelector('.text-white.opacity-60');
                    indicator.className = 'status-indicator status-completed';
                    statusText.textContent = '待机';
                });
                
                this.currentMessages.clear();
                this.currentToolCalls.clear();
                this.agentStates.clear();
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new AGUIEventHandler();
        });
    </script>
</body>
</html>
