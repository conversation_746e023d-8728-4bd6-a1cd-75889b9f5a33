import requests
from typing import Optional, Tuple
import os
from dotenv import load_dotenv

# 加载项目根目录下的 .env 文件
load_dotenv()

# 使用高德开放平台的地理编码API
GEOCODE_API_URL = "https://restapi.amap.com/v3/geocode/geo"
# 从环境变量中安全地获取高德API Key
AMAP_API_KEY = os.getenv("AMAP_API_KEY")

def get_city_coordinates(city_name: str) -> Optional[Tuple[float, float]]:
    """
    根据城市名称获取经纬度坐标。
    
    Args:
        city_name: 城市名称，例如 "北京"
        
    Returns:
        一个包含纬度和经度的元组 (lat, lon)，如果找不到则返回 None。
    """
    if not AMAP_API_KEY:
        print("错误：未在.env文件中找到高德地图API Key (AMAP_API_KEY)，请检查配置。")
        return None
        
    params = {
        "key": AMAP_API_KEY,
        "address": city_name,
        "output": "json"
    }
    try:
        response = requests.get(GEOCODE_API_URL, params=params)
        response.raise_for_status()
        data = response.json()
        
        if data["status"] == "1" and data["geocodes"]:
            location_str = data["geocodes"][0]["location"]
            lon, lat = map(float, location_str.split(','))
            return lat, lon
        else:
            print(f"在高德API中找不到城市 '{city_name}' 的坐标。API返回信息: {data.get('info')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"请求地理编码API时出错: {e}")
        return None
    except (KeyError, IndexError, ValueError):
        print(f"解析城市 '{city_name}' 的坐标时出错。")
        return None 