"""
Tool Event Handlers for AG-UI Protocol
Specialized event handlers for different types of tools
"""

import json
import time
from typing import Any, Dict, List, Optional, Callable
from contextlib import contextmanager

from .events import (
    ToolCallStartEvent, ToolCallArgsEvent, ToolCallEndEvent, ToolCallResultEvent,
    generate_tool_call_id, generate_message_id, chunk_string
)
from .stream_manager import AGUIStreamManager


class ToolEventHandler:
    """Base class for tool event handlers"""
    
    def __init__(self, stream_manager: AGUIStreamManager, tool_name: str):
        """
        Initialize the tool event handler
        
        Args:
            stream_manager: AG-UI stream manager
            tool_name: Name of the tool
        """
        self.stream_manager = stream_manager
        self.tool_name = tool_name
        self.tool_call_id = None
    
    @contextmanager
    def tool_call_context(self, *args, **kwargs):
        """Context manager for tool call events"""
        self.tool_call_id = generate_tool_call_id()
        
        try:
            # Start tool call
            self.stream_manager.stream.emit(
                ToolCallStartEvent(
                    tool_call_id=self.tool_call_id,
                    tool_call_name=self.tool_name,
                    parent_message_id=self.stream_manager._current_message_id
                )
            )
            
            # Emit arguments
            self._emit_arguments(*args, **kwargs)
            
            # End tool call
            self.stream_manager.stream.emit(
                ToolCallEndEvent(tool_call_id=self.tool_call_id)
            )
            
            yield self.tool_call_id
            
        except Exception as e:
            # Emit error result
            self._emit_error_result(str(e))
            raise
        finally:
            self.tool_call_id = None
    
    def _emit_arguments(self, *args, **kwargs):
        """Emit tool arguments"""
        args_dict = {
            "args": args,
            "kwargs": kwargs
        }
        args_json = json.dumps(args_dict, ensure_ascii=False, default=str)
        
        for chunk in chunk_string(args_json, 100):
            self.stream_manager.stream.emit(
                ToolCallArgsEvent(
                    tool_call_id=self.tool_call_id,
                    delta=chunk
                )
            )
    
    def _emit_success_result(self, result: Any):
        """Emit successful tool result"""
        result_json = json.dumps(result, ensure_ascii=False, default=str)
        self.stream_manager.stream.emit(
            ToolCallResultEvent(
                message_id=generate_message_id(),
                tool_call_id=self.tool_call_id,
                content=result_json
            )
        )
    
    def _emit_error_result(self, error_message: str):
        """Emit error tool result"""
        error_result = {
            "error": error_message,
            "tool": self.tool_name,
            "timestamp": time.time()
        }
        self.stream_manager.stream.emit(
            ToolCallResultEvent(
                message_id=generate_message_id(),
                tool_call_id=self.tool_call_id,
                content=json.dumps(error_result, ensure_ascii=False)
            )
        )


class TransportSearchEventHandler(ToolEventHandler):
    """Event handler for transport search tools (train, flight)"""
    
    def search_with_events(self, search_func: Callable, departure_city: str, arrival_city: str):
        """
        Execute transport search with AG-UI events
        
        Args:
            search_func: The actual search function
            departure_city: Departure city
            arrival_city: Arrival city
            
        Returns:
            Search results
        """
        with self.tool_call_context(departure_city, arrival_city) as tool_call_id:
            # Execute search
            result = search_func(departure_city, arrival_city)
            
            # Emit result
            self._emit_success_result(result)
            
            return result


class GaodeAPIEventHandler(ToolEventHandler):
    """Event handler for Gaode API tools"""
    
    def call_with_events(self, api_func: Callable, *args, **kwargs):
        """
        Execute Gaode API call with AG-UI events
        
        Args:
            api_func: The actual API function
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            API results
        """
        with self.tool_call_context(*args, **kwargs) as tool_call_id:
            # Execute API call
            result = api_func(*args, **kwargs)
            
            # Emit result
            self._emit_success_result(result)
            
            return result


class WeatherEventHandler(ToolEventHandler):
    """Event handler for weather tools"""
    
    def get_weather_with_events(self, weather_func: Callable, city: str):
        """
        Execute weather query with AG-UI events
        
        Args:
            weather_func: The actual weather function
            city: City name
            
        Returns:
            Weather data
        """
        with self.tool_call_context(city) as tool_call_id:
            # Execute weather query
            result = weather_func(city)
            
            # Emit result
            self._emit_success_result(result)
            
            return result


# Factory functions for creating event-enabled tools
def create_agui_train_search(stream_manager: AGUIStreamManager):
    """Create AG-UI enabled train search tool"""
    from src.tools.train_search_tool import search_train
    
    handler = TransportSearchEventHandler(stream_manager, "search_train")
    
    def agui_search_train(departure_city: str, arrival_city: str):
        return handler.search_with_events(search_train, departure_city, arrival_city)
    
    return agui_search_train


def create_agui_flight_search(stream_manager: AGUIStreamManager):
    """Create AG-UI enabled flight search tool"""
    from src.tools.flight_search_tool import search_flight
    
    handler = TransportSearchEventHandler(stream_manager, "search_flight")
    
    def agui_search_flight(departure_city: str, arrival_city: str):
        return handler.search_with_events(search_flight, departure_city, arrival_city)
    
    return agui_search_flight


def create_agui_weather_tool(stream_manager: AGUIStreamManager):
    """Create AG-UI enabled weather tool"""
    from src.tools.search_tool import get_weather
    
    handler = WeatherEventHandler(stream_manager, "get_weather")
    
    def agui_get_weather(city: str):
        return handler.get_weather_with_events(get_weather, city)
    
    return agui_get_weather


def create_agui_gaode_tools(stream_manager: AGUIStreamManager):
    """Create AG-UI enabled Gaode API tools"""
    from src.tools.gaode_api_tool import (
        get_city_from_coordinates, get_railway_station,
        get_taxi_route, get_transit_route
    )
    
    # Create handlers for each tool
    handlers = {
        "get_city_from_coordinates": GaodeAPIEventHandler(stream_manager, "get_city_from_coordinates"),
        "get_railway_station": GaodeAPIEventHandler(stream_manager, "get_railway_station"),
        "get_taxi_route": GaodeAPIEventHandler(stream_manager, "get_taxi_route"),
        "get_transit_route": GaodeAPIEventHandler(stream_manager, "get_transit_route"),
    }
    
    # Create wrapped functions
    tools = {}
    
    def create_wrapper(handler, original_func):
        def wrapper(*args, **kwargs):
            return handler.call_with_events(original_func, *args, **kwargs)
        return wrapper
    
    tools["get_city_from_coordinates"] = create_wrapper(
        handlers["get_city_from_coordinates"], get_city_from_coordinates
    )
    tools["get_railway_station"] = create_wrapper(
        handlers["get_railway_station"], get_railway_station
    )
    tools["get_taxi_route"] = create_wrapper(
        handlers["get_taxi_route"], get_taxi_route
    )
    tools["get_transit_route"] = create_wrapper(
        handlers["get_transit_route"], get_transit_route
    )
    
    return tools


# Comprehensive tool factory
def create_all_agui_tools(stream_manager: AGUIStreamManager) -> Dict[str, Callable]:
    """
    Create all AG-UI enabled tools
    
    Args:
        stream_manager: AG-UI stream manager
        
    Returns:
        Dictionary of all AG-UI enabled tools
    """
    tools = {}
    
    # Transport tools
    tools["search_train"] = create_agui_train_search(stream_manager)
    tools["search_flight"] = create_agui_flight_search(stream_manager)
    
    # Weather tool
    tools["get_weather"] = create_agui_weather_tool(stream_manager)
    
    # Gaode API tools
    gaode_tools = create_agui_gaode_tools(stream_manager)
    tools.update(gaode_tools)
    
    return tools


# Tool registry for easy access
class AGUIToolRegistry:
    """Registry for AG-UI enabled tools"""
    
    def __init__(self, stream_manager: AGUIStreamManager):
        """
        Initialize the tool registry
        
        Args:
            stream_manager: AG-UI stream manager
        """
        self.stream_manager = stream_manager
        self._tools = {}
        self._initialize_tools()
    
    def _initialize_tools(self):
        """Initialize all tools"""
        self._tools = create_all_agui_tools(self.stream_manager)
    
    def get_tool(self, tool_name: str) -> Optional[Callable]:
        """
        Get a tool by name
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool function or None if not found
        """
        return self._tools.get(tool_name)
    
    def list_tools(self) -> List[str]:
        """
        List all available tools
        
        Returns:
            List of tool names
        """
        return list(self._tools.keys())
    
    def call_tool(self, tool_name: str, *args, **kwargs) -> Any:
        """
        Call a tool by name
        
        Args:
            tool_name: Name of the tool
            *args: Tool arguments
            **kwargs: Tool keyword arguments
            
        Returns:
            Tool result
            
        Raises:
            ValueError: If tool not found
        """
        tool = self.get_tool(tool_name)
        if not tool:
            raise ValueError(f"Tool '{tool_name}' not found")
        
        return tool(*args, **kwargs)
