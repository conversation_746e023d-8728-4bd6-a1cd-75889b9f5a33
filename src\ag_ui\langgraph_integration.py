"""
LangGraph Integration with AG-UI Protocol
Provides decorators and utilities to integrate LangGraph nodes with AG-UI events
"""

import functools
from typing import Dict, Any, Callable, Optional, Generator
import json
import asyncio
from contextlib import contextmanager

from .events import (
    EventType, StepStartedEvent, StepFinishedEvent,
    TextMessageStartEvent, TextMessageContentEvent, TextMessageEndEvent,
    StateSnapshotEvent, StateDeltaEvent,
    generate_message_id, create_json_patch
)
from .stream_manager import AGUIStreamManager
from .tool_wrapper import AGUIToolWrapper


class LangGraphAGUIIntegration:
    """Integration layer between LangGraph and AG-UI"""
    
    def __init__(self, stream_manager: AGUIStreamManager):
        """
        Initialize the integration
        
        Args:
            stream_manager: AG-UI stream manager
        """
        self.stream_manager = stream_manager
        self.tool_wrapper = AGUIToolWrapper(stream_manager)
        self._node_states = {}
    
    def agui_node(self, node_name: str = None, emit_thinking: bool = True):
        """
        Decorator to wrap LangGraph nodes with AG-UI event emission
        
        Args:
            node_name: Name of the node (defaults to function name)
            emit_thinking: Whether to emit thinking process as text messages
            
        Returns:
            Decorated function
        """
        def decorator(func: Callable) -> Callable:
            node_name_final = node_name or func.__name__
            
            @functools.wraps(func)
            def wrapped_node(state: Dict[str, Any]) -> Dict[str, Any]:
                """Wrapped node function that emits AG-UI events"""
                
                # Store previous state for delta calculation
                previous_state = self._node_states.get(node_name_final, {})
                
                # Emit step started event
                self.stream_manager.start_step(node_name_final)
                
                try:
                    # Execute the original node function
                    result_state = func(state)
                    
                    # Emit thinking process if available and enabled
                    if emit_thinking and "thinking_process" in result_state:
                        thoughts = result_state.get("thinking_process", [])
                        if thoughts:
                            self._emit_thinking_process(node_name_final, thoughts)
                            # Clear thinking process to avoid duplication
                            result_state["thinking_process"] = []
                    
                    # Emit state delta
                    self._emit_state_delta(previous_state, result_state)
                    
                    # Store current state
                    self._node_states[node_name_final] = result_state.copy()
                    
                    # Emit step finished event
                    self.stream_manager.finish_step(node_name_final)
                    
                    return result_state
                    
                except Exception as e:
                    # Emit error and re-raise
                    self.stream_manager.error_run(f"Node {node_name_final} failed: {str(e)}")
                    raise
            
            return wrapped_node
        
        return decorator
    
    def _emit_thinking_process(self, agent_name: str, thoughts: list):
        """
        Emit thinking process as text message events
        
        Args:
            agent_name: Name of the agent
            thoughts: List of thought strings
        """
        if not thoughts:
            return
        
        message_id = self.stream_manager.start_message()
        
        for thought in thoughts:
            content = f"[{agent_name}] {thought}\n"
            self.stream_manager.add_message_content(content)
        
        self.stream_manager.end_message()
    
    def _emit_state_delta(self, old_state: Dict[str, Any], new_state: Dict[str, Any]):
        """
        Emit state delta event
        
        Args:
            old_state: Previous state
            new_state: New state
        """
        # Filter out non-serializable fields
        filtered_old = self._filter_state_for_delta(old_state)
        filtered_new = self._filter_state_for_delta(new_state)
        
        patches = create_json_patch(filtered_old, filtered_new)
        if patches:
            self.stream_manager.update_state(filtered_new, emit_delta=True)
    
    def _filter_state_for_delta(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter state to include only serializable fields for delta emission
        
        Args:
            state: Original state
            
        Returns:
            Filtered state
        """
        filtered = {}
        
        # Include key fields that are JSON serializable
        serializable_fields = [
            "departure_city", "arrival_city", "departure_place", "arrival_place",
            "departure_coordinates", "arrival_coordinates", "travel_date",
            "passenger_count", "criteria", "transport_preference",
            "major_transport_options", "selected_major_transport",
            "local_transport_options", "departure_station", "arrival_station",
            "current_step", "next_step", "is_clarification_needed",
            "clarification_question", "clarification_options",
            "final_plan", "timeline", "map_data"
        ]
        
        for field in serializable_fields:
            if field in state:
                try:
                    # Test JSON serialization
                    json.dumps(state[field], default=str)
                    filtered[field] = state[field]
                except (TypeError, ValueError):
                    # Skip non-serializable fields
                    continue
        
        return filtered
    
    def wrap_tools_in_state(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Wrap tools in the state with AG-UI event emission
        
        Args:
            state: LangGraph state
            
        Returns:
            State with wrapped tools
        """
        # This would wrap tools if they were stored in state
        # For now, tools are imported directly in nodes
        return state


def create_agui_langgraph_integration(stream_manager: AGUIStreamManager) -> LangGraphAGUIIntegration:
    """
    Create a new LangGraph AG-UI integration
    
    Args:
        stream_manager: AG-UI stream manager
        
    Returns:
        New LangGraphAGUIIntegration instance
    """
    return LangGraphAGUIIntegration(stream_manager)


# Context manager for AG-UI enabled LangGraph execution
@contextmanager
def agui_langgraph_context(stream_manager: AGUIStreamManager):
    """
    Context manager for AG-UI enabled LangGraph execution
    
    Args:
        stream_manager: AG-UI stream manager
        
    Yields:
        LangGraphAGUIIntegration instance
    """
    integration = LangGraphAGUIIntegration(stream_manager)
    try:
        yield integration
    finally:
        # Cleanup if needed
        pass


# Utility functions for existing nodes
def add_agui_events_to_existing_nodes():
    """
    Add AG-UI event emission to existing LangGraph nodes
    This function modifies the existing nodes to emit AG-UI events
    """
    # This would be called to retrofit existing nodes
    # Implementation would depend on how we want to modify existing code
    pass


def create_agui_enabled_graph(original_graph, stream_manager: AGUIStreamManager):
    """
    Create an AG-UI enabled version of an existing LangGraph
    
    Args:
        original_graph: Original LangGraph
        stream_manager: AG-UI stream manager
        
    Returns:
        AG-UI enabled graph
    """
    # This would create a new graph with AG-UI event emission
    # Implementation would depend on LangGraph's architecture
    pass


# Async version for async LangGraph nodes
class AsyncLangGraphAGUIIntegration(LangGraphAGUIIntegration):
    """Async version of LangGraph AG-UI integration"""
    
    def agui_async_node(self, node_name: str = None, emit_thinking: bool = True):
        """
        Decorator to wrap async LangGraph nodes with AG-UI event emission
        
        Args:
            node_name: Name of the node (defaults to function name)
            emit_thinking: Whether to emit thinking process as text messages
            
        Returns:
            Decorated async function
        """
        def decorator(func: Callable) -> Callable:
            node_name_final = node_name or func.__name__
            
            @functools.wraps(func)
            async def wrapped_async_node(state: Dict[str, Any]) -> Dict[str, Any]:
                """Wrapped async node function that emits AG-UI events"""
                
                # Store previous state for delta calculation
                previous_state = self._node_states.get(node_name_final, {})
                
                # Emit step started event
                self.stream_manager.start_step(node_name_final)
                
                try:
                    # Execute the original async node function
                    result_state = await func(state)
                    
                    # Emit thinking process if available and enabled
                    if emit_thinking and "thinking_process" in result_state:
                        thoughts = result_state.get("thinking_process", [])
                        if thoughts:
                            self._emit_thinking_process(node_name_final, thoughts)
                            # Clear thinking process to avoid duplication
                            result_state["thinking_process"] = []
                    
                    # Emit state delta
                    self._emit_state_delta(previous_state, result_state)
                    
                    # Store current state
                    self._node_states[node_name_final] = result_state.copy()
                    
                    # Emit step finished event
                    self.stream_manager.finish_step(node_name_final)
                    
                    return result_state
                    
                except Exception as e:
                    # Emit error and re-raise
                    self.stream_manager.error_run(f"Node {node_name_final} failed: {str(e)}")
                    raise
            
            return wrapped_async_node
        
        return decorator
