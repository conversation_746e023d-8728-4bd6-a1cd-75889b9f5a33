<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
  <style>
    .node { fill: #fff; stroke: #000; stroke-width: 2px; }
    .planner { fill: #6366f1; stroke: #4f46e5; }
    .major-transport { fill: #60a5fa; stroke: #2563eb; }
    .local-transport { fill: #34d399; stroke: #059669; }
    .web-search { fill: #fbbf24; stroke: #d97706; }
    .synthesis { fill: #a78bfa; stroke: #7c3aed; }
    .ask-user { fill: #f87171; stroke: #dc2626; }
    .state { fill: #e5e7eb; stroke: #9ca3af; }
    .external { fill: #cbd5e1; stroke: #94a3b8; }
    .user { fill: #c4b5fd; stroke: #8b5cf6; }
    .node-text { fill: #fff; font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; dominant-baseline: middle; }
    .state-text { fill: #000; font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; dominant-baseline: middle; }
    .edge { stroke: #64748b; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
    .edge-text { fill: #475569; font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
    .subgraph { fill: #f8fafc; stroke: #cbd5e1; stroke-width: 1px; rx: 8px; ry: 8px; }
    .subgraph-label { fill: #334155; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b" />
    </marker>
  </defs>
  
  <!-- 子图：用户交互 -->
  <rect x="50" y="50" width="700" height="100" class="subgraph" />
  <text x="100" y="80" class="subgraph-label">用户交互</text>
  <circle cx="150" cy="120" r="30" class="node user" />
  <text x="150" y="120" class="node-text">用户</text>
  <rect x="250" y="90" width="120" height="60" rx="8" ry="8" class="node user" />
  <text x="310" y="120" class="node-text">网页界面/CLI</text>
  
  <!-- 子图：多智能体工作流 -->
  <rect x="50" y="170" width="700" height="250" class="subgraph" />
  <text x="100" y="200" class="subgraph-label">多智能体工作流</text>
  
  <!-- 规划师节点 -->
  <circle cx="250" cy="250" r="30" class="node planner" />
  <text x="250" y="250" class="node-text">规划师</text>
  
  <!-- 专家节点 -->
  <circle cx="150" cy="350" r="30" class="node major-transport" />
  <text x="150" y="350" class="node-text">大交通专家</text>
  
  <circle cx="250" cy="350" r="30" class="node local-transport" />
  <text x="250" y="350" class="node-text">小交通专家</text>
  
  <circle cx="350" cy="350" r="30" class="node web-search" />
  <text x="350" y="350" class="node-text">网络搜索</text>
  
  <circle cx="450" cy="350" r="30" class="node synthesis" />
  <text x="450" y="350" class="node-text">综合报告</text>
  
  <circle cx="550" cy="250" r="30" class="node ask-user" />
  <text x="550" y="250" class="node-text">用户交互</text>
  
  <circle cx="650" cy="350" r="30" class="node external" />
  <text x="650" y="350" class="node-text">结束</text>
  
  <!-- 子图：外部工具和API -->
  <rect x="50" y="440" width="700" height="100" class="subgraph" />
  <text x="100" y="470" class="subgraph-label">外部工具和API</text>
  
  <rect x="120" y="480" width="80" height="40" rx="8" ry="8" class="node external" />
  <text x="160" y="500" class="state-text">火车查询</text>
  
  <rect x="220" y="480" width="80" height="40" rx="8" ry="8" class="node external" />
  <text x="260" y="500" class="state-text">高德地图</text>
  
  <rect x="320" y="480" width="80" height="40" rx="8" ry="8" class="node external" />
  <text x="360" y="500" class="state-text">天气查询</text>
  
  <!-- 共享状态 -->
  <rect x="500" y="440" width="200" height="100" class="subgraph" />
  <text x="550" y="470" class="subgraph-label">共享状态</text>
  <rect x="520" y="480" width="160" height="40" rx="8" ry="8" class="node state" />
  <text x="600" y="500" class="state-text">ItineraryState</text>
  
  <!-- 连接线 -->
  <!-- 用户交互 -->
  <path d="M180 120 L250 120" class="edge" />
  <path d="M310 150 L250 220" class="edge" />
  <path d="M450 350 L410 410 L310 150" class="edge" />
  
  <!-- 规划师到专家 -->
  <path d="M235 275 L165 325" class="edge" />
  <text x="190" y="300" class="edge-text">路由</text>
  
  <path d="M250 280 L250 320" class="edge" />
  <text x="260" y="300" class="edge-text">路由</text>
  
  <path d="M265 275 L335 325" class="edge" />
  <text x="310" y="300" class="edge-text">路由</text>
  
  <path d="M280 250 L420 320" class="edge" />
  <text x="330" y="270" class="edge-text">路由</text>
  
  <!-- 专家到规划师 -->
  <path d="M175 325 L225 275" class="edge" />
  <text x="190" y="290" class="edge-text">返回</text>
  
  <path d="M250 320 L250 280" class="edge" />
  <text x="270" y="300" class="edge-text">返回</text>
  
  <path d="M335 325 L265 275" class="edge" />
  <text x="310" y="290" class="edge-text">返回</text>
  
  <!-- 规划师到用户交互 -->
  <path d="M280 250 L520 250" class="edge" />
  <text x="400" y="240" class="edge-text">需要澄清</text>
  
  <path d="M520 250 L280 250" class="edge" />
  <text x="400" y="260" class="edge-text">返回</text>
  
  <!-- 用户交互到用户 -->
  <path d="M550 220 L550 150 L180 120" class="edge" />
  <text x="400" y="140" class="edge-text">澄清问题</text>
  
  <!-- 综合报告到结束 -->
  <path d="M480 350 L620 350" class="edge" />
  <text x="550" y="340" class="edge-text">最终结果</text>
  
  <!-- 外部工具连接 -->
  <path d="M150 380 L160 480" class="edge" />
  <path d="M250 380 L260 480" class="edge" />
  <path d="M350 380 L360 480" class="edge" />
  
  <!-- 状态连接 -->
  <path d="M600 480 L250 250" class="edge" stroke-dasharray="5,5" />
  <path d="M600 480 L150 350" class="edge" stroke-dasharray="5,5" />
  <path d="M600 480 L250 350" class="edge" stroke-dasharray="5,5" />
  <path d="M600 480 L350 350" class="edge" stroke-dasharray="5,5" />
  <path d="M600 480 L450 350" class="edge" stroke-dasharray="5,5" />
  <path d="M600 480 L550 250" class="edge" stroke-dasharray="5,5" />
</svg> 