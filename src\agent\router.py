# agent/router.py
from typing import Literal, Dict, Any
from src.agent.state import ItineraryState
import time

def route_by_next_step(state: ItineraryState) -> str:
    """
    基于状态中的next_step字段路由到下一个节点。
    这是主要的路由逻辑，决定执行流程中的下一步。
    
    返回:
        下一个节点的名称或"end"表示结束
    """
    next_step = state.get("next_step", "planner")
    
    # 如果next_step为None，默认返回planner
    if next_step is None:
        print("[路由] ⚠️ next_step为None，默认路由到'planner'")
        next_step = "planner"
        
    print(f"[路由] 🔀 从 '{state.get('current_step', 'unknown')}' 路由到 '{next_step}'")
    
    # 如果明确指定了结束，则结束流程
    if next_step == "end":
        print("[路由] 🏁 流程结束")
        return "end"
    
    # 验证next_step是否为有效节点
    valid_nodes = ["planner", "major_transport", "local_transport", "web_search", "synthesis", "ask_user", "intelligent_ask_user"]
    if next_step not in valid_nodes:
        print(f"[路由] ⚠️ 警告: '{next_step}'不是有效节点，默认路由到'planner'")
        return "planner"
    
    return next_step

def check_user_interaction_needed(state: ItineraryState) -> Literal["continue", "pause"]:
    """
    检查是否需要暂停工作流以等待用户输入。
    当状态中的is_clarification_needed为True时，路由到ask_user节点。
    
    返回:
        "pause" - 需要用户交互，暂停工作流
        "continue" - 不需要用户交互，继续执行
    """
    # 明确检查is_clarification_needed字段
    if state.get("is_clarification_needed", False) is True:
        question = state.get("clarification_question", "需要您提供更多信息...")
        print(f"[路由] ❓ 需要用户交互: {question}")
        return "pause"
    else:
        print(f"[路由] ➡️ 无需用户交互，继续流程")
        return "continue"

def check_error_condition(state: ItineraryState) -> Literal["normal", "error"]:
    """
    检查状态中是否有需要处理的错误。
    如果有严重错误且未被处理，路由到错误处理节点。
    
    返回:
        "error" - 存在严重错误需要处理
        "normal" - 无错误或错误已处理
    """
    errors = state.get("errors", [])
    # 检查是否有新的、未处理的错误
    unhandled_errors = [e for e in errors if not e.get("handled", False)]
    
    if unhandled_errors:
        error_count = len(unhandled_errors)
        print(f"[路由] ⚠️ 检测到{error_count}个未处理错误")
        return "error"
    return "normal"

def check_final_state(state: ItineraryState) -> Literal["complete", "incomplete"]:
    """
    检查行程规划是否完成。
    当final_plan存在且非空时，认为规划完成。
    
    返回:
        "complete" - 行程规划已完成
        "incomplete" - 行程规划未完成
    """
    # 检查是否有强制终止条件
    if state.get("major_transport_failure_count", 0) > 5:
        print("[路由] ⚠️ 检测到多次失败，强制结束流程")
        return "complete"
    
    # 检查是否显式请求结束    
    if state.get("next_step") == "end":
        print("[路由] 🏁 检测到终止请求，结束流程")
        time.sleep(10)
        return "complete"
        
    # 检查是否有最终规划结果
    if state.get("final_plan"):
        print("[路由] ✅ 行程规划已完成")
        return "complete"
    
    # 如果没有完成规划，继续流程
    print("[路由] ⏳ 行程规划尚未完成，继续流程")
    return "incomplete"