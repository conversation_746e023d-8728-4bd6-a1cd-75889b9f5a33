"""
AG-UI FastAPI Integration
Provides AG-UI compatible endpoints for the itinerary planning system
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, AsyncGenerator
import asyncio
import json
import uuid
from datetime import datetime

from .events import (
    EventType, generate_thread_id, generate_run_id, generate_message_id
)
from .stream_manager import AsyncAGUIStreamManager
from .encoder import EventEncoder


class Message(BaseModel):
    """AG-UI Message format"""
    id: str
    role: str  # "user", "assistant", "tool"
    content: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None


class RunAgentInput(BaseModel):
    """AG-UI RunAgent input format"""
    thread_id: str
    run_id: str
    messages: List[Message]
    context: Optional[List[Dict[str, Any]]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    state: Optional[Dict[str, Any]] = None


class AGUIFastAPIIntegration:
    """FastAPI integration for AG-UI protocol"""
    
    def __init__(self, app: FastAPI):
        """
        Initialize the AG-UI FastAPI integration
        
        Args:
            app: FastAPI application instance
        """
        self.app = app
        self.encoder = EventEncoder("sse")
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup AG-UI compatible routes"""
        
        @self.app.post("/agent")
        async def run_agent(input_data: RunAgentInput):
            """
            AG-UI compatible agent endpoint
            Streams events using Server-Sent Events
            """
            return StreamingResponse(
                self._run_agent_stream(input_data),
                media_type=self.encoder.get_content_type(),
                headers=self.encoder.get_headers()
            )
        
        @self.app.post("/agent/chat")
        async def chat_with_agent(request: Request):
            """
            Simplified chat endpoint that creates AG-UI compatible input
            """
            try:
                body = await request.json()
                message = body.get("message", "")
                
                if not message:
                    raise HTTPException(status_code=400, detail="Message is required")
                
                # Create AG-UI compatible input
                thread_id = generate_thread_id()
                run_id = generate_run_id()
                
                input_data = RunAgentInput(
                    thread_id=thread_id,
                    run_id=run_id,
                    messages=[
                        Message(
                            id=generate_message_id(),
                            role="user",
                            content=message
                        )
                    ]
                )
                
                return StreamingResponse(
                    self._run_agent_stream(input_data),
                    media_type=self.encoder.get_content_type(),
                    headers=self.encoder.get_headers()
                )
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _run_agent_stream(self, input_data: RunAgentInput) -> AsyncGenerator[str, None]:
        """
        Stream AG-UI events for agent execution
        
        Args:
            input_data: AG-UI RunAgent input
            
        Yields:
            Encoded AG-UI events
        """
        stream_manager = AsyncAGUIStreamManager(
            thread_id=input_data.thread_id,
            run_id=input_data.run_id
        )
        
        try:
            # Start the run
            yield stream_manager.start_run()
            
            # Convert AG-UI messages to LangGraph format
            langgraph_state = self._convert_to_langgraph_state(input_data)
            
            # Emit initial state snapshot
            yield stream_manager.snapshot_state(langgraph_state)
            
            # Import LangGraph app
            from src.agent.graph import itinerary_app
            
            # Run the LangGraph workflow with AG-UI event streaming
            async for event_data in self._stream_langgraph_with_agui(
                langgraph_state, stream_manager
            ):
                yield event_data
            
            # Finish the run
            yield stream_manager.finish_run()
            
        except Exception as e:
            # Emit error event
            yield stream_manager.error_run(str(e))
            raise
    
    def _convert_to_langgraph_state(self, input_data: RunAgentInput) -> Dict[str, Any]:
        """
        Convert AG-UI input to LangGraph state format
        
        Args:
            input_data: AG-UI RunAgent input
            
        Returns:
            LangGraph compatible state
        """
        from langchain_core.messages import HumanMessage, AIMessage
        
        # Convert messages
        messages = []
        user_query = ""
        
        for msg in input_data.messages:
            if msg.role == "user":
                messages.append(HumanMessage(content=msg.content or ""))
                user_query = msg.content or ""
            elif msg.role == "assistant":
                messages.append(AIMessage(content=msg.content or ""))
        
        # Create LangGraph state
        state = {
            "messages": messages,
            "original_query": user_query,
            "thread_id": input_data.thread_id,
            "run_id": input_data.run_id,
            "execution_history": [],
            "thinking_process": [],
            "current_step": "planner",
            "next_step": "planner",
            "is_clarification_needed": False,
            "clarification_count": 0,
            "major_transport_failure_count": 0,
            "failed_searches": {},
            "tool_results": {},
            "errors": [],
            "debug_info": {}
        }
        
        # Add any additional state from input
        if input_data.state:
            state.update(input_data.state)
        
        return state
    
    async def _stream_langgraph_with_agui(
        self,
        initial_state: Dict[str, Any],
        stream_manager: AsyncAGUIStreamManager
    ) -> AsyncGenerator[str, None]:
        """
        Stream LangGraph execution with AG-UI events

        Args:
            initial_state: Initial LangGraph state
            stream_manager: AG-UI stream manager

        Yields:
            Encoded AG-UI events
        """
        try:
            # Set up AG-UI context for nodes
            from src.agent.agui_nodes import set_agui_context
            set_agui_context(stream_manager)

            # Create AG-UI compatible graph
            from src.agent.agui_graph import create_agui_itinerary_graph
            agui_app = create_agui_itinerary_graph()

            # Create LangGraph stream
            stream = agui_app.astream(initial_state)

            current_step = None
            last_state = initial_state.copy()

            async for chunk in stream:
                if not chunk:
                    continue

                # Extract node name and state
                node_name = list(chunk.keys())[0]
                new_state = chunk[node_name]

                # The AG-UI events are already emitted by the nodes
                # We just need to track state changes here
                last_state = new_state.copy()

                # Handle clarification if needed
                if new_state.get("is_clarification_needed"):
                    question = new_state.get("clarification_question", "需要更多信息")

                    yield stream_manager.start_message()
                    yield stream_manager.add_message_content(f"🤖 需要澄清：{question}")
                    yield stream_manager.end_message()

                    # In a real implementation, you would pause here and wait for user input
                    # For now, we'll continue with a default response
                    break

            # Emit final messages snapshot
            if "messages" in last_state:
                messages_data = []
                for msg in last_state["messages"]:
                    if hasattr(msg, 'content'):
                        messages_data.append({
                            "id": generate_message_id(),
                            "role": "user" if msg.__class__.__name__ == "HumanMessage" else "assistant",
                            "content": msg.content
                        })

                yield stream_manager.snapshot_messages(messages_data)

        except Exception as e:
            yield stream_manager.error_run(f"LangGraph execution error: {str(e)}")
            raise


def setup_agui_routes(app: FastAPI) -> AGUIFastAPIIntegration:
    """
    Setup AG-UI routes on a FastAPI app
    
    Args:
        app: FastAPI application instance
        
    Returns:
        AGUIFastAPIIntegration instance
    """
    return AGUIFastAPIIntegration(app)


# Compatibility layer for existing endpoints
class CompatibilityLayer:
    """Provides compatibility with existing non-AG-UI endpoints"""
    
    @staticmethod
    def wrap_existing_endpoint(endpoint_func):
        """
        Wrap an existing endpoint to also support AG-UI format
        
        Args:
            endpoint_func: Existing endpoint function
            
        Returns:
            Wrapped function that supports both formats
        """
        async def wrapped_endpoint(request: Request, *args, **kwargs):
            # Check if request wants AG-UI format
            accept_header = request.headers.get("accept", "")
            
            if "text/event-stream" in accept_header:
                # Convert to AG-UI streaming response
                # This would need to be implemented based on the specific endpoint
                pass
            else:
                # Use original endpoint
                return await endpoint_func(request, *args, **kwargs)
        
        return wrapped_endpoint
