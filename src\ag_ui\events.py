"""
AG-UI Protocol Event Types and Classes
Based on the AG-UI Protocol specification
"""

from typing import Dict, Any, List, Optional, Literal, Union
from enum import Enum
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
import json


class EventType(str, Enum):
    """AG-UI Protocol Event Types"""
    # Lifecycle Events
    RUN_STARTED = "RUN_STARTED"
    RUN_FINISHED = "RUN_FINISHED"
    RUN_ERROR = "RUN_ERROR"
    STEP_STARTED = "STEP_STARTED"
    STEP_FINISHED = "STEP_FINISHED"
    
    # Text Message Events
    TEXT_MESSAGE_START = "TEXT_MESSAGE_START"
    TEXT_MESSAGE_CONTENT = "TEXT_MESSAGE_CONTENT"
    TEXT_MESSAGE_END = "TEXT_MESSAGE_END"
    
    # Tool Call Events
    TOOL_CALL_START = "TOOL_CALL_START"
    TOOL_CALL_ARGS = "TOOL_CALL_ARGS"
    TOOL_CALL_END = "TOOL_CALL_END"
    TOOL_CALL_RESULT = "TOOL_CALL_RESULT"
    
    # State Management Events
    STATE_SNAPSHOT = "STATE_SNAPSHOT"
    STATE_DELTA = "STATE_DELTA"
    MESSAGES_SNAPSHOT = "MESSAGES_SNAPSHOT"
    
    # Special Events
    RAW = "RAW"
    CUSTOM = "CUSTOM"


class BaseEvent(BaseModel):
    """Base class for all AG-UI events"""
    type: EventType
    timestamp: Optional[int] = Field(default_factory=lambda: int(datetime.now().timestamp() * 1000))
    raw_event: Optional[Any] = None


# Lifecycle Events
class RunStartedEvent(BaseEvent):
    type: Literal[EventType.RUN_STARTED] = EventType.RUN_STARTED
    thread_id: str
    run_id: str


class RunFinishedEvent(BaseEvent):
    type: Literal[EventType.RUN_FINISHED] = EventType.RUN_FINISHED
    thread_id: str
    run_id: str
    result: Optional[Any] = None


class RunErrorEvent(BaseEvent):
    type: Literal[EventType.RUN_ERROR] = EventType.RUN_ERROR
    message: str
    code: Optional[str] = None


class StepStartedEvent(BaseEvent):
    type: Literal[EventType.STEP_STARTED] = EventType.STEP_STARTED
    step_name: str


class StepFinishedEvent(BaseEvent):
    type: Literal[EventType.STEP_FINISHED] = EventType.STEP_FINISHED
    step_name: str


# Text Message Events
class TextMessageStartEvent(BaseEvent):
    type: Literal[EventType.TEXT_MESSAGE_START] = EventType.TEXT_MESSAGE_START
    message_id: str
    role: Literal["assistant"] = "assistant"


class TextMessageContentEvent(BaseEvent):
    type: Literal[EventType.TEXT_MESSAGE_CONTENT] = EventType.TEXT_MESSAGE_CONTENT
    message_id: str
    delta: str

    def model_post_init(self, __context):
        if len(self.delta) == 0:
            raise ValueError("Delta must not be an empty string")


class TextMessageEndEvent(BaseEvent):
    type: Literal[EventType.TEXT_MESSAGE_END] = EventType.TEXT_MESSAGE_END
    message_id: str


# Tool Call Events
class ToolCallStartEvent(BaseEvent):
    type: Literal[EventType.TOOL_CALL_START] = EventType.TOOL_CALL_START
    tool_call_id: str
    tool_call_name: str
    parent_message_id: Optional[str] = None


class ToolCallArgsEvent(BaseEvent):
    type: Literal[EventType.TOOL_CALL_ARGS] = EventType.TOOL_CALL_ARGS
    tool_call_id: str
    delta: str


class ToolCallEndEvent(BaseEvent):
    type: Literal[EventType.TOOL_CALL_END] = EventType.TOOL_CALL_END
    tool_call_id: str


class ToolCallResultEvent(BaseEvent):
    type: Literal[EventType.TOOL_CALL_RESULT] = EventType.TOOL_CALL_RESULT
    message_id: str
    tool_call_id: str
    content: str
    role: Optional[Literal["tool"]] = "tool"


# State Management Events
class StateSnapshotEvent(BaseEvent):
    type: Literal[EventType.STATE_SNAPSHOT] = EventType.STATE_SNAPSHOT
    snapshot: Any


class StateDeltaEvent(BaseEvent):
    type: Literal[EventType.STATE_DELTA] = EventType.STATE_DELTA
    delta: List[Any]  # JSON Patch operations


class MessagesSnapshotEvent(BaseEvent):
    type: Literal[EventType.MESSAGES_SNAPSHOT] = EventType.MESSAGES_SNAPSHOT
    messages: List[Dict[str, Any]]


# Special Events
class RawEvent(BaseEvent):
    type: Literal[EventType.RAW] = EventType.RAW
    event: Any
    source: Optional[str] = None


class CustomEvent(BaseEvent):
    type: Literal[EventType.CUSTOM] = EventType.CUSTOM
    name: str
    value: Any


# Union type for all events
AGUIEvent = Union[
    RunStartedEvent,
    RunFinishedEvent,
    RunErrorEvent,
    StepStartedEvent,
    StepFinishedEvent,
    TextMessageStartEvent,
    TextMessageContentEvent,
    TextMessageEndEvent,
    ToolCallStartEvent,
    ToolCallArgsEvent,
    ToolCallEndEvent,
    ToolCallResultEvent,
    StateSnapshotEvent,
    StateDeltaEvent,
    MessagesSnapshotEvent,
    RawEvent,
    CustomEvent
]


def generate_id() -> str:
    """Generate a unique ID for events"""
    return str(uuid.uuid4())


def generate_message_id() -> str:
    """Generate a unique message ID"""
    return f"msg_{generate_id()}"


def generate_tool_call_id() -> str:
    """Generate a unique tool call ID"""
    return f"tool_{generate_id()}"


def generate_run_id() -> str:
    """Generate a unique run ID"""
    return f"run_{generate_id()}"


def generate_thread_id() -> str:
    """Generate a unique thread ID"""
    return f"thread_{generate_id()}"


def chunk_string(text: str, chunk_size: int = 50) -> List[str]:
    """Split a string into chunks for streaming"""
    return [text[i:i + chunk_size] for i in range(0, len(text), chunk_size)]


def create_json_patch(old_data: dict, new_data: dict) -> List[Dict[str, Any]]:
    """Create JSON Patch operations for state delta"""
    patches = []
    
    # Simple implementation - can be enhanced with proper JSON Patch library
    for key, new_value in new_data.items():
        old_value = old_data.get(key)
        if old_value != new_value:
            patches.append({
                "op": "replace" if key in old_data else "add",
                "path": f"/{key}",
                "value": new_value
            })
    
    # Check for removed keys
    for key in old_data:
        if key not in new_data:
            patches.append({
                "op": "remove",
                "path": f"/{key}"
            })
    
    return patches
