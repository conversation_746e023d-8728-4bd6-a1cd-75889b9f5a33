# LangGraph多智能体系统架构

## 架构概述

本项目采用基于LangGraph的多智能体协作系统（Agentic Swarm），通过多个专业智能体的协同工作，实现复杂行程规划任务的自动化处理。系统构建了一个状态共享、动态路由的工作流，支持专家智能体间的信息传递和任务分工，以及与用户的交互式澄清。

![多智能体系统架构图](../assets/images/multi-agent-architecture.png)

## 核心组件

### 1. 状态管理（State）

系统采用集中式状态管理模式，所有智能体共享一个全局状态对象，包含：

```python
ItineraryState(TypedDict, total=False):
    # 基础信息
    messages: List  # 消息历史
    original_query: str  # 原始查询
    structured_request: Dict  # 结构化请求
    
    # 地点信息
    departure_city: str  # 出发城市
    arrival_city: str  # 到达城市
    departure_place: str  # 出发地点
    arrival_place: str  # 目的地点
    departure_coordinates: Dict  # 出发地坐标
    arrival_coordinates: Dict  # 目的地坐标
    
    # 交通规划
    travel_date: str  # 出行日期
    passenger_count: int  # 乘客数量
    major_transport_options: List  # 大交通选项
    selected_major_transport: Dict  # 已选大交通
    departure_station: Dict  # 出发站
    arrival_station: Dict  # 到达站
    local_transport_options: Dict  # 小交通选项
    
    # 可视化数据
    map_data: Dict  # 地图数据
    timeline: Dict  # 时间轴
    
    # 用户交互
    is_clarification_needed: bool  # 是否需要用户澄清
    clarification_question: str  # 澄清问题
    clarification_options: List  # 澄清选项
    user_clarification_response: str  # 用户回应
    
    # 执行控制
    current_tool: str  # 当前工具
    tool_results: Dict  # 工具结果
    errors: List  # 错误列表
    current_step: str  # 当前步骤
    next_step: str  # 下一步骤
    execution_history: List  # 执行历史
    
    # 输出结果
    final_plan: Dict  # 最终规划
    thinking_process: List  # 思考过程
    debug_info: Dict  # 调试信息
```

### 2. 专家智能体节点

系统包含多个专业智能体节点，每个节点专注于特定领域的决策和处理：

#### 2.1 规划师节点（Planner Node）

规划师是系统的中央控制节点，负责：
- 解析用户自然语言请求为结构化数据
- 决定下一步应该执行哪个专家节点
- 分析工作流状态并确定后续行动

```python
def planner_node(state: ItineraryState) -> ItineraryState:
    """规划师节点负责解析用户需求，安排任务，并决定下一步执行哪个专家节点"""
    # 解析用户查询
    if "structured_request" not in state:
        # 使用LLM解析自然语言为结构化数据
    
    # 决定下一步执行哪个专家节点
    if not state.get("major_transport_options"):
        state["next_step"] = "major_transport"
    elif state.get("selected_major_transport") and not state.get("local_transport_options"):
        state["next_step"] = "local_transport"
    # ... 其他决策逻辑
    
    return state
```

#### 2.2 大交通专家节点（Major Transport Node）

负责规划城市间的高铁/火车交通方案：
- 调用火车/高铁查询工具
- 基于用户偏好选择最优交通方案
- 提取站点信息用于后续规划

```python
def major_transport_node(state: ItineraryState) -> ItineraryState:
    """大交通专家负责规划城市间的高铁/火车交通方案"""
    # 检查必要参数
    departure_city = state.get("departure_city")
    arrival_city = state.get("arrival_city")
    
    # 调用火车/高铁查询工具
    result = search_train(departure_city, arrival_city, criteria, date)
    
    # 处理查询结果
    if result["status"] == "success":
        state["major_transport_options"] = [result["data"]]
        state["selected_major_transport"] = result["data"]
    else:
        # 错误处理并请求用户澄清
    
    return state
```

#### 2.3 小交通专家节点（Local Transport Node）

负责规划目的地内的驾车路线：
- 获取出发地、目的地和车站坐标
- 调用高德地图API规划驾车路线
- 提取路线信息和驾驶指引

```python
def local_transport_node(state: ItineraryState) -> ItineraryState:
    """小交通专家负责规划目的地内的交通方案"""
    # 获取车站信息
    departure_station = get_railway_station(departure_city)
    arrival_station = get_railway_station(arrival_city)
    
    # 规划出发地到出发站的路线
    dep_route = get_driving_route(
        departure_coordinates["lng"], 
        departure_coordinates["lat"],
        departure_station["location"]["lng"], 
        departure_station["location"]["lat"]
    )
    
    # 规划到达站到目的地的路线
    arr_route = get_driving_route(...)
    
    # 更新状态
    state["local_transport_options"] = {
        "departure": dep_route,
        "arrival": arr_route
    }
    
    return state
```

#### 2.4 网络搜索专家节点（Web Search Node）

负责获取城市、景点、天气等辅助信息：
- 调用天气API获取出发地和目的地天气
- 生成本地交通提示
- 整合辅助信息到最终规划中

```python
def web_search_node(state: ItineraryState) -> ItineraryState:
    """网络搜索专家负责获取天气、景点等辅助信息"""
    # 查询天气信息
    departure_weather = get_weather(departure_city)
    arrival_weather = get_weather(arrival_city)
    
    # 生成交通提示
    transportation_tips = generate_transportation_tips(arrival_city)
    
    # 更新状态
    state["additional_info"] = {
        "weather": {
            departure_city: departure_weather,
            arrival_city: arrival_weather
        },
        "transportation_tips": transportation_tips
    }
    
    return state
```

#### 2.5 综合报告智能体（Synthesis Node）

负责整合所有信息并生成最终行程规划：
- 计算详细时间轴（包括跨天行程处理）
- 整合大交通和小交通信息
- 生成完整的行程规划报告

```python
def synthesis_node(state: ItineraryState) -> ItineraryState:
    """整合报告智能体负责将所有信息整合成最终的行程规划"""
    # 计算时间轴
    train_departure_dt = datetime.strptime(f"{date} {departure_time}", "%Y-%m-%d %H:%M")
    train_arrival_dt = datetime.strptime(...)
    
    # 计算最晚出发时间和最终到达时间
    latest_departure_from_origin_dt = train_departure_dt - timedelta(hours=1) - dep_drive_duration
    final_arrival_at_destination_dt = train_arrival_dt + arr_drive_duration
    
    # 生成最终计划
    state["final_plan"] = {
        "title": f"从{departure_place}到{arrival_place}的行程规划",
        "timeline": {...},
        "transportation": {...},
        "map_data": {...},
        "tips": [...]
    }
    
    return state
```

#### 2.6 用户交互节点（Ask User Node）

负责处理需要用户澄清的情况：
- 生成明确的澄清问题
- 处理用户回应
- 更新状态以继续执行

```python
def ask_user_node(state: ItineraryState) -> ItineraryState:
    """用户交互节点负责处理需要用户澄清的情况"""
    # 检查是否有用户响应
    if state.get("user_clarification_response"):
        # 处理用户响应
        user_response = state["user_clarification_response"]
        
        # 根据响应更新状态
        if "出发城市" in state.get("clarification_question", ""):
            # 使用LLM从用户回应中提取城市信息
            parsed = json.loads(llm.invoke(parse_messages).content)
            state["departure_city"] = parsed["departure_city"]
        
        # 清除交互标记
        state["is_clarification_needed"] = False
    else:
        # 暂停等待用户响应
        state["is_clarification_needed"] = True
    
    return state
```

### 3. 路由系统

系统使用基于状态的条件路由，使工作流能够根据状态动态决定下一步执行的节点：

```python
def route_by_next_step(state: ItineraryState) -> str:
    """基于状态中的next_step字段决定下一个节点"""
    next_step = state.get("next_step", "planner")
    if next_step == "end":
        return "end"
    
    valid_nodes = ["planner", "major_transport", "local_transport", "web_search", "synthesis", "ask_user"]
    if next_step not in valid_nodes:
        return "planner"
    
    return next_step

def check_user_interaction_needed(state: ItineraryState) -> Literal["continue", "pause"]:
    """检查是否需要暂停工作流等待用户输入"""
    if state.get("is_clarification_needed", False):
        return "pause"
    return "continue"
```

### 4. 工作流图构建

使用LangGraph的StateGraph构建完整工作流，连接所有专家节点：

```python
def create_itinerary_graph() -> StateGraph:
    """创建行程规划工作流图谱"""
    # 创建状态图
    workflow = StateGraph(ItineraryState)
    
    # 添加所有节点
    workflow.add_node("planner", planner_node)
    workflow.add_node("major_transport", major_transport_node)
    workflow.add_node("local_transport", local_transport_node)
    workflow.add_node("web_search", web_search_node)
    workflow.add_node("synthesis", synthesis_node)
    workflow.add_node("ask_user", ask_user_node)
    
    # 设置入口点
    workflow.set_entry_point("planner")
    
    # 从规划师节点出发的条件路由
    workflow.add_conditional_edges(
        "planner",
        route_by_next_step,
        {
            "major_transport": "major_transport",
            "local_transport": "local_transport",
            "web_search": "web_search",
            "synthesis": "synthesis",
            "ask_user": "ask_user",
            "end": END
        }
    )
    
    # 所有专家节点都路由回规划师
    workflow.add_conditional_edges(
        "major_transport",
        check_user_interaction_needed,
        {
            "pause": "ask_user",
            "continue": "planner"
        }
    )
    
    # ... 其他节点的路由设置
    
    # 编译图谱
    return workflow.compile()
```

## 系统工作流程

### 1. 初始解析阶段

1. 用户提交查询请求
2. 规划师节点解析自然语言为结构化数据
3. 规划师确定下一步是查询大交通

### 2. 大交通规划阶段

1. 大交通专家接收城市信息
2. 调用火车/高铁搜索工具
3. 处理搜索结果并选择最佳方案
4. 提取车站信息并返回规划师

### 3. 小交通规划阶段

1. 规划师将控制权交给小交通专家
2. 小交通专家获取车站坐标
3. 规划两段驾车路线
4. 将路线信息返回规划师

### 4. 辅助信息获取阶段

1. 网络搜索专家获取天气信息
2. 生成交通提示
3. 返回辅助信息

### 5. 综合报告生成阶段

1. 综合报告智能体整合所有信息
2. 计算详细时间轴
3. 生成最终行程规划

### 6. 用户交互阶段（按需触发）

1. 当遇到信息不足或模糊情况
2. 用户交互节点生成澄清问题
3. 系统暂停等待用户响应
4. 处理用户回应并继续执行

## 错误处理与恢复策略

系统采用多层错误处理策略：

1. **节点内部错误处理**：每个节点内部捕获和处理异常
2. **状态错误记录**：将错误信息记录到状态中
3. **降级方案**：当API调用失败时使用备选方案
4. **用户澄清机制**：遇到无法自动解决的问题时请求用户澄清
5. **重试与回退**：支持操作重试和回退到上一状态

## 实现优势

1. **高度模块化**：每个专家独立工作，便于维护和扩展
2. **状态共享**：所有节点共享统一状态，确保信息一致性
3. **动态路由**：基于状态动态决定执行流程，灵活适应不同情况
4. **交互式澄清**：支持与用户的自然交互，提高规划准确性
5. **可视化与解释**：保存思考过程，提供透明的决策解释
6. **容错与恢复**：强大的错误处理机制，提高系统稳定性

## 性能与扩展性

- **并行处理**：支持未来实现节点并行执行
- **横向扩展**：可轻松添加新的专家节点
- **纵向扩展**：可增强现有节点的功能
- **状态持久化**：支持将状态持久化存储，实现长会话

## 使用方法

### 命令行界面

```bash
python main.py langgraph
```

### Web API

可通过网页界面的"多智能体模式"选项使用LangGraph系统：

```python
@app.post("/plan_langgraph")
async def plan_trip_langgraph(request: Request, ...):
    # 构建初始状态
    state = {
        "messages": [HumanMessage(content=query)],
        "original_query": query,
        ...
    }
    
    # 运行LangGraph工作流
    result = itinerary_app.invoke(state)
    
    # 处理结果
    return templates.TemplateResponse("plan.html", {
        "request": request, 
        "plan": result.get("final_plan", {}),
        "thinking_process": formatted_thinking
    })
```

## 开发者指南

### 添加新的专家节点

1. 在`src/agent/nodes.py`中定义新的节点函数
2. 在`src/agent/graph.py`中将节点添加到图谱
3. 更新路由逻辑以支持新节点

```python
# 1. 定义节点
def new_expert_node(state: ItineraryState) -> ItineraryState:
    # 实现节点逻辑
    return state

# 2. 添加到图谱
workflow.add_node("new_expert", new_expert_node)

# 3. 更新路由
workflow.add_conditional_edges(
    "planner",
    route_by_next_step,
    {
        # 添加新节点到路由选项
        "new_expert": "new_expert",
        # 其他节点...
    }
)
```

### 修改状态结构

1. 在`src/agent/state.py`中更新`ItineraryState`类型定义
2. 确保所有节点正确处理新的状态字段

## 未来增强方向

1. **工具并行调用**：实现并行调用多个工具，提高效率
2. **记忆增强**：增加长期记忆组件，改善用户体验
3. **多轮对话**：增强多轮对话能力，实现更自然的交互
4. **高级决策**：增加基于用户偏好的高级决策能力
5. **自适应路由**：根据历史执行效果动态调整路由策略
6. **异步执行模式**：实现异步工作流，支持长时间运行的任务

## 结语

LangGraph多智能体系统为智能行程规划提供了一个强大、灵活的框架。通过专家节点的协作和动态路由，系统能够处理复杂的行程规划任务，并在遇到问题时与用户进行交互式澄清。这种架构不仅提高了规划的准确性和完整性，还为未来功能扩展提供了坚实基础。 