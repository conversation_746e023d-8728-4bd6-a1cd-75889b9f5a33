# 智能行程规划助手项目说明书

## 项目概述

智能行程规划助手是一个基于Python和高德地图API的全栈应用，能够为用户提供精确、智能的出行路线规划。系统结合了大交通（高铁）和小交通（驾车）规划，为用户提供从出发地到目的地的全程出行方案，包括精确的时间节点、路线指引和可视化地图展示。

**【新增】** 系统采用先进的LangGraph多智能体协作架构，通过多个专业智能体（规划师、大交通专家、小交通专家等）的协同工作，实现更精确、更个性化的行程规划。

### 核心价值

- **一站式出行规划**：从家门到目的地的完整路线设计
- **精确时间节点**：提供每个行程环节的具体时间点和缓冲设计
- **可视化路线**：直观展示整个行程路线，包括小交通和大交通
- **灵活定位输入**：支持精确地点输入和自动定位功能
- **【新增】** **智能协作决策**：多专家智能体协同工作，提供全面考量的出行方案
- **【新增】** **交互式澄清**：在信息不足时主动与用户交互，获取必要信息

## 功能特性

### 1. 精确地点输入系统

- **浏览器自动定位**：用户可一键获取当前位置作为出发点
- **地址联想与补全**：基于高德API的输入提示功能，支持模糊输入
- **地理编码与逆编码**：双向转换经纬度坐标与文本地址
- **灵活修改**：自动填充后支持用户手动修改地址

### 2. 大交通规划（高铁/火车）

- **智能车站匹配**：自动匹配最合适的高铁站/火车站
- **最优路线推荐**：基于时间、距离等因素推荐最优高铁路线
- **车次、时刻表与票价**：展示详细车次信息、出发到达时间和票价

### 3. 小交通规划（驾车路线）

- **精确驾车导航**：提供从出发地到车站、车站到目的地的驾车导航
- **路线详情**：包括距离、预计时间、红绿灯数量等信息
- **分段导航指引**：提供详细的转弯、道路信息等精细化导航指引

### 4. 行程时间轴

- **关键时间点计算**：自动计算最晚出发时间、建议到站时间等
- **日期智能处理**：自动识别跨天行程，并在时间轴上明确标注
- **可视化时间轴**：直观展示整个行程的时间流程

### 5. 地图可视化

- **多段路线显示**：区分小交通和大交通路线，使用不同颜色标识
- **动态路径动画**：使用Ant-Path实现动态流动的路线效果
- **标记点区分**：使用不同图标标记出发地、目的地和车站位置

### 6. 智能推荐与建议

- **出行时间建议**：计算最佳出发时间，考虑交通和安全缓冲
- **天气信息集成**：展示出发地和目的地的天气情况
- **出行提醒**：关键提醒信息（如提前1小时到站）

### 7. 【新增】多智能体协作系统

- **规划师智能体**：中央控制节点，负责解析需求和分配任务
- **大交通专家**：专注于城市间高铁/火车路线规划
- **小交通专家**：专注于本地驾车路线规划
- **网络搜索专家**：负责获取天气、景点等辅助信息
- **综合报告智能体**：整合所有信息，生成完整行程计划
- **用户交互节点**：处理信息不足情况，与用户交互获取必要信息

## 技术栈

### 后端

- **编程语言**：Python 3.11+
- **Web框架**：FastAPI
- **服务器**：Uvicorn (ASGI服务器)
- **智能规划**：LangChain, LangGraph, ZhipuAI
- **环境变量**：python-dotenv
- **HTTP请求**：requests
- **模板引擎**：jinja2
- **时间处理**：Python datetime, timedelta

### 前端

- **基础技术**：HTML5, CSS3, 原生JavaScript (Vanilla JS)
- **CSS框架**：Tailwind CSS
- **图标库**：Font Awesome
- **地图库**：Leaflet.js
- **路径动画**：Leaflet-Ant-Path

### 第三方API集成

- **高德地图API**
  - **Web服务API**：服务端调用
    - 地理/逆地理编码API
    - POI搜索API
    - 驾车路线规划API
  - **JavaScript API**：客户端调用
    - 自动完成API
    - 地理编码API
    - 定位API

### 【新增】LangGraph多智能体框架

- **状态管理**：TypedDict实现的集中式状态管理
- **条件边**：基于状态的动态路由机制
- **工具节点**：与外部API和服务集成的专用节点
- **交互式澄清**：支持工作流暂停等待用户输入
- **错误处理**：多层错误处理和回退策略

## 项目结构

```
itinerary-plan/
├── main.py                  # 项目入口文件
├── requirements.txt         # 项目依赖
├── README.md                # 项目说明
├── 项目说明书.md              # 详细项目文档
├── .env                     # 环境变量配置（含API密钥）
├── assets/                  # 静态资源
│   └── images/              # 图片资源
├── docs/                    # 文档目录
│   └── LangGraph多智能体系统架构.md  # 多智能体架构文档
└── src/
    ├── agent/               # 智能代理模块
    │   ├── graph.py         # LangGraph工作流图谱
    │   ├── nodes.py         # 专家智能体节点定义
    │   ├── router.py        # 条件路由逻辑
    │   └── state.py         # 状态类型定义
    ├── clients/             # 外部客户端
    │   └── train_client.py  # 火车/高铁查询客户端
    ├── main_cli.py          # 命令行接口
    ├── main_cli_langgraph.py  # LangGraph命令行接口
    ├── services/            # 服务层
    │   └── train_service.py # 列车服务
    ├── tools/               # 工具模块
    │   ├── __init__.py
    │   ├── gaode_api_tool.py  # 高德地图API工具
    │   ├── search_tool.py   # 搜索工具
    │   └── train_search_tool.py # 列车搜索工具
    ├── utils/               # 工具函数
    │   └── map_utils.py     # 地图工具函数
    └── web/                 # Web应用
        ├── app.py           # FastAPI应用
        └── templates/       # HTML模板
            ├── error.html   # 错误页面
            ├── index.html   # 主页/输入页面
            └── plan.html    # 规划结果页面
```

## 安装与配置

### 环境要求

- Python 3.11+
- 高德地图API密钥（Web服务API密钥和JavaScript API密钥）
- 智谱AI API密钥（用于LLM调用）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/yourusername/itinerary-plan.git
   cd itinerary-plan
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   # Windows
   venv\Scripts\activate
   # Linux/MacOS
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   
   在项目根目录创建`.env`文件，添加以下内容：
   ```
   AMAP_API_KEY=your_amap_web_service_key
   AMAP_JS_API_KEY=your_amap_javascript_api_key
   AMAP_JS_SECRET=your_amap_javascript_security_code
   ZHIPUAI_API_KEY=your_zhipuai_api_key
   ```

### 启动应用

#### 标准模式

```bash
python main.py web
```

#### LangGraph多智能体模式

```bash
python main.py langgraph
```

应用将在 http://localhost:8000 启动。

## API集成详解

### 高德地图API

#### 1. 地理编码API

将地址转换为经纬度坐标。

```
GET https://restapi.amap.com/v3/geocode/geo
参数：
- key: API密钥
- address: 结构化地址信息
- city: 指定查询城市
```

#### 2. 逆地理编码API

将经纬度坐标转换为地址。

```
GET https://restapi.amap.com/v3/geocode/regeo
参数：
- key: API密钥
- location: 经度,纬度
```

#### 3. POI搜索API

搜索兴趣点（如火车站、高铁站）。

```
GET https://restapi.amap.com/v3/place/text
参数：
- key: API密钥
- keywords: 关键词
- city: 城市
- types: POI类型
```

#### 4. 驾车路线规划API

获取从起点到终点的驾车路线。

```
GET https://restapi.amap.com/v3/direction/driving
参数：
- key: API密钥
- origin: 起点经纬度
- destination: 终点经纬度
- extensions: all/base
- strategy: 路线规划策略
```

### JavaScript API

#### 1. 自动完成API

```javascript
// 初始化自动完成组件
var autoComplete = new AMap.AutoComplete({
    input: "input-element"
});

// 监听选择事件
AMap.event.addListener(autoComplete, "select", function(e) {
    // 处理选中的地点
});
```

#### 2. 地理编码API

```javascript
// 初始化地理编码组件
var geocoder = new AMap.Geocoder();

// 地址转经纬度
geocoder.getLocation(address, function(status, result) {
    if (status === 'complete' && result.info === 'OK') {
        // 获取经纬度
        var location = result.geocodes[0].location;
    }
});
```

#### 3. 浏览器定位API

```javascript
// 初始化定位组件
var geolocation = new AMap.Geolocation({
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 0,
    convert: true
});

// 获取当前位置
geolocation.getCurrentPosition();
AMap.event.addListener(geolocation, 'complete', onComplete);
AMap.event.addListener(geolocation, 'error', onError);
```

## 核心功能实现

### 1. 自动定位与地址获取流程

1. 用户点击定位按钮或聚焦输入框
2. 触发浏览器Geolocation API获取坐标
3. 调用高德地图逆地理编码API将坐标转换为地址
4. 自动填充到输入框并存储坐标信息

### 2. 地点输入与提示流程

1. 用户在输入框中输入文字
2. 触发高德地图输入提示API
3. 显示下拉提示列表
4. 用户选择后，获取该地点的经纬度坐标
5. 存储地点名称和坐标信息

### 3. 行程规划流程

1. 获取出发地和目的地的精确坐标
2. 调用逆地理编码API获取坐标所在城市名称
3. 基于城市名称查询车次信息
4. 调用POI搜索API查找城市内的高铁站/火车站
5. 为两段小交通规划驾车路线（出发地到出发站、到达站到目的地）
6. 计算关键时间节点（最晚出发时间、到站时间等）
7. 整合大交通和小交通信息，生成完整行程计划

### 4. 时间轴计算逻辑

```python
# 计算关键时间点
train_departure_dt = datetime.combine(date_obj, datetime.strptime(departure_time_str, "%H:%M").time())
train_arrival_dt = datetime.combine(date_obj, datetime.strptime(arrival_time_str, "%H:%M").time())

# 处理跨天情况
if train_arrival_dt < train_departure_dt:
    train_arrival_dt += timedelta(days=1)

# 计算小交通耗时
dep_drive_duration = timedelta(seconds=local_transport.get("departure", {}).get("raw_duration_seconds", 0))
arr_drive_duration = timedelta(seconds=local_transport.get("arrival", {}).get("raw_duration_seconds", 0))

# 计算时间点
arrival_at_station_dt = train_departure_dt - timedelta(hours=1)  # 提前1小时到站
latest_departure_from_origin_dt = arrival_at_station_dt - dep_drive_duration
final_arrival_at_destination_dt = train_arrival_dt + arr_drive_duration
```

### 5. 地图渲染逻辑

1. 前端接收来自后端的坐标和路线数据
2. 初始化Leaflet地图
3. 添加TileLayer作为地图底图
4. 添加出发地、目的地和车站标记点
5. 使用AntPath渲染三段路线（出发地→出发站、出发站→到达站、到达站→目的地）
6. 自动调整地图视野以包含所有标记点和路线

### 6. 【新增】多智能体协作流程

1. **用户请求解析**：规划师节点将自然语言查询解析为结构化数据
2. **专家分工协作**：
   - 大交通专家负责高铁/火车查询和选择
   - 小交通专家负责本地驾车路线规划
   - 网络搜索专家获取辅助信息
3. **动态路由决策**：基于状态动态决定下一步执行哪个专家节点
4. **交互式澄清**：当信息不足时，暂停工作流并向用户提问
5. **综合报告生成**：整合所有专家输出，生成完整行程规划
6. **错误恢复**：多层错误处理机制，确保系统稳定性

## 使用指南

### 规划新行程

1. 访问应用首页
2. 在"出发地"输入框：
   - 点击定位图标使用当前位置，或
   - 输入地址，并从下拉提示中选择
3. 在"目的地"输入框输入地址
4. 选择出行日期
5. 【新增】选择处理模式：
   - 标准模式：简单行程规划
   - 多智能体模式：更精确、全面的规划
6. 点击"规划行程"按钮
7. 系统会跳转至规划结果页面

### 查看规划结果

规划结果页面包含：

1. **行程时间轴**：显示整个行程的关键时间点
2. **详细行程**：包含各段行程的详细信息
   - 小交通信息：距离、时间、红绿灯数量
   - 大交通信息：车次、发车时间、票价
   - 驾车路线指引：可展开查看详细驾车导航步骤
3. **可视化地图**：显示整个行程路线
4. **【新增】智能思考过程**：在多智能体模式下，展示系统的分析和决策过程

### 查看驾车导航

1. 在小交通部分，点击"查看驾车路线指引"
2. 系统会展开显示详细的驾车导航步骤
3. 每步包含指令、距离和道路名称

## 开发者指南

### 新增城市支持

系统默认支持中国大陆城市。如需添加新城市，确保：

1. 高德地图API覆盖该区域
2. 火车/高铁数据中包含该城市信息

### 自定义地图样式

可在`plan.html`中修改Leaflet地图配置：

```javascript
// 更改底图
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// 自定义标记点图标
var customIcon = L.icon({
    iconUrl: 'path/to/icon.png',
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32]
});
```

### 扩展交通方式

如需增加更多交通方式（如公交、步行等），可参考以下步骤：

1. 在`app.py`中增加相应的路线规划函数
2. 调用高德相应的API端点
3. 在前端模板中添加对应的显示逻辑

### 【新增】扩展多智能体系统

1. **添加新专家节点**：
   ```python
   def new_expert_node(state: ItineraryState) -> ItineraryState:
       # 实现节点逻辑
       return state
   ```

2. **注册节点到图谱**：
   ```python
   workflow.add_node("new_expert", new_expert_node)
   ```

3. **更新路由逻辑**：
   ```python
   workflow.add_conditional_edges(
       "planner",
       route_by_next_step,
       {
           "new_expert": "new_expert",
           # 其他节点...
       }
   )
   ```

4. **修改状态结构**：在`state.py`中更新`ItineraryState`类型定义

## 错误处理与日志

系统对各种错误情况进行了处理：

1. **API调用失败**：使用备用方案，确保系统可用性
2. **坐标无效**：进行坐标验证，避免无效坐标导致系统崩溃
3. **计算错误**：为时间计算提供合理默认值
4. **详细日志**：系统记录详细日志于`web_app.log`，便于排查问题
5. **【新增】多层错误处理**：多智能体系统实现了多层次错误处理策略，包括节点内部错误捕获、状态错误记录、降级方案等

## 未来功能规划

- **多交通方式整合**：增加公交、地铁等更多小交通选择
- **行程保存与分享**：允许用户保存和分享行程计划
- **多日行程支持**：支持规划跨多天的复杂行程
- **酒店推荐**：基于目的地提供住宿推荐
- **实时路况**：整合实时交通数据，提供更准确的时间估计
- **历史行程分析**：基于用户历史行程提供个性化建议
- **【新增】并行工具调用**：实现多专家节点并行工作，提高效率
- **【新增】记忆增强**：增加长期记忆组件，改善用户体验
- **【新增】多轮对话**：实现更自然的多轮交互能力

## 结语

智能行程规划助手通过整合大交通和小交通，为用户提供从出发地到目的地的无缝规划体验。该系统结合了精确的地理定位、路线规划和时间计算，为用户的出行提供全方位的智能支持。

【新增】通过引入LangGraph多智能体架构，系统的智能化程度和规划准确性得到了显著提升。专家智能体的协作模式使系统能够更全面地考虑各方面因素，提供更优质的行程规划服务。

未来，我们将持续优化算法，扩展功能覆盖范围，提升用户体验，打造更加智能、便捷的出行助手。 